from django.db.models import Count
from settings.performance_tests import BUSINESSES_FOR_TESTS
from service.search.search_engine import SearchEngine, SearchKwargs
from service.search.serializers import BusinessSearchSerializer
from webapps.business.models import Business
from webapps.business.searchables.business import BusinessSearchable
from webapps.business.searchables.serializers.business import BusinessHitSerializer


def check_perf_test_businesses(business_ids_list: list = None) -> bool:
    """Checks if the business search function can find ALL business_ids in the list.
    If no list is passed, a list is generated according to Perf Test settings.
    Intended to check if businesses with modified session keys are available in search"""

    if not business_ids_list:
        business_ids_list = list(
            Business.objects.filter(active=True, visible=True)
            .annotate(count=Count('appointments'))
            .order_by('-count')[:BUSINESSES_FOR_TESTS]
            .values_list('id', flat=True)
        )

    search_kwargs = SearchKwargs(1, 20, BusinessHitSerializer())
    data_in = {'business_ids': business_ids_list}
    data = BusinessSearchSerializer(data=data_in)
    data.is_valid()
    data = data.validated_data
    searchable = BusinessSearchable
    search_results = SearchEngine.search(
        data=data, search_kwargs=search_kwargs, searchable=searchable
    )
    return len(business_ids_list) == search_results.businesses.hits.total.value
