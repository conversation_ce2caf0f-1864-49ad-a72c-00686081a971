#!/usr/bin/env python
import os
import re
from importlib import import_module

import django
from mock import patch

django.setup()  # NOQA
# pylint: disable=wrong-import-position
from django.conf import settings
from webapps.script_runner.enums import ScriptType
from webapps.script_runner.management.commands.execute_scripts import (
    Command,
    EXCLUDED_NAMES,
    SCRIPT_REGEX,
)
from webapps.script_runner.mixins import DependentScript, ReusableScript
from webapps.script_runner.runners import AbstractScriptRunner

# pylint: enable=wrong-import-position
# pylint: disable=broad-exception-raised


def validate_dependencies(script_obj, script, scripts):
    dependencies = getattr(script_obj, 'dependencies', None)
    if not dependencies or not isinstance(dependencies, list):
        raise RuntimeError(
            f'Script {script} must have an attribute `dependencies` which'
            'should be a list of dependencies, because it inherits from '
            '`DependentScript`.'
        )
    scripts = set(scripts)
    for dependency in dependencies:
        if dependency not in scripts:
            raise RuntimeError(f'Script {script} declared invalid dependency: {dependency}')


def validate_imported_script(script_obj, script, scripts):
    if not isinstance(script_obj, AbstractScriptRunner):
        raise RuntimeError(
            f'Script {script} must inherit from AbstractScriptRunner or other inherited class.'
        )
    if isinstance(script_obj, DependentScript):
        validate_dependencies(script_obj, script, scripts)

    if isinstance(script_obj, ReusableScript):
        version = getattr(script_obj, 'version', None)
        if not version or not isinstance(version, int):
            raise RuntimeError(
                f'Invalid version attribute for script {script}, version must be int.'
            )


def validate_dependency_graph(scripts):
    # check dependency graph for circular dependencies
    # patch ExecutedScript to build full graph
    with patch(
        'webapps.script_runner.management.commands.execute_scripts.'
        'ExecutedScript.objects.values_list',
        return_value=[],
    ):
        # will raise CircularDependencyError
        Command.get_scripts_dependencies(scripts, ScriptType.SCRIPT_RUNNER)


def main():
    relative_path = ('webapps', 'script_runner', 'scripts')
    scripts_path = os.path.join(settings.PROJECT_PATH, *relative_path)
    scripts = set(os.listdir(scripts_path))
    scripts = scripts - EXCLUDED_NAMES

    script_regex = re.compile(SCRIPT_REGEX)
    scripts_with_errors = []
    scripts_to_import = []
    for script in scripts:
        match = script_regex.fullmatch(script)
        if not match:
            scripts_with_errors.append(script)
        else:
            scripts_to_import.append(script)
    if scripts_with_errors:
        raise RuntimeError(f'Invalid name for scripts: {scripts_with_errors}')
    # Try to import main class
    module_path = '.'.join(relative_path)
    for script in scripts_to_import:
        script_name = script.replace('.py', '')
        script_module = f'{module_path}.{script_name}'
        try:
            module = import_module(script_module)
            script_obj = module.Script()
            validate_imported_script(script_obj, script, scripts_to_import)
        except Exception as e:
            raise RuntimeError(f'Failed to import {script_module}; {repr(e)}') from e

    validate_dependency_graph(scripts)


if __name__ == '__main__':
    main()
