id: BusinessDetails
description: detailed info about a Business
required:
  - id
  - name
  - slug
  - subdomain
  - description
  - phone
  - website
  - facebook_link
  - instagram_link
  - public_email
  - booking_mode
  - booking_policy
  - reviews_rank
  - reviews_count
  - reviews_stars
  - images
  - cover_photo
  - credit_cards
  - parking
  - wheelchair_access
  - pricing_level
  - promoted
  - show_similar_gallery
  - location
  - open_hours
  - service_categories
  - timezone_name
  - regions
  - business_categories
  - staff
  - contractors
  - is_recommended
  - max_discount_rate
  - amenities
  - printer_config
properties:
    id:
        type: integer
        description: ID of the Business
    name:
        type: string
        description: name of the Business
    slug:
        type: string
        description: slug of Business name
    subdomain:
        type: string
        description: subdomain of the Business
    description:
        type: string
        description: description of the Business
    phone:
        type: string
        description: phone number of the Business
    website:
        type: string
        description: URL of Business' web page
    facebook_link:
        type: string
        description: URL of Business' web page
    instagram_link:
        type: string
        description: URL of Business' web page
    public_email:
        type: string
        description: Business' public email
    booking_mode:
        type: string
        description:
            indicates if booking is made automatically (highly recommended) or manually
        enum_from_const: business.Business.BookingMode
    booking_policy:
        type: string
        description: information text about booking policy
    booking_max_lead_months_count:
        type: number
        description: max lead booking months count
    reviews_rank:
        type: number
        maximum: 5
        minimum: 1
        description: average rank of Business reviews (can be null)
    reviews_count:
        type: integer
        description: number of Business reviews (can be null)
    reviews_stars:
        type: integer
        maximum: 5
        minimum: 0
        description: number of review rank stars of Business
    images:
        type: BusinessImages
        description: a container for business images
    cover_photo:
        type: string
        description: URL to Business' cover photo
    credit_cards:
        type: string
        description: information text about accepted credit cards
    parking:
        type: string
        description: information text about available parking space
    wheelchair_access:
        type: string
        description: information text about wheelchair access
    pricing_level:
        type: string
        description: pricing level of Business (can be null)
    promoted:
        type: boolean
        description: is Business promoted?
    show_similar_gallery:
        type: boolean
        description:
            should similar businesses be shown on business details page
    location:
        type: BusinessDetailsLocation
        description: Business location data
    open_hours:
        type: array
        items:
            type: LegacyHours
        description: DEPRECATED - use business_opening_hours
    business_opening_hours:
        type: array
        items:
            type: WeekdayHours
        description: List of opening hours for days of week
    open_hours_customizations:
        type: array
        items:
            type: OpenHoursCustomization
    service_categories:
        type: array
        items:
            type: ServiceCategory
        description: list of ServiceCategories of the Business
    timezone_name:
        type: string
        description: timezone of the Business
    regions:
        type: array
        items:
            type: RegionSimple
        description: Regions Business belongs to
    business_categories:
      type: array
      items:
        type: IdAndNameAndSlugAndInternalName
      description: BusienssCategories Business belongs to
    primary_category:
      type: integer
      description: ID of primary category (one of chosen ones by business)
    staff:
      type: array
      items:
        type: ResourceSimple
      description: business staff
    opening_hours_note:
      type: string
      description: BusinessDetailHandler opening ours input
    noindex:
        type: boolean
        description:
            should Business be indexed in search engines
    deposit_policy:
        type: string
        description: deposit policy text
    deposit_cancel_time:
        type: TimeDelta
        description: >
            canceling deposit within this time from the visit will
            automatically release it.
    contractors:
        type: array
        items:
          type: integer
        description: If business is umbrella venue return all contractors ids
    umbrella_venue_name:
        type: string
        description: >
          If business is contractor of some UmbrellaVenue.
          It will have name of this venue.
    renting_venue_id:
        type: integer
        description: Venue business id
    contractor_description:
        type: string
        description: >
          If business is contractor of some UmbrellaVenue.
          It will have contractor description
    distance:
      type: number
      description: >
        optional(Distance of Business from given location in kilometers.)
    top_services:
        type: array
        items:
          type: Service
        description: >
            List of three top booked services of business.
            Gender specific if gender was provided.
    is_recommended:
        type: boolean
        description: >
          Show 'Booksy recommend' badge if flag is set
    max_discount_rate:
        type: integer
        description: >
          Show maximal percentage discount of business' service in next 3 days
    traveling:
        type: TravelingToClients
        description: traveling to clients details
    amenities:
        type: array
        description: List of amenities provided by business
        items:
            type: Amenity
    printer_config:
        type: boolean
        description:
            Business has fiscal printer configured (printer_api)
    simplified_booking_feature_checklist:
        type: SimplifiedBookingFeatureChecklist
        description: Feature checklist required for Simplified Booking targeting
