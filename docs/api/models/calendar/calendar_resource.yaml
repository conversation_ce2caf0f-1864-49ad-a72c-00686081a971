id: CalendarResource
required:
  - id
  - name
  - type
  - description
  - staff_user_exists
  - is_current_user
  - working_hours
  - bookings
  - reservations
  - time_offs
  - visible_on_calendar
properties:
    id:
        type: integer
    name:
        type: string
    type:
        type: string
        enum_from_const: business.Resource.RESOURCE_TYPES
    description:
        type: string
    staff_user_exists:
        type: boolean
    is_current_user:
        type: boolean
    working_hours:
        type: object
        items:
            type: HoursRange
    bookings:
        type: array
    reservations:
        type: array
    time_offs:
        type: array
    visible_on_calendar:
        type: boolean
