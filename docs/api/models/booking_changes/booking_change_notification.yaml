id: BookingChangeNotification
required:
  - type
  - sender
  - change_datetime
  - title
properties:
    change_datetime:
        type: string
        description: |
            When message has been sent.
            Already in locale based format.
    id:
        type: integer
        description: NotificationHistory.id
    type:
        type: string
        enum_from_const: notification.UserNotification.NOTIFICATION_TYPES
    sender:
        type: string
        enum_from_const: notification.NotificationHistory.SENDER_TYPES
    title:
        type: string
        description: |
            Text of Push, SMS or title of Email
    meta_to:
        type: string
        description: |
            Phone number if `type=S` or email address if `type=E`, otherwise omitted
            Using this may have weird behaviors, like ignoring BCC.
