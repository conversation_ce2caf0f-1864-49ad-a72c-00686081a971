id: PaymentType
description: Payment type Details
required:
    - order
    - code
    - label
    - default
    - pay_by_app

properties:
    id:
        type: integer
        description: Payment Type ID
    order:
        type: integer
        description: order in the list of payment_types
    code:
        type: string
        description: human readable form of Payment Type codename
        enum_from_const: webapps.pos.enums.PaymentTypeEnum
    label:
        type: string
        description: Payment Label
    default:
        type: boolean
        description: is default
    enabled:
        type: boolean
        description: set selected PaymentType as visible(enabled) or not
    selected:
        type: boolean
        description: selected payment type (in transaction payment_type_choices only)
