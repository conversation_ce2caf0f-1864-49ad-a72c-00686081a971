id: ReviewRatingsResponseListWithPagination
description: |
    List of Reviews with pagination info
required:
    - reviews
    - reviews_count
    - reviews_rank
    - reviews_page
    - reviews_per_page
    - num_reviews_per_rank
properties:
    reviews:
        description: List of Review on current page
        type: array
        items:
            type: ReviewResponse
    reviews_count:
        type: integer
        description: total number of reviews which can be shown
    reviews_rank:
        type: number
        maximum: 5
        minimum: 1
        description: average rank of all reviews (null when count==0)
    reviews_stars:
        type: integer
        maximum: 5
        minimum: 0
        description: stars based on of all reviews (0 when count==0)
    reviews_page:
        type: integer
        description: current page number (starts with 1)
    reviews_per_page:
        type: integer
        description: number of reviews per page
    num_reviews_per_rank:
        type: ReviewPerRankResponse
        description: Number of reviews per rank, without review from blacklisted users
