id: ConfigCurrency
description:
required:
  - code
  - decimal_length
  - decimal_separator
  - group_separator
  - negative_sign
  - precedes
  - space
  - symbol
properties:
    code:
        type: string
        description:
    decimal_length:
        type: integer
        description: |
            More about:
             - http://www.londonfx.co.uk/ccylist.html
             - http://en.wikipedia.org/wiki/ISO_4217
            Real currencies have at most 3 (like Tunisian dinar)
        minimum: 0
        maximum: 3
    decimal_separator:
        type: string
        description:
    group_separator:
        type: string
        description:
    negative_sign:
        type: string
        description:
    precedes:
        type: boolean
        description: |
            if symbol is before number 
             - True => "$12.34"
             - False => "12.34zł"
    space:
        type: boolean
        description: |
            space between symbol and value
             - True => "12.34 zł"
             - False => "$12.34"
    symbol:
        type: string
        description:
