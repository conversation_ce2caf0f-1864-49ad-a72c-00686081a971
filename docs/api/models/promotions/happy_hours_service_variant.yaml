id: HappyHoursServiceVariant
description: ServiceVariant Happy Hour definition
required:
  - discount_type
  - discount_amount
  - service_variant_id
  - hour_from
  - hour_till
properties:
  discount_type:
    type: string
    description: discount type (amount or percentage)
    enum_from_const: webapps.business.enums.DiscountType
  discount_amount:
    type: string
    format: decimal
    description: amount of currency or percentage discount
  service_variant_id:
    type: integer
    description: id of ServiceVariant
  hour_from:
    type: string
    format: time
    description: booking hour from
  hour_till:
    type: string
    format: time
    description: bookig hour to