id: Recaptcha
description: Recap<PERSON><PERSON> flags
required:
    - sms_registration
    - login
    - password_reset
    - email_change
    - account_exists
    - jwt_password_reset
properties:
    sms_registration:
        type: boolean
        description: Recaptcha on sms registration endpoint
    login:
        type: boolean
        description: Recaptcha on login endpoint
    password_reset:
        type: boolean
        description: <PERSON><PERSON><PERSON><PERSON> on password reset endpoint
    email_change:
        type: boolean
        description: <PERSON><PERSON><PERSON><PERSON> on email change endpoint
    account_exists:
        type: boolean
        description: Recaptcha on account exists endpoint
    jwt_password_reset:
        type: boolean
        description: Recaptcha on jwt password reset endpoint
