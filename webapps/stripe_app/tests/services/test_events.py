from unittest.mock import MagicMock, call, patch

from django.test import TestCase

from webapps.stripe_app.enums import StripeEventType
from webapps.stripe_app.services.charge import PaymentIntentResult, PaymentIntentService
from webapps.stripe_app.services.customer import CustomerResult, CustomerService
from webapps.stripe_app.services.events import EventHandlerService, EventProcessService
from webapps.stripe_app.services.payment_method import PaymentMethodResult, PaymentMethodService
from webapps.stripe_app.services.refund import RefundResult, RefundService
from webapps.stripe_app.tests.utils import (
    stripe_api_charge,
    stripe_api_customer,
    stripe_api_payment_intent,
    stripe_api_payment_method,
    stripe_api_refund,
)


class TestEventHandlerService(TestCase):
    @patch.object(
        PaymentIntentService,
        'handle_data_from_webhook',
        MagicMock(return_value=PaymentIntentResult(is_success=True)),
    )
    def test_process_payment_intent(self):
        payment_intent = stripe_api_payment_intent(id='pi_123')
        result = EventProcessService.process_payment_intent(
            stripe_object=payment_intent,
            event_type=StripeEventType.PAYMENT_INTENT_SUCCEEDED.value,
        )
        self.assertTrue(result.is_success)

    @patch.object(
        PaymentMethodService,
        'handle_payment_method_attached_data_from_webhook',
        MagicMock(return_value=PaymentMethodResult(is_success=True)),
    )
    def test_process_payment_method_attached(self):
        payment_method = stripe_api_payment_method(id='pi_123')
        result = EventProcessService.process_payment_method_attached(
            stripe_object=payment_method,
            event_type=StripeEventType.PAYMENT_METHOD_ATTACHED.value,
        )
        self.assertTrue(result.is_success)

    @patch.object(
        PaymentMethodService,
        'handle_payment_method_updated_data_from_webhook',
        MagicMock(return_value=PaymentMethodResult(is_success=True)),
    )
    def test_process_payment_method_update(self):
        payment_method = stripe_api_payment_method(id='pi_123')
        result = EventProcessService.process_payment_method_update(
            stripe_object=payment_method,
            event_type=StripeEventType.PAYMENT_METHOD_AUTO_UPDATE.value,
        )
        self.assertTrue(result.is_success)

    @patch.object(
        CustomerService,
        'handle_data_from_webhook',
        MagicMock(return_value=CustomerResult(is_success=True)),
    )
    def test_process_customer(self):
        customer = stripe_api_customer(id='cu_123')
        result = EventProcessService.process_customer(
            stripe_object=customer,
            event_type=StripeEventType.CUSTOMER_CREATED.value,
        )
        self.assertTrue(result.is_success)

    @patch.object(
        RefundService,
        'handle_charge_refunded_data_from_webhook',
        MagicMock(return_value=RefundResult(is_success=True)),
    )
    def test_process_charge_refunded(self):
        charge = stripe_api_charge(id='ch_123')
        result = EventProcessService.process_charge_refunded(
            stripe_object=charge,
            event_type=StripeEventType.CHARGE_REFUNDED.value,
        )
        self.assertTrue(result.is_success)

    @patch.object(
        RefundService,
        'handle_charge_refund_update_data_from_webhook',
        MagicMock(return_value=RefundResult(is_success=True)),
    )
    def test_process_charge_refund_updated(self):
        refund = stripe_api_refund(id='ch_123', metadata={'suscriber': 'billing'})
        result = EventProcessService.process_charge_refund_updated(
            stripe_object=refund,
            event_type=StripeEventType.CHARGE_REFUND_UPDATED.value,
        )
        self.assertTrue(result.is_success)


class TestEventProcessService(TestCase):
    @patch('webapps.stripe_app.services.events.event_handler_task')
    def test_handle_event(self, task_mock):
        event_object = stripe_api_payment_intent(id='pi_123')
        result = EventHandlerService.handle_event(event_object=event_object, abc=123)
        self.assertEqual(
            task_mock.apply_async.call_args,
            call(kwargs={'event_data': {'id': 'py_123'}, 'abc': 123}, countdown=45),
        )
        self.assertIn('task_id', result)
