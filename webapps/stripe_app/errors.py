from dataclasses import dataclass, field

import stripe
import stripe.error
from dataclasses_json import DataClassJsonMixin

from webapps.stripe_app.enums import ErrorType


__all__ = [
    'ErrorObject',
    'parse_stripe_error',
]


@dataclass(frozen=True)
class ErrorObject(DataClassJsonMixin):  # pylint: disable=too-many-instance-attributes
    type: ErrorType | None = field(default=None)
    code: str | None = field(default=None)
    decline_code: str | None = field(default=None)
    doc_url: str | None = field(default=None)
    message: str | None = field(default=None)
    param: str | None = field(default=None)
    payment_intent: stripe.api_resources.payment_intent.PaymentIntent | None = field(default=None)
    payment_method: stripe.api_resources.payment_method.PaymentMethod | None = field(default=None)
    setup_intent: stripe.api_resources.setup_intent.SetupIntent | None = field(default=None)
    source: stripe.api_resources.source.Source | None = field(default=None)
    charge: stripe.api_resources.charge.Charge | None = field(default=None)


def parse_stripe_error(stripe_exc: stripe.error.StripeError) -> ErrorObject:
    if not (stripe_error := stripe_exc.error):
        return ErrorObject(
            message=repr(stripe_exc),
            code=str(stripe_exc.code),
            type=ErrorType.OTHER_STRIPE_ERROR,
        )

    stripe_error = dict(stripe_error)

    dataclass_kwargs = {}
    for field_key in ErrorObject.__dataclass_fields__:
        dataclass_kwargs[str(field_key)] = stripe_error.get(str(field_key))
    return ErrorObject(**dataclass_kwargs)
