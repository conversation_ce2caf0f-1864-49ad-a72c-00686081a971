# Generated by Django 4.0.2 on 2022-03-16 11:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stripe_app', '0005_alter_charge_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='charge',
            name='subscriber_key',
            field=models.CharField(blank=True, db_index=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='subscriber_key',
            field=models.CharField(blank=True, db_index=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='paymentintent',
            name='subscriber_key',
            field=models.CharField(blank=True, db_index=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='subscriber_key',
            field=models.CharField(blank=True, db_index=True, max_length=120, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='refund',
            name='subscriber_key',
            field=models.Char<PERSON><PERSON>(blank=True, db_index=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='setupintent',
            name='subscriber_key',
            field=models.CharField(blank=True, db_index=True, max_length=120, null=True),
        ),
    ]
