from django.test import TestCase
from model_bakery import baker
from parameterized import parameterized

from lib.tools import tznow
from webapps.business.baker_recipes import (
    business_recipe,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.voucher.management.commands.fill_sold_vouchers_before_migration_threshold import (
    Command,
)
from webapps.voucher.models import (
    Voucher,
    VoucherAdditionalInfo,
    VoucherTemplate,
)


class TestFillSoldVouchersBeforeMigrationThreshold(TestCase):
    def setUp(self):
        super().setUp()
        business = business_recipe.make()
        self.pos = pos_recipe.make(business=business)

    @parameterized.expand(
        [
            (Voucher.VOUCHER_TYPE__MEMBERSHIP, False),
            (Voucher.VOUCHER_TYPE__PACKAGE, False),
            (Voucher.VOUCHER_TYPE__EGIFT_CARD, True),
        ]
    )
    def test_fill_sold_vouchers_before_migration_threshold(
        self,
        voucher_type,
        sold_vouchers_before_migration_threshold_value,
    ):
        baker.make(VoucherTemplate, pos=self.pos, type=voucher_type)
        baker.make(VoucherAdditionalInfo, pos=self.pos)

        Command().handle()
        self.pos.refresh_from_db()

        assert (
            self.pos.voucher_additional_info.sold_vouchers_before_migration_threshold
            is sold_vouchers_before_migration_threshold_value
        )

    def test_fill_sold_vouchers_before_migration_threshold_no_additional_info_gift_card(self):
        baker.make(VoucherTemplate, pos=self.pos, type=Voucher.VOUCHER_TYPE__EGIFT_CARD)

        assert not VoucherAdditionalInfo.objects.filter(pos_id=self.pos.id).exists()

        Command().handle()
        self.pos.refresh_from_db()

        assert self.pos.voucher_additional_info.sold_vouchers_before_migration_threshold is True

    @parameterized.expand(
        [
            (Voucher.VOUCHER_TYPE__MEMBERSHIP,),
            (Voucher.VOUCHER_TYPE__PACKAGE,),
        ]
    )
    def test_fill_sold_vouchers_before_migration_threshold_no_additional_info_membership_package(
        self,
        voucher_type,
    ):
        baker.make(VoucherTemplate, pos=self.pos, type=voucher_type)

        Command().handle()
        self.pos.refresh_from_db()

        assert not VoucherAdditionalInfo.objects.filter(pos_id=self.pos.id).exists()

    @parameterized.expand(
        [
            (Voucher.VOUCHER_TYPE__MEMBERSHIP, False),
            (Voucher.VOUCHER_TYPE__PACKAGE, False),
            (Voucher.VOUCHER_TYPE__EGIFT_CARD, True),
        ]
    )
    def test_fill_sold_vouchers_before_migration_threshold_deleted_template(
        self,
        voucher_type,
        sold_vouchers_before_migration_threshold_value,
    ):
        baker.make(VoucherTemplate, pos=self.pos, type=voucher_type, deleted=tznow())
        baker.make(VoucherAdditionalInfo, pos=self.pos)

        Command().handle()
        self.pos.refresh_from_db()

        assert (
            self.pos.voucher_additional_info.sold_vouchers_before_migration_threshold
            is sold_vouchers_before_migration_threshold_value
        )

    def test_fill_sold_vouchers_before_migration_threshold_no_templates(self):
        Command().handle()
        self.pos.refresh_from_db()

        assert not VoucherAdditionalInfo.objects.filter(pos_id=self.pos.id)
