from webapps.market_pay import transfer
from webapps.market_pay.models import AccountHolder


def get_balances(business_id):
    """Acctual balances of business

    :param business_id: Business ID
    :return: dict with values of the balances
    """
    account_holder = AccountHolder.objects.filter(pos__business_id=business_id).first()
    if not account_holder:
        return None

    return transfer.get_balances(
        account_holder.account_holder_code,
        account_holder.account_code,
    )
