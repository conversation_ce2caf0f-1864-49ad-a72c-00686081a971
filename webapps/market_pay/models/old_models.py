# -*- coding: utf-8 -*-
from __future__ import annotations

import datetime
from collections import namedtuple
from decimal import Decimal
from difflib import Differ
from typing import Optional, <PERSON>ple

import simplejson as json
from dateutil.utils import today
from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import connection, models, transaction
from django.db.models import <PERSON><PERSON><PERSON>ield
from django.utils.functional import cached_property
from rest_framework import status

from lib.models import (
    ArchiveManager,
    ArchiveModel,
    AutoUpdateQuerySet,
    BaseArchiveManager,
    SoftDeleteManager,
    UndeletableMixin,
)
from lib.tools import (
    create_uuid,
    format_currency,
    id_to_external_api,
    minor_unit,
    tznow,
    list_flatten,
)
from service.exceptions import ServiceError
from webapps.adyen.helpers import make_request
from webapps.adyen.models import Capture, Notification
from webapps.b2b_referral.models import B2BReferralReward
from webapps.business.models import Business
from webapps.market_pay import consts, enums, requests
from webapps.market_pay.exceptions import (
    Not<PERSON><PERSON><PERSON><PERSON>alance,
    WrongAccountHolderStatus,
)
from webapps.market_pay.requests import (
    AccountHolderStateType,
    account_holder_balance,
    unsuspend_account_holder,
)
from webapps.market_pay.typing import (
    AccountHolderBalance,
    AccountHolderTransactionListArgs,
    AmountRequestArg,
    CaptureReferencesPerTransactionStatus,
    TransactionListPerAccount,
)
from webapps.pos.models import POS

# pylint: disable=abstract-method

CURRENCY_FACTOR = 10 ** settings.COUNTRY_CONFIG.currency_data['frac_digits']


class AccountHolder(UndeletableMixin, ArchiveModel):
    class Meta:
        # for admin page
        verbose_name_plural = 'KYC Status'

    id = models.AutoField(primary_key=True, db_column='accountholder_id')
    pos = models.ForeignKey(
        POS,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='account_holders',
    )
    account_holder_code = models.CharField(max_length=64, unique=True)
    account_code = models.CharField(max_length=64)
    status = models.CharField(max_length=12, blank=True)
    payout_allowed = models.BooleanField(default=False)
    manual_payout_allowed = models.BooleanField(default=False)
    ever_passed_kyc = models.BooleanField(default=False)
    data = JSONField(default=dict)

    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'{self.id}, {self.account_holder_code}'

    @property
    def payout_schedule(self) -> Optional[enums.PayoutSchedule]:
        payout_schedule = self.data.get('payout_schedule')
        return enums.PayoutSchedule(payout_schedule) if payout_schedule else None

    def set_payout_schedule(self, schedule):
        self.data['payout_schedule'] = schedule
        self.save()

    @property
    def manual_payouts_enabled(self) -> bool:
        """
        Manual payouts are requested by Booksy and can only be performed
        if the account holder is KYC verified (has `payout_allowed`) and has
        the current payout schedule set to HOLD (so that Adyen does not perform
        the payout automatically). Additionally the account holder must have
        `manual_payout_allowed` flag turned on since there may be some
        situations where the CS team wants to hold payouts for fraud-detection
        reasons.
        """
        return (
            self.payout_allowed
            and self.payout_schedule == enums.PayoutSchedule.HOLD
            and self.manual_payout_allowed
        )

    @property
    def needs_kyc_notification(self) -> bool:
        """
        Denotes if the account holder needs to be informed about KYC-related
        problem.
        """
        from webapps.market_pay.serializers import ModelAccountHolderVerificationSerializer

        serializer = ModelAccountHolderVerificationSerializer(self.data)
        return bool(serializer.data.get('verification_errors'))

    def refresh_from_adyen(self, operator: Optional['user.User'] = None):
        from webapps.market_pay.serializers import ModelAccountHolderSerializer
        from webapps.market_pay.requests import get_account_holder

        instance = get_account_holder(self.account_holder_code)
        return ModelAccountHolderSerializer(
            instance,
            context={'account_holder': self, 'operator': operator},
        ).save()

    def update_payout_state_type(
        self,
        disable: bool,
        reason: Optional[str] = None,
        operator: Optional['user.User'] = None,
    ) -> None:
        """
        Sets new account holder's payout status (enables or disables it).
        Remember to provide `reason` when disabling payouts.
        """
        from webapps.market_pay.requests import update_account_holder_state

        if disable and not reason:
            raise ValueError('Cannot set empty `reason` when disabling payouts')

        response = update_account_holder_state(
            account_holder_code=self.account_holder_code,
            disable=disable,
            reason=reason,
            state_type=AccountHolderStateType.PAYOUT,
        )
        if 'invalidFields' not in response:
            self.refresh_from_adyen(operator=operator)

    def unsuspend_account(self, operator: Optional['user.User'] = None) -> None:
        response = unsuspend_account_holder(self.account_holder_code)
        if not response.get('invalidFields'):
            self.refresh_from_adyen(operator=operator)

    def log_changes(self, operator_id=None):
        """
        Creates AccountHolderChangeLog object to track changes in AccountHolder.
        Data field contains all needed information.
        """
        AccountHolderChangeLog.objects.create(
            account_holder=self,
            operator_id=operator_id,
            data=json.dumps(self.data, sort_keys=True),
        )

    def get_balance(
        self,
        currency_code=settings.CURRENCY_CODE,
    ) -> Optional[AccountHolderBalance]:
        """
        Returns detailed balance status of the account holder account for the
        provided currency.
        The `None` value is returned in case of communication errors.

        The returned balance data contains three fields that correspond to
        different statuses:
        * balance - money already on account holder's Adyen balance, meaning
                    that the bank transfer has been finished
        * on_hold_balance - money that has been requested to move from the
                    account, but the transfer has yet to be marked as "done"
        * pending_balance - money that was sent to the account holder, but has
                    not been marked as "settled" yet
        """
        balance = AccountHolderBalance(
            balance=0,
            on_hold_balance=0,
            pending_balance=0,
        )
        try:
            result = account_holder_balance(self.account_holder_code)
        except ServiceError:
            balance = None
        else:
            account_balances = [
                account
                for account in result['balancePerAccount']
                if account['accountCode'] == self.account_code
            ]
            for account in account_balances:
                balance.balance += sum(
                    currency_balance['value']
                    for currency_balance in account['detailBalance'].get('balance', [])
                    if currency_balance['currency'] == currency_code
                )
                balance.on_hold_balance += sum(
                    currency_balance['value']
                    for currency_balance in account['detailBalance'].get('onHoldBalance', [])
                    if currency_balance['currency'] == currency_code
                )
                balance.pending_balance += sum(
                    currency_balance['value']
                    for currency_balance in account['detailBalance'].get('pendingBalance', [])
                    if currency_balance['currency'] == currency_code
                )

        return balance

    @staticmethod
    def _get_payment_refs_based_on_adyen_psp(payment_refs, refund_refs, chargeback_refs):
        # Get list of Capture references that are related to retrieved
        # transactions list
        capture_payment_references = set(
            Capture.objects.filter(
                auth__psp_reference__in=payment_refs,
            ).values_list('reference', flat=True)
        )
        capture_refund_references = set(
            Capture.objects.filter(
                auth__psp_reference__in=refund_refs,
            ).values_list('reference', flat=True)
        )

        # Chargebacks include pspReference to the Notification object, thus
        # requiring us to further retrieve `originalReference` to Auth object
        chargeback_auth_refs = [
            notification.body.get('originalReference')
            for notification in Notification.objects.filter(
                psp_reference__in=chargeback_refs,
                handled_successfully=True,
            )
        ]
        capture_chargeback_references = set(
            Capture.objects.filter(
                auth__psp_reference__in=chargeback_auth_refs,
            ).values_list('reference', flat=True)
        )

        return capture_payment_references, capture_refund_references, capture_chargeback_references

    def retrieve_balance_capture_references(self) -> CaptureReferencesPerTransactionStatus:
        """
        Returns list of `Capture` pnrefs that are in account holder's current
        balance (both credited and pending).
        """

        statuses = [
            'PendingCredit',
            'PendingDebit',
        ]

        prev_payment_refs, prev_refund_refs, prev_chargeback_refs = [], [], []
        # Calculate references that had not been already included in
        # previous manual payouts (excluding failed payouts)
        previous_manual_payouts = ManualPendingFundsPayout.objects.filter(
            account_holder=self,
        ).exclude(
            successful=False,
        )
        for manual_payout in previous_manual_payouts:
            prev_payment_refs.extend(manual_payout.payment_refs)
            prev_refund_refs.extend(manual_payout.refund_refs)
            prev_chargeback_refs.extend(manual_payout.chargeback_refs)

        payment_refs, refund_refs, chargeback_refs = self._get_transactions_refs_from_adyen(
            statuses
        )
        statuses = [
            'Credited',
            'Debited',
            'Chargeback',
            'ChargebackReversed',
        ]

        non_pending_refs = self._get_transactions_refs_from_adyen(statuses)
        payment_refs.extend(non_pending_refs[0])
        refund_refs.extend(non_pending_refs[1])
        chargeback_refs.extend(non_pending_refs[2])

        (
            capture_payment_refs,
            capture_refund_refs,
            capture_chargeback_refs,
        ) = self._get_payment_refs_based_on_adyen_psp(payment_refs, refund_refs, chargeback_refs)

        # Index references based on transaction status
        return CaptureReferencesPerTransactionStatus(
            payment_refs=list(capture_payment_refs - set(prev_payment_refs)),
            refund_refs=list(capture_refund_refs - set(prev_refund_refs)),
            chargeback_refs=list(capture_chargeback_refs - set(prev_chargeback_refs)),
        )

    def _get_transactions_refs_from_adyen(self, statuses):
        more_transactions_available = True
        page = 1

        while more_transactions_available:
            response = self._get_transaction_list_from_adyen(page, statuses)
            page += 1

            # pylint: disable=line-too-long
            (
                more_transactions_available,
                transactions_data,
            ) = self._parse_adyen_transaction_list_response(response)

            payment_refs, refund_refs, chargeback_refs = [], [], []
            for psp_ref, transaction_status in transactions_data:
                if transaction_status in ('Chargeback', 'ChargebackReversed'):
                    chargeback_refs.append(psp_ref)
                elif transaction_status in ('PendingDebit', 'Debited'):
                    refund_refs.append(psp_ref)
                else:
                    payment_refs.append(psp_ref)

        return payment_refs, refund_refs, chargeback_refs

    def _get_transaction_list_from_adyen(self, page, statuses):
        return requests.list_account_holder_transactions(
            AccountHolderTransactionListArgs(
                accountHolderCode=self.account_holder_code,
                transactionStatuses=statuses,
                transactionListsPerAccount=[
                    TransactionListPerAccount(
                        accountCode=self.account_code,
                        page=page,
                    ),
                ],
                timeout=10,
            )
        )

    @staticmethod
    def _parse_adyen_transaction_list_response(response):
        more_transactions_available = any(
            account_info['hasNextPage'] and len(account_info['transactions'])
            for account_info in response['accountTransactionLists']
        )

        transactions_data = (
            (
                # pspReference corresponds to Auth psp_reference
                # disputePspReference is tied to AdyenNotification object
                transaction.get('pspReference') or transaction.get('disputePspReference'),
                transaction['transactionStatus'],
            )
            for entry in response['accountTransactionLists']
            for transaction in entry['transactions']
        )

        return more_transactions_available, transactions_data

    def perform_manual_payout_action(self) -> None:
        """
        Sends `manual payout` request to the Adyen for paying out all money
        from the account holder's current and pending balance.

        Before sending payout, the pending fund transfers are checked and
        scheduled. If there's not enough money to schedule all fund transfers,
        or there's no balance left after scheduling them, the payout is not
        done and exception is raised instead.

        :raises WrongAccountHolderStatus: in case of account holder not having
                                          manual payouts enabled
        :raises NotEnoughBalance: if the account holder's balance is
                                  insufficient to create payout
        """
        from webapps.market_pay.typing import PayoutAccountHolderRequestArgs

        if not self.manual_payouts_enabled:
            raise WrongAccountHolderStatus(
                'AccountHolder does not have manual payouts enabled.'
                'Turn payout schedule to HOLD and ensure that payouts are '
                'allowed.'
            )
        all_transfer_funds_scheduled, surplus_balance_amount = self.execute_pending_fund_transfers()
        if not all_transfer_funds_scheduled or surplus_balance_amount <= 0:
            raise NotEnoughBalance(
                f"Not enough money to perform payout"
                f"all_transfer_funds_scheduled = {all_transfer_funds_scheduled}"
                f",surplus_balance_amount = {surplus_balance_amount}"
                f",account_holder.id = {self.id}"
                f",account_holder.pos_id = {self.pos_id}"
                f",account_holder.account_code = {self.account_code}"
            )

        ordered_pnrefs = self.retrieve_balance_capture_references()

        if (
            ordered_pnrefs.payment_refs
            or ordered_pnrefs.refund_refs
            or ordered_pnrefs.chargeback_refs
        ):
            # Do not create payout if there are no transactions pending
            with transaction.atomic():
                manual_payout = ManualPendingFundsPayout.objects.create(
                    account_holder=self,
                    currency=settings.CURRENCY_CODE,
                    amount=surplus_balance_amount / 100,
                    payment_refs=ordered_pnrefs.payment_refs,
                    refund_refs=ordered_pnrefs.refund_refs,
                    chargeback_refs=ordered_pnrefs.chargeback_refs,
                )
                manual_payout: ManualPendingFundsPayout
                response = requests.payout_account_holder(
                    PayoutAccountHolderRequestArgs(
                        accountCode=self.account_code,
                        accountHolderCode=self.account_holder_code,
                        amount=AmountRequestArg(
                            currency=settings.CURRENCY_CODE,
                            value=surplus_balance_amount,
                        ),
                        description=manual_payout.get_description_for_report(),
                    )
                )
            manual_payout.psp_reference = response.get('pspReference')
            manual_payout.save(update_fields=['psp_reference'])

    def execute_pending_fund_transfers(self) -> Tuple[bool, int]:
        """
        Sends all pending FundTransfer objects associated with this account
        holder to Adyen for settling.
        Only transfer funds that do not exceed current account balance
        are scheduled.

        :returns tuple: information if all pending fund transfers have been
                        processed and account balance after sending transfers
        """
        from webapps.market_pay.transfer import _execute_fund_transfers

        pending_fund_transfers = FundTransfer.objects.pending().filter(
            account_holder=self,
        )
        account_balance = self.get_balance()

        if account_balance is None:
            return False, -1

        surplus_balance_amount = account_balance.summarized_balance()
        current_surplus_balance_amount = surplus_balance_amount
        all_transfer_funds_scheduled = True
        for fund_transfer in pending_fund_transfers:
            if (surplus_balance_amount + fund_transfer.balance >= 0) or fund_transfer.balance > 0:
                if _execute_fund_transfers(fund_transfer):
                    current_surplus_balance_amount += fund_transfer.balance

            else:
                all_transfer_funds_scheduled = False
        return all_transfer_funds_scheduled, current_surplus_balance_amount


class AccountHolderChangeLog(ArchiveModel):
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='change_logs',
        on_delete=models.CASCADE,
    )

    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    data = models.TextField()

    @cached_property
    def previous(self):
        return AccountHolderChangeLog.objects.filter(
            account_holder_id=self.account_holder_id,
            created__lt=self.created,
        ).last()

    @property
    def diff(self):
        """Compares current data with previous; used in admin."""
        previous = self.previous

        if not previous or not previous.data:
            return 'No previous settings'

        def format_data(data):
            decoded_json = json.loads(data)

            return json.dumps(
                {k: json.dumps(v) for k, v in decoded_json.items()},
                indent=True,
                sort_keys=True,
            )

        try:
            prev_data = format_data(previous.data)
            curr_data = format_data(self.data)
        except ValueError:
            return 'data error'

        differ = Differ()
        _diff = differ.compare(prev_data.split('\n'), curr_data.split('\n'))

        result = '\n'.join((line for line in _diff if line[0] in '+-'))
        result = result.replace('{', '{{').replace('}', '}}')
        return result


class MarketpayNotification(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='notification_id')
    event_type = models.CharField(max_length=64)
    event_date = models.DateTimeField()
    psp_reference = models.CharField(max_length=80, db_index=True)
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='notifications',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    payout = models.ForeignKey(
        'Payout',
        related_name='notifications',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    live = models.BooleanField(default=False)
    content = JSONField(default=dict)
    handled_successfully = models.BooleanField(default=None, null=True)
    prev_oper_result = models.IntegerField(blank=True, null=True)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ['-id']
        get_latest_by = 'event_date'

    def __str__(self):
        return f'{self.id}, {self.event_type}'


class TransferFundsQuerySet(AutoUpdateQuerySet):
    def pending(self):
        return self.filter(
            status__in=[
                enums.TransferFundsStatus.PREPARED.value,
                enums.TransferFundsStatus.FAILED.value,
            ]
        )


class FundTransfer(UndeletableMixin, ArchiveModel):
    class Meta:
        verbose_name_plural = 'Fund Transfers'

    source_account_code = models.CharField(max_length=64)
    destination_account_code = models.CharField(max_length=64)
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='fund_transfers',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    currency = models.CharField(max_length=3)
    amount = models.IntegerField()
    merchant_reference = models.CharField(
        max_length=80,
        db_index=True,
        default=create_uuid,
    )
    transfer_code = models.CharField(
        max_length=80,
        choices=consts.FUND_TRANSFER_CHOICES,
    )
    psp_reference = models.CharField(
        max_length=80,
        blank=True,
        null=True,
        db_index=True,
    )
    payout_reference = models.CharField(
        max_length=64,
        blank=True,
    )
    status = models.CharField(
        max_length=1,
        db_index=True,
        choices=enums.TransferFundsStatus.choices(),
    )
    status_changes = JSONField(
        default=list,
    )
    # list(OperationFee.reference)
    # More: https://phalt.github.io/django-api-domains/
    fee_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )

    # list(Prize.reference)
    prize_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    source_account_description = models.CharField(
        max_length=256,
        null=False,
        blank=True,
        default='',
    )
    destination_account_description = models.CharField(
        max_length=256,
        null=False,
        blank=True,
        default='',
    )

    objects = BaseArchiveManager.from_queryset(TransferFundsQuerySet)()
    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'{self.id}, {self.formatted_amount()}'

    @property
    def balance(self):
        """
        If destination account code is Booksy, balance is negative - we take money from merchant.
        """
        if self.destination_account_code == settings.MARKET_PAY_MERCHANT_ACCOUNT_CODE:
            return -self.amount  # pylint: disable=invalid-unary-operand-type

        return self.amount

    def formatted_amount(self):
        return format_currency(self.amount / CURRENCY_FACTOR)

    def update_status(self, status_change):
        """Update status and record old values to status_changes field

        :param status_change: TransferFundsStatusChange
        """
        record = {
            'old_status': self.status,
            'old_psp_reference': self.psp_reference,
            'new_data': status_change.data,
        }
        self.status = status_change.status
        self.psp_reference = status_change.psp_reference
        self.status_changes.append(record)

        update_fields = ['status', 'status_changes']
        if status_change.psp_reference is not None:
            update_fields.append('psp_reference')

        self.save(update_fields=update_fields)


class PayoutQuerySet(AutoUpdateQuerySet):
    def notification_pending(self):
        return self.filter(
            notification_sent__isnull=True,
        )


class Payout(UndeletableMixin, ArchiveModel):
    # Kind of ID for Payout. Generated by Adyen
    psp_reference = models.CharField(
        max_length=80,
        blank=True,
        null=True,
        db_index=True,
        unique=True,
    )
    report_date = models.DateField(
        default=datetime.date(1990, 1, 1),
    )
    booking_date = models.DateField(default=today)
    currency = models.CharField(max_length=3, default=settings.CURRENCY_CODE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)

    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='payouts',
        on_delete=models.DO_NOTHING,
        null=True,
    )
    # list(pos.PaymentRow.pnref). PR with standard payments
    payment_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.FundTransfer.merchant_reference)
    fee_refs = ArrayField(
        models.CharField(max_length=500),
        default=list,
    )
    # list(pos.PaymentRow.pnref). PR with refunds
    refund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.PaymentRow.pnref). PR with chargebacks
    chargeback_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.FundTransfer.merchant_reference)
    transfer_fund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    notification_sent = models.DateTimeField(null=True)

    objects = BaseArchiveManager.from_queryset(PayoutQuerySet)()
    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'id:{self.id}, Account Holder {self.account_holder_id}: {self.amount} {self.currency}'  # pylint: disable=line-too-long

    def fee_refs_as_array(self):
        if not self.fee_refs:
            return []

        if isinstance(self.fee_refs[0], list):
            return list_flatten(self.fee_refs)

        return list_flatten(map(lambda refs: refs.split(','), self.fee_refs))

    def formatted_amount(self):
        return format_currency(self.amount)


class ManualPendingFundsPayout(UndeletableMixin, ArchiveModel):
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='manual_payouts',
        on_delete=models.DO_NOTHING,
        null=False,
    )
    currency = models.CharField(max_length=3, default=settings.CURRENCY_CODE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)
    psp_reference = models.CharField(max_length=80, db_index=True, null=True, blank=True)
    payment_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    refund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    chargeback_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    paid_out = models.DateField(null=True, blank=True)
    successful = models.BooleanField(null=True, blank=True, default=None)

    MANUAL_PAYOUT_DESCRIPTION_PREFIX = 'Booksy Manual Payout #'

    all_objects = SoftDeleteManager()

    def get_description_for_report(self) -> str:
        """
        Returns string description of the manual payout that can be used
        when requesting payout in Adyen. This description will then be visible
        within marketplace accounting report used in Booksy for tracking
        payouts.
        """
        return f'{self.MANUAL_PAYOUT_DESCRIPTION_PREFIX}{self.id}'

    @classmethod
    def is_manual_payout_notification(cls, notification: MarketpayNotification) -> bool:
        if not 'description' in notification.content:
            return False

        return notification.content['description'].startswith(cls.MANUAL_PAYOUT_DESCRIPTION_PREFIX)

    @classmethod
    def get_from_description(
        cls,
        description: str,
    ) -> Optional[ManualPendingFundsPayout]:
        """
        Returns ManualPendingFundsPayout object associated with the provided
        description, or None if it can't be found.
        """
        manual_payout = None
        if description.startswith(cls.MANUAL_PAYOUT_DESCRIPTION_PREFIX):
            manual_payout = ManualPendingFundsPayout.objects.filter(
                id=description[len(cls.MANUAL_PAYOUT_DESCRIPTION_PREFIX) :]
            ).first()
        return manual_payout


class MarketplaceAccountingQuerySet(AutoUpdateQuerySet):
    RECORDS_SQL = f'''
        SELECT record.*,
               transfer.fee_refs,
               payout.id payout_id,
               payout.psp_reference payout_psp_reference,
               prev_payout.booking_date payout_from,
               payout.booking_date payout_till,
               payout.currency payout_currency,
               payout.amount payout_amount
        FROM market_pay_marketplaceaccounting payout
        -- for filter out liable account join with business account holders
        JOIN market_pay_accountholder account_holder
            ON payout.account_holder_code = account_holder.account_holder_code
        LEFT JOIN LATERAL (
            SELECT prev_payout.booking_date
            FROM market_pay_marketplaceaccounting prev_payout
            WHERE prev_payout.record_type = '{consts.RECORD_TYPE_PAYOUT}' AND
                  prev_payout.booking_date < payout.booking_date AND
                  prev_payout.account_code = payout.account_code
            ORDER BY prev_payout.booking_date DESC
            LIMIT 1
        ) prev_payout ON TRUE

        LEFT JOIN market_pay_marketplaceaccounting record
            ON payout.account_code = record.account_code AND
                record.record_type IN {consts.PAYOUT_RECORD_TYPES} AND
                record.booking_date < payout.booking_date AND
                (
                  prev_payout.booking_date IS NULL OR
                  prev_payout.booking_date < record.booking_date
                )

        LEFT JOIN market_pay_fundtransfer transfer
            ON record.payment_merchant_reference = transfer.merchant_reference

        WHERE payout.record_type = '{consts.RECORD_TYPE_PAYOUT}' AND
             (%(report_date)s IS NULL OR %(report_date)s = payout.report_date) AND
             payout.id = ANY(%(payout_ids)s)
    '''  # nosec
    VALIDATE_SQL = f'''
        SELECT payout_id,
               -payout_amount payout_amount,
               SUM(amount) records_amount,
               array_agg(DISTINCT record_type) record_types
        FROM (
            SELECT payout_id,
                   payout_amount,
                   amount,
                   record_type
            FROM ({RECORDS_SQL}) a
        ) b
        GROUP BY payout_id, payout_amount
        '''  # nosec
    INVALID_SQL = f'''
            SELECT
                a.payout_id,
                r.account_holder_code,
                a.payout_amount,
                a.records_amount,
                a.record_types
            FROM ({VALIDATE_SQL}) a
            LEFT JOIN market_pay_marketplaceaccounting r
                ON a.payout_id = r.id
            WHERE a.payout_amount != a.records_amount OR a.records_amount IS NULL
            '''  # nosec

    CREATE_PAYOUTS_SELECT_SQL = f'''
        WITH payout_records AS ({RECORDS_SQL}),
        payout_aggs AS (
            SELECT
                r.payout_psp_reference,
                array_agg(r.capture_merchant_reference)
                    FILTER(
                        WHERE r.record_type = '{consts.RECORD_TYPE_CREDITED}' AND
                              r.chargeback_psp_reference = ''
                    ) payment_refs,
                array_agg(r.capture_merchant_reference)
                    FILTER(
                        WHERE r.record_type IN {consts.REFUND_RECORD_TYPES} AND
                              r.chargeback_psp_reference = ''
                    ) refund_refs,
                array_agg(r.capture_merchant_reference)
                    FILTER(
                        WHERE r.record_type IN {consts.CHARGEBACK_RECORD_TYPES} AND
                              r.chargeback_psp_reference != ''
                    ) chargeback_refs,
                array_agg(left(array_to_string(r.fee_refs, ','), 467))
                    FILTER(
                        WHERE r.record_type = '{consts.RECORD_TYPE_FUND_TRANSFER}' AND
                              r.fee_refs IS NOT NULL AND
                              r.fee_refs <> '{{}}'
                    ) fee_refs,
                array_agg(r.payment_merchant_reference)
                    FILTER(
                        WHERE r.record_type = '{consts.RECORD_TYPE_FUND_TRANSFER}'
                    ) transfer_fund_refs
            FROM payout_records r
            GROUP BY
                r.payout_psp_reference
        ),
        payouts AS (
            SELECT r.*, a.*, h.accountholder_id
            FROM payout_aggs a
            JOIN market_pay_marketplaceaccounting r ON (
                a.payout_psp_reference = r.psp_reference
            )
            JOIN market_pay_accountholder h ON (
                r.account_holder_code = h.account_holder_code AND
                r.account_code = h.account_code
            )
        )
        SELECT * FROM payouts
        '''  # nosec

    def payout_records_list(self, report_date=None, payout_id=None):
        params = {'report_date': report_date, 'payout_id': payout_id}
        return self.raw(self.RECORDS_SQL, params)

    def payout_validation_list(self, report_date=None, payout_id=None, show_all=False):
        params = {'report_date': report_date, 'payout_id': payout_id}
        sql = self.VALIDATE_SQL if show_all else self.INVALID_SQL
        PayoutValidation = namedtuple(
            'PayoutValidation',
            'valid,payout_id,account_holder_code,payout_amount,records_amount,record_types',
        )
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            return [PayoutValidation(r[1] == r[2], *r) for r in cursor.fetchall()]

    def payouts(self):
        return self.filter(
            record_type=consts.RECORD_TYPE_PAYOUT,
        )

    def create_payouts(self, report_date=None, payout_id=None):
        params = {'report_date': report_date, 'payout_id': payout_id}
        return self.raw(self.CREATE_PAYOUTS_SELECT_SQL, params)


class MarketplaceAccounting(ArchiveModel):
    class Meta:
        unique_together = ('psp_reference', 'record_type')
        index_together = ('account_code', 'booking_date')

    id = models.AutoField(primary_key=True)
    psp_reference = models.CharField(max_length=64)
    report_date = models.DateField(default=today, db_index=True)
    merchant = models.CharField(max_length=64)
    account_holder_code = models.CharField(max_length=64)
    account_code = models.CharField(max_length=64)
    payment_merchant_reference = models.CharField(max_length=64, blank=True)
    capture_merchant_reference = models.CharField(max_length=64, blank=True)
    chargeback_psp_reference = models.CharField(max_length=64, blank=True)
    booking_date = models.DateTimeField()
    record_type = models.CharField(max_length=64)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)
    currency = models.CharField(max_length=3)

    objects = models.Manager.from_queryset(MarketplaceAccountingQuerySet)()

    def __str__(self):
        return f'{self.id}, {self.report_date}, {self.record_type}, {self.account_holder_code}: {self.amount} {self.currency}'  # pylint: disable=line-too-long


class ManualPayout(UndeletableMixin, ArchiveModel):
    referral_reward = models.ForeignKey(
        B2BReferralReward,
        on_delete=models.DO_NOTHING,
        null=True,
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.DO_NOTHING,
        null=True,
    )
    request = JSONField()
    response = JSONField(blank=True, null=True)

    all_objects = SoftDeleteManager()

    @staticmethod
    def _get_prepared_business_name(business):
        """Returns business name without special characters and spaces."""
        return ''.join(c for c in business.name if c.isalnum())

    @classmethod
    def create_request(  # pylint: disable=too-many-arguments
        cls,
        business: Business,
        amount: Decimal,
        bic: str,
        iban: str,
        owner_name: str,
        shopper_statement: str,
    ) -> dict:
        """Creates body request.

        :param business: Business related to payout
        :param amount: amount of payment. Major unit
        :param bic: BIC, nothing special
        :param iban: Iban, nothing special
        :param owner_name: Full name of Bank Account owner
        :param shopper_statement: Kind of transfer title
        :return: request body
        """
        business_name = cls._get_prepared_business_name(business)

        return {
            "bank": {
                "bic": bic,
                "countryCode": settings.API_COUNTRY.upper(),
                "iban": iban,
                "ownerName": owner_name,
            },
            "amount": {
                "value": minor_unit(amount),
                "currency": settings.CURRENCY_CODE,
            },
            "recurring": {
                "contract": "PAYOUT",
            },
            "reference": f"{tznow().strftime('%y%m%d')} - {id_to_external_api(business.id)}",
            "merchantAccount": settings.ADYEN_MERCHANT_ACCOUNT,
            "shopperReference": business_name,
            "shopperEmail": business.owner.email,
            "shopperStatement": shopper_statement,
        }

    @classmethod
    def create_referral_reward_payout(
        cls,
        referral_reward: B2BReferralReward,
        bic: str,
        iban: str,
        owner_name: str,
    ):
        """Creates manual payout for reward.

        :param referral_reward: B2BReferralReward related to payout
        :param bic: BIC, nothing special
        :param iban: Iban, nothing special
        :param owner_name: Full name of Bank Account owner
        """

        request = cls.create_request(
            business=referral_reward.business,
            amount=referral_reward.amount,
            bic=bic,
            iban=iban,
            owner_name=owner_name,
            shopper_statement='Booksy reward',
        )

        return cls.objects.create(referral_reward=referral_reward, request=request)

    @classmethod
    def create_standalone_payout(  # pylint: disable=too-many-arguments
        cls,
        business: Business,
        amount: Decimal,
        bic: str,
        iban: str,
        owner_name: str,
    ):
        """Creates request for regular countries [GB, IE]

        :param business: Business related to payout
        :param amount: amount of payment. Major unit
        :param bic: BIC, nothing special
        :param iban: Iban, nothing special
        :param owner_name: Full name of Bank Account owner
        """

        request = cls.create_request(
            business=business,
            amount=amount,
            bic=bic,
            iban=iban,
            owner_name=owner_name,
            shopper_statement='payout',
        )

        return cls.objects.create(business=business, request=request)

    def send(self):
        """Sends manual payout.

        Result will be saved in response field.
        If there is related referral_reward and everything was ok, mark it as
        paid.
        """
        response = make_request(self.request, settings.ADYEN_MANUAL_PAY_OUT_URL, manual_payout=True)

        if response.json():
            self.response = {'body': response.json(), 'status_code': response.status_code}
            self.save(update_fields=['response'])

            if response.status_code == status.HTTP_200_OK and self.referral_reward:
                self.referral_reward.mark_as_paid_manually()
