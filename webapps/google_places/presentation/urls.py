from django.urls import path

from webapps.google_places.presentation.views.google_places import (
    GooglePlacesDetailsView,
    GooglePlacesAddressAutocompleteView,
    GooglePlacesBusinessAutocompleteView,
)

urlpatterns = [
    path('details/<str:place_id>', GooglePlacesDetailsView.as_view(), name='google_places_details'),
    path(
        'autocomplete/address/',
        GooglePlacesAddressAutocompleteView.as_view(),
        name='google_places_address_autocomplete',
    ),
    path(
        'autocomplete/business/',
        GooglePlacesBusinessAutocompleteView.as_view(),
        name='google_places_business_autocomplete',
    ),
]
