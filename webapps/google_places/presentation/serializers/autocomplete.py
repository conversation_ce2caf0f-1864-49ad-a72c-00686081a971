from rest_framework import serializers


class GooglePlacesAddressSuggestionsResponseSerializer(serializers.Serializer):
    place_id = serializers.CharField()
    main_text = serializers.CharField()
    secondary_text = serializers.Char<PERSON><PERSON>()


class GooglePlacesAddressAutocompleteSerializer(serializers.Serializer):
    search_input = serializers.CharField(required=True, allow_blank=False)
    longitude = serializers.FloatField(required=False, allow_null=True)
    latitude = serializers.FloatField(required=False, allow_null=True)


class GooglePlacesAddressAutocompleteResponseSerializer(serializers.Serializer):
    suggestions = GooglePlacesAddressSuggestionsResponseSerializer(many=True)


class GooglePlacesBusinessSuggestionsResponseSerializer(serializers.Serializer):
    place_id = serializers.Char<PERSON>ield()
    main_text = serializers.CharField()
    secondary_text = serializers.Char<PERSON><PERSON>()


class GooglePlacesBusinessAutocompleteSerializer(serializers.Serializer):
    search_input = serializers.Char<PERSON>ield(required=True, allow_blank=False)


class GooglePlacesBusinessAutocompleteResponseSerializer(serializers.Serializer):
    suggestions = GooglePlacesAddressSuggestionsResponseSerializer(many=True)
