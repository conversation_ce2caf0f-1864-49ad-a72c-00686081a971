import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource

from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestBusinessAutocompleteView(BaseBusinessApiTestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        super().setUp()

    def test_get_success(self):
        """Test successful GET request to Google Places details endpoint."""
        url = reverse(
            "google_places_business_autocomplete",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        query_params = {
            'search_input': "SomeBusinessName",
            'longitude': 10.02310,
            'latitude': 132.3123,
        }
        response = self.client.get(url, query_params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_response(self):
        url = reverse(
            "google_places_business_autocomplete",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        query_params = {
            'search_input': "SomeBusinessName",
            'longitude': 10.02310,
            'latitude': 132.3123,
        }

        response = self.client.get(url, query_params)
        expected_response = {
            'suggestions': [
                {
                    'place_id': 'ChIJS6opydEtPUcRjnxYvCorb_c',
                    'main_text': 'BarBer Shop Szkolna 4',
                    'secondary_text': 'Radom, Polska',
                },
                {
                    'place_id': 'ChIJQeC28sKbPEcRetf6LGEceYw',
                    'main_text': 'BarvBert Szkolna 40',
                    'secondary_text': 'Jarosław, Polska',
                },
            ]
        }
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)

    def test_empty_search_input(self):
        """Test GET request with empty/blank input returns 400 error."""
        empty_url = reverse(
            "google_places_business_autocomplete",
            kwargs={"business_pk": self.business.id},
        )

        response = self.client.get(empty_url, {})

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        data = response.json()
        self.assertIn('errors', data)
        self.assertIn('search_input', data['errors'])
