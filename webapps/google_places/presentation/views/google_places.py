from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin, QuerySerializerMixin
from webapps.business.models import Resource
from webapps.google_places.domain.dtos.autocomplete import (
    AddressAutocompleteSuggestions,
    BusinessAutocompleteSuggestions,
    Suggestion,
)
from webapps.google_places.domain.dtos.place_details import (
    PlaceDetails,
    Location,
    AddressComponents,
)
from webapps.google_places.presentation.serializers.autocomplete import (
    GooglePlacesAddressAutocompleteSerializer,
    GooglePlacesAddressAutocompleteResponseSerializer,
)
from webapps.google_places.presentation.serializers.places_details import (
    GooglePlacesDetailsResponseSerializer,
    GooglePlacesDetailsRequestSerializer,
)


class GooglePlacesDetailsView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesDetailsResponseSerializer
    query_serializer_class = GooglePlacesDetailsRequestSerializer

    """
    Response body attributes:
    `place_id` (str): Google Place ID used to get details about a place.
    `name` (str): Name of a place taken from Google Place Details.
    `formatted_address` (str): Address oneliner taken from Google Place Details.
    `location` (dict): Longitude and Latitude taken from Google Place Details.
    `address_components` (dict): More detailed location data taken from Google Place Details.
    """

    def get(self, request: Request, business_pk: int, place_id: str, *args, **kwargs):
        """
        Get detailed information about a specific place.

        Args:
            place_id (str): The Google Places API place identifier starting with ChIJ
        """
        self.get_business(business_pk)
        request_serializer = self.get_query_serializer(data={'place_id': place_id})

        request_serializer.is_valid(raise_exception=True)
        place_details_dto = PlaceDetails(
            place_id=place_id,
            name="Sample Business",
            formatted_address="Street 1, 37-450 City, Poland",
            location=Location(latitude=50.5688818, longitude=22.0583922),
            address_components=AddressComponents(
                street_number="1",
                street="TestStreet",
                city="City",
                country="Polska",
                zipcode="37-450",
            ),
        )
        response_serializer = self.get_serializer(place_details_dto)

        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesAddressAutocompleteView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesAddressAutocompleteResponseSerializer
    query_serializer_class = GooglePlacesAddressAutocompleteSerializer

    def get(self, request: Request, business_pk: int, *args, **kwargs):
        self.get_business(business_pk)
        request_serializer = self.get_query_serializer(data=request.query_params)
        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )

        address_autocomplete_dto = AddressAutocompleteSuggestions(
            [
                Suggestion(
                    place_id="ChIJN1t_tDeuEmsRUsoyG83frY4",
                    main_text="Teodora Zielińskiego 4",
                    secondary_text="Radom, Polska",
                ),
                Suggestion(
                    place_id="ChIJQeC28sKbPEcRetf6LGEceYw",
                    main_text="Zygmunta Zielińskiego 4",
                    secondary_text="Jarosław, Polska",
                ),
            ]
        )

        response_serializer = self.get_serializer(address_autocomplete_dto)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesBusinessAutocompleteView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesAddressAutocompleteResponseSerializer
    query_serializer_class = GooglePlacesAddressAutocompleteSerializer

    def get(self, request: Request, business_pk: int, *args, **kwargs):
        self.get_business(business_pk)
        request_serializer = self.get_query_serializer(data=request.query_params)

        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )

        business_autocomplete_dto = BusinessAutocompleteSuggestions(
            [
                Suggestion(
                    place_id="ChIJS6opydEtPUcRjnxYvCorb_c",
                    main_text="BarBer Shop Szkolna 4",
                    secondary_text="Radom, Polska",
                ),
                Suggestion(
                    place_id="ChIJQeC28sKbPEcRetf6LGEceYw",
                    main_text="BarvBert Szkolna 40",
                    secondary_text="Jarosław, Polska",
                ),
            ]
        )

        response_serializer = self.get_serializer(business_autocomplete_dto)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)
