# Generated by Django 2.0.13 on 2020-03-20 11:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pop_up_notification', '0033_add_valid_since_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='B2BReferralNotification',
            fields=[
                (
                    'genericpopupnotificationmodel_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='pop_up_notification.GenericPopUpNotificationModel',
                    ),
                ),
                (
                    'event_type',
                    models.CharField(
                        choices=[
                            ('s', 'Signed up'),
                            ('ro', 'Reward paid out'),
                            ('rp', 'Reward pending'),
                            ('ds', 'Did you know? Single subscribe'),
                            ('db', 'Did you know? Both subscribe'),
                            ('fs', 'Referrer cashed in'),
                            ('fb', 'You and referrer cashed in'),
                            ('is', 'More people like you'),
                            ('ib', 'More people like you both'),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    'image_type',
                    models.CharField(
                        choices=[('P', 'Present'), ('T', 'Tadaa'), ('B', 'Bulb')], max_length=1
                    ),
                ),
                ('text_1', models.CharField(blank=True, max_length=512, null=True)),
                ('text_2', models.CharField(blank=True, max_length=512, null=True)),
                ('text_3', models.CharField(blank=True, max_length=512, null=True)),
                ('text_button', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ['valid_till'],
            },
            bases=('pop_up_notification.genericpopupnotificationmodel',),
        ),
        migrations.AlterField(
            model_name='genericpopupnotificationmodel',
            name='notification_type',
            field=models.CharField(
                choices=[
                    ('meet_me_again', 'Meet me Again!'),
                    ('business_like', 'Business You may like'),
                    ('category_like', 'Category You may like'),
                    ('booking_pattern', ''),
                    ('short_review', 'Short Review'),
                    ('referral_c2b_reward_change', 'Reward C2B Status Change'),
                    ('late_cancellation', 'Late Cancellation'),
                    ('cross_booking_after_first', 'Cross booking after first booking'),
                    ('digital_flyer', 'Digital Flyer'),
                    ('subdomain', 'Subdomain'),
                    ('mobile_payments_introduction', 'Mobile Payments Introduction'),
                    ('mobile_payments_migration', 'Mobile Payments Migration'),
                    ('boost_intro', 'Boost Intro'),
                    ('b2b_referral', 'B2B Referral'),
                ],
                default='',
                max_length=30,
            ),
        ),
    ]
