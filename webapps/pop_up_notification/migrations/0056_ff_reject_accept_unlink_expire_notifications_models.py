# Generated by Django 4.0.2 on 2022-03-24 12:30

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('family_and_friends', '0022_add_rejected_invitation_enum'),
        ('pop_up_notification', '0055_merge_20220322_1129'),
    ]

    operations = [
        migrations.CreateModel(
            name='FamilyAndFriendsUnlinkNotification',
            fields=[
                (
                    'genericpopupnotificationmodel_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='pop_up_notification.genericpopupnotificationmodel',
                    ),
                ),
                (
                    'trigger_profile',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='unlink_triggered_by_me',
                        to='family_and_friends.memberprofile',
                    ),
                ),
                (
                    'unlinked_profile',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='unlinked_by',
                        to='family_and_friends.memberprofile',
                    ),
                ),
            ],
            options={
                'verbose_name': 'F&F Unlink Notification',
                'verbose_name_plural': 'F&F Unlink Notifications',
                'ordering': ['valid_since'],
            },
            bases=('pop_up_notification.genericpopupnotificationmodel',),
            managers=[
                ('objects', lib.models.NoCreateArchiveManager()),
            ],
        ),
        migrations.CreateModel(
            name='FamilyAndFriendsInvitationResponseNotification',
            fields=[
                (
                    'genericpopupnotificationmodel_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='pop_up_notification.genericpopupnotificationmodel',
                    ),
                ),
                (
                    'invitation_status',
                    models.CharField(
                        choices=[('A', 'Accepted'), ('R', 'Rejected'), ('E', 'Expired')],
                        max_length=1,
                    ),
                ),
                (
                    'member',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='family_and_friends.memberprofile',
                    ),
                ),
            ],
            options={
                'verbose_name': 'F&F Invitation Response Notification',
                'verbose_name_plural': 'F&F Invitation Response Notifications',
                'ordering': ['valid_since'],
            },
            bases=('pop_up_notification.genericpopupnotificationmodel',),
            managers=[
                ('objects', lib.models.NoCreateArchiveManager()),
            ],
        ),
    ]
