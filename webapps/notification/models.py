r"""
Setup HOWTO
http://stackoverflow.com/questions/21250510/generate-pem-file-used-to-setup-apple-push-notification

get cert-push.p12 file with password
$ openssl pkcs12 -in certificate-push.p12 -out cert.pem -nodes -clcerts -legacy

pass: omen4\hits

cert.pem is proper cert for our use

http://bsdsupport.org/how-do-i-determine-the-expiration-date-of-a-p12-certificate/
$ (devel)$ openssl x509 -in booksy-cust-private.cer -noout -enddate
notAfter=Oct 10 13:58:10 2014 GMT
"""

import csv
import io
import json
from datetime import datetime, timedelta
from types import MappingProxyType
from typing import Self

from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models, transaction
from django.db.models import F, JSONField, Q
from django.utils.translation import gettext_lazy as _

from lib import safe_json
from lib.booksy_sms import (
    SMSCosts,
    expand_sms_limit_postpaid_history,
    expand_sms_prepaid_history,
    parse_phone_number,
)
from lib.enums import SMSPaidEnum, SMSTypeEnum
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.models import ArchiveManager, ArchiveModel, TimestampedModel
from lib.payment_gateway.enums import PaymentMethodType
from lib.tools import deprecated, round_to_month, rounded_month_range, tznow

# noinspection PyUnresolvedReferences
from webapps.business.models import Business
from webapps.notification.enums import (
    BlastSendType,
    NotificationCategory,
    NotificationSendStatus,
    NotificationService,
    ScheduleState,
)
from webapps.notification.events import sms_statistics_upsert_event
from webapps.notification.tasks import single_notification_schedule_task
from webapps.notification.utils import get_non_trial_sms_limits
from webapps.user.models import UserProfile, User


class UserNotification(ArchiveModel):
    EMAIL_NOTIFICATION = 'E'
    PUSH_NOTIFICATION = 'P'
    SMS_NOTIFICATION = 'S'
    POPUP_NOTIFICATION = 'O'
    CUSTOMER_POPUP_NOTIFICATION = 'C'
    NOTIFICATION_TYPES = (
        (EMAIL_NOTIFICATION, _("E-mail notifications")),
        (PUSH_NOTIFICATION, _("Push notifications")),
        (SMS_NOTIFICATION, _("SMS notifications")),
        (POPUP_NOTIFICATION, _("POPUP notifications")),
        (CUSTOMER_POPUP_NOTIFICATION, _("Customer POPUP notifications")),
    )
    id = models.AutoField(primary_key=True, db_column='user_notification_id')
    profile = models.ForeignKey(
        UserProfile,
        related_name='notifications',
        on_delete=models.CASCADE,
    )
    type = models.CharField(max_length=1, choices=NOTIFICATION_TYPES)
    settings_data = JSONField(default=dict)
    badge = models.IntegerField(default=0)

    class Meta:
        unique_together = ('profile', 'type')

    def set_defaults_for_missing_settings(self):
        """When we add new notification definitions, old settings in the DB
        must be updated."""
        updated = False

        # Should I block any new notifications by default
        default_block_all = self.settings_data.get('block_all', False)
        if 'default_block_all' not in self.settings_data:
            self.settings_data['default_block_all'] = False
            updated = True

        definitions = settings.NOTIFICATION_DEFINITIONS.get(self.profile.profile_type, {})
        for code in definitions.get(self.type, {}):
            if code not in self.settings_data:
                self.settings_data[code] = (
                    not default_block_all and definitions[self.type][code]['default']
                )
                updated = True

        if updated:
            self.save()


class RecieverManager(ArchiveManager):
    pass


class Reciever(ArchiveModel):
    IOS = 'ios'
    ANDROID = 'android'
    DEVICE_CHOICES = (
        (IOS, IOS),
        (ANDROID, ANDROID),
    )
    id = models.AutoField(primary_key=True, db_column='reciever_id')
    identifier = models.CharField(max_length=512)
    language = models.CharField(max_length=8, default='en')
    customer_notifications = models.ForeignKey(
        UserNotification,
        related_name='recievers',
        on_delete=models.CASCADE,
    )
    # Business notifications must be specified per-business, i.e.
    # business IS NOT NULL
    # Customer notifications are global for customer's profile, i.e.
    # business IS NULL
    business = models.ForeignKey(
        'business.Business',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    # A M2M relation to filter notifications for this Receiver
    # only to the bound resources
    # NOTE: if no resources are assigned to Receiver, it is assumed that
    # this Receiver supports all business' Resources. Thus we do not support
    # the option "This Receiver has no Resources assigned".
    # NOTE2: there is a DB constraint that the Resources bound for this
    # Receiver must come from the assigned Business
    resources = models.ManyToManyField('business.Resource', related_name='receivers')
    device = models.CharField(max_length=100, choices=DEVICE_CHOICES, blank=True, null=True)
    is_tablet = models.BooleanField(blank=True, null=True)
    is_superuser = models.BooleanField(default=False, blank=True, null=True)
    last_refresh = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Last refresh datetime (UTC)',
    )
    session_hash = models.CharField(max_length=128, null=True, blank=True, db_index=True)

    objects = RecieverManager()
    all_objects = models.Manager()

    class Meta:
        # One user can have only one business setting
        unique_together = (('identifier', 'business', 'customer_notifications'),)
        constraints = [
            models.UniqueConstraint(
                fields=('identifier', 'business', 'customer_notifications'),
                condition=Q(deleted__isnull=True),
                name='notification_receiver_uniq_v2',
            ),
        ]

    def __str__(self):
        return f"{self.id} {self.language} - {self.identifier}"

    def format_reciever(self):
        return {
            'business': self.business_id,
            'business_id': self.business_id,
            'customer_notifications_id': self.customer_notifications_id,
            'identifier': self.identifier,
            'language': self.language,
            'resources': [res.id for res in self.resources.all()],
            'type': self.customer_notifications.type,
        }


def split_meta_dict(input_dict):
    main, meta = {}, {}
    for key, val in list(input_dict.items()):
        if key.startswith('meta_'):
            meta[key[5:]] = val
        else:
            main[key] = val
    return main, meta


class NotificationHistory(models.Model):
    SENDER_SYSTEM = 'S'
    SENDER_BUSINESS = 'B'
    SENDER_CUSTOMER = 'C'

    SENDER_TYPES = (
        (SENDER_SYSTEM, _("Sent by Booksy")),
        (SENDER_BUSINESS, _("Sent by Business")),
        (SENDER_CUSTOMER, _("Sent by Customer")),
    )

    TASK_TYPE__ACCOUNT_ADDED = 'AA'
    TASK_TYPE__B_LISTING_CLAIMED = 'BD'
    TASK_TYPE__BLAST = 'BL'
    TASK_TYPE__BOOKING_CHANGED = 'BC'
    TASK_TYPE__BOOKING_FINISHED = 'BF'
    TASK_TYPE__BOOKING_OTHER = 'BO'
    TASK_TYPE__BOOST_CHANGE_STATUS = 'BX'
    TASK_TYPE__BOOST_CHANGE_STATUS_WITH_DATE = 'BW'
    TASK_TYPE__BOOST_SET_DATE = 'BE'
    TASK_TYPE__BOOST_SWITCH = 'BS'
    TASK_TYPE__BUSINESS_ACCOUNT_INVITE = 'BI'
    TASK_TYPE__BUSINESS_ACTIVITY = 'BT'
    TASK_TYPE__BUSINESS_ADDED = 'BA'
    TASK_TYPE__BUSINESS_CHANGE_PAYMENT_SOURCE = 'PS'
    TASK_TYPE__COMMISSION_CHANGE = 'CC'
    TASK_TYPE__COMMISSION_CLOSE = 'CL'
    TASK_TYPE__EXTERNAL_BUSINESS_RECOMMENDATION = 'EB'
    TASK_TYPE__GDPR_EXPORT = 'GE'
    TASK_TYPE__IMPORT_CUSTOMERS_REPORT = 'IC'
    TASK_TYPE__INVITATION = 'IT'
    TASK_TYPE__INVITATION_REMINDER = 'IR'
    TASK_TYPE__PASSWORD_RESET = 'PR'
    TASK_TYPE__PATTERN_REMINDER = 'PP'
    TASK_TYPE__EMAIL_CHANGE = 'EC'
    TASK_TYPE__POS = 'PO'
    TASK_TYPE__REVIEW_ADDED = 'RA'
    TASK_TYPE__REVIEW_REPLY_ADDED = 'RR'
    TASK_TYPE__SMS_BLAST = 'SB'
    TASK_TYPE__SMS_BOOKING_REACTIVATION = 'BR'
    TASK_TYPE__SMS_CUSTOMER_INVITATION = 'CX'
    TASK_TYPE__SMS_EXTERNAL_BUSINESS = 'SE'
    TASK_TYPE__SMS_GATE = 'SG'
    TASK_TYPE__SMS_REGISTRATION_CODE = 'SR'
    TASK_TYPE__SMS_TOOL = 'ST'
    TASK_TYPE__SMS_WAIT_LIST = 'WL'
    TASK_TYPE__STATISTICS = 'SC'
    TASK_TYPE__TRANSACTION = 'TR'
    TASK_TYPE__TEMPLATE_TEST = 'TT'
    TASK_TYPE__UPDATE_REPEATING_REPORT = 'UR'
    TASK_TYPE__NO_SHOW_PROPOSITION = 'CF'
    TASK_TYPE__NO_SHOW_CONFIRMATION = 'NC'
    TASK_TYPE__NO_SHOW_INFORMATION = 'NS'
    TASK_TYPE__LM_PROMO_INCENTIVE = 'LM'
    TASK_TYPE__HH_PROMO_INCENTIVE = 'HH'
    TASK_TYPE__DIGITAL_FLYER = 'DF'
    TASK_TYPE__IMPORT_WAREHOUSE_COMMODITIES_REPORT = 'IW'
    TASK_TYPE__IMPORT_WHOLESALER_COMMODITIES_REPORT = 'WC'
    TASK_TYPE__VOUCHER = 'VC'
    TASK_TYPE__SMS_MARKETING_CONSENT = 'MC'
    TASK_TYPE__BOOKSY_PAY = 'BP'
    TASK_TYPE__TTP = 'TP'
    TASK_TYPE__TTP_OR_BCR = 'TB'
    TASK_TYPE__PREMIUM_HOURS = 'PH'

    task_id_to_task_type_mapping = {
        'account_added': TASK_TYPE__ACCOUNT_ADDED,
        'blast': TASK_TYPE__BLAST,
        'b_listing_claimed': TASK_TYPE__B_LISTING_CLAIMED,
        'booking_changed': TASK_TYPE__BOOKING_CHANGED,
        'booking_finished': TASK_TYPE__BOOKING_FINISHED,
        'booking_other': TASK_TYPE__BOOKING_OTHER,
        'boost_change_status': TASK_TYPE__BOOST_CHANGE_STATUS,
        'boost_change_status_with_date': TASK_TYPE__BOOST_CHANGE_STATUS_WITH_DATE,
        'boost_status_set_date': TASK_TYPE__BOOST_SET_DATE,
        'boost_switch': TASK_TYPE__BOOST_SWITCH,
        'business_account_invite': TASK_TYPE__BUSINESS_ACCOUNT_INVITE,
        'business_activity': TASK_TYPE__BUSINESS_ACTIVITY,
        'business_added': TASK_TYPE__BUSINESS_ADDED,
        'business_change_payment_source': TASK_TYPE__BUSINESS_CHANGE_PAYMENT_SOURCE,
        'external_business_recommendation': TASK_TYPE__EXTERNAL_BUSINESS_RECOMMENDATION,
        'gdpr_export': TASK_TYPE__GDPR_EXPORT,
        'import_customers_report': TASK_TYPE__IMPORT_CUSTOMERS_REPORT,
        'invitation': TASK_TYPE__INVITATION,
        'invitation_reminder': TASK_TYPE__INVITATION_REMINDER,
        'mass_commission_change_task': TASK_TYPE__COMMISSION_CHANGE,
        'mass_commission_close_task': TASK_TYPE__COMMISSION_CLOSE,
        'password_reset': TASK_TYPE__PASSWORD_RESET,
        'pattern_reminder': TASK_TYPE__PATTERN_REMINDER,
        'pos': TASK_TYPE__POS,
        'review_added': TASK_TYPE__REVIEW_ADDED,
        'review_reply_added': TASK_TYPE__REVIEW_REPLY_ADDED,
        'sms_blast': TASK_TYPE__SMS_BLAST,
        'sms_external_business': TASK_TYPE__SMS_EXTERNAL_BUSINESS,
        'sms_gate': TASK_TYPE__SMS_GATE,
        'sms_registration_code': TASK_TYPE__SMS_REGISTRATION_CODE,
        'sms_tool': TASK_TYPE__SMS_TOOL,
        'statistics': TASK_TYPE__STATISTICS,
        'template_test': TASK_TYPE__TEMPLATE_TEST,
        'transaction': TASK_TYPE__TRANSACTION,
        'update_repeating_report': TASK_TYPE__UPDATE_REPEATING_REPORT,
        'no_show_proposition': TASK_TYPE__NO_SHOW_PROPOSITION,
        'no_show_confirmation': TASK_TYPE__NO_SHOW_CONFIRMATION,
        'no_show_information': TASK_TYPE__NO_SHOW_INFORMATION,
        'last_minute_incentive': TASK_TYPE__LM_PROMO_INCENTIVE,
        'happy_hours_incentive': TASK_TYPE__HH_PROMO_INCENTIVE,
        'waitlist': TASK_TYPE__SMS_WAIT_LIST,
        'df': TASK_TYPE__DIGITAL_FLYER,
        'import_warehouse_commodities_report': TASK_TYPE__IMPORT_WAREHOUSE_COMMODITIES_REPORT,
        'import_wholesaler_commodities_report': TASK_TYPE__IMPORT_WHOLESALER_COMMODITIES_REPORT,
        'sms_booking_reactivation': TASK_TYPE__SMS_BOOKING_REACTIVATION,
        'voucher': TASK_TYPE__VOUCHER,
        'sms_marketing_consent': TASK_TYPE__SMS_MARKETING_CONSENT,
        'sms_customer_invitation': TASK_TYPE__SMS_CUSTOMER_INVITATION,
        'booksy_pay': TASK_TYPE__BOOKSY_PAY,
        'premium_hours': TASK_TYPE__PREMIUM_HOURS,
    }

    # Required to apply sms limits
    task_type_to_sms_type_mapping = MappingProxyType(
        {
            TASK_TYPE__INVITATION: SMSTypeEnum.INVITATION,
            TASK_TYPE__SMS_BLAST: SMSTypeEnum.MARKETING,
            TASK_TYPE__BOOKING_CHANGED: SMSTypeEnum.SYSTEM,
        }
    )

    TASK_TYPE_CHOICES = tuple((v, k) for k, v in list(task_id_to_task_type_mapping.items()))

    id = models.AutoField(primary_key=True, db_column='user_notification_id')
    created = models.DateTimeField(
        auto_now_add=True,
        db_index=True,
        verbose_name='Created (UTC)',
    )
    type = models.CharField(
        max_length=1,
        choices=UserNotification.NOTIFICATION_TYPES,
        default=UserNotification.EMAIL_NOTIFICATION,
    )
    sender = models.CharField(
        max_length=1,
        choices=SENDER_TYPES,
    )

    task_id = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        db_index=True,
    )
    task_type = models.CharField(
        null=True,
        max_length=2,
        choices=TASK_TYPE_CHOICES,
        db_index=True,
    )
    blast_id = models.IntegerField(null=True, blank=True, db_index=True)
    appointment_id = models.IntegerField(null=True, blank=True, db_index=True)
    booking_id = models.IntegerField(null=True, blank=True, db_index=True)
    business_id = models.IntegerField(null=True, blank=True, db_index=True)
    customer_id = models.IntegerField(null=True, blank=True, db_index=True)
    customer_card_id = models.IntegerField(
        null=True,
        blank=True,
        db_index=True,
    )
    recipient_phone = BooksyPhoneNumberField(
        null=True,
        blank=True,
        db_index=True,
    )
    recipient_email = models.EmailField(blank=True, null=True)

    parameters = models.TextField(null=True, blank=True)

    sms_count = models.IntegerField(null=True, blank=True)
    sms_cost = models.DecimalField(null=True, max_digits=6, decimal_places=3)

    # 48190, 48305
    free_sms_bunch_id = models.IntegerField(
        null=True,
        blank=True,
    )

    title = models.TextField(null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    metadata = models.TextField(null=True, blank=True)
    status = models.CharField(
        blank=True,
        null=True,
        max_length=1,
        choices=NotificationSendStatus.choices(),
    )
    service = models.CharField(
        max_length=1,
        choices=NotificationService.choices(),
        null=True,
    )
    external_id = models.CharField(
        max_length=100,
        null=True,
    )
    blast_send_type = models.CharField(
        max_length=1,
        choices=BlastSendType.choices(),
        null=True,
        default=None,
    )

    class Meta:
        verbose_name = _('Paid SMS Messages')
        verbose_name_plural = _('Paid SMS Messages')

        indexes = [
            models.Index(fields=['recipient_email'], name='notification_hstry_recipie_idx'),
            models.Index(fields=['sender'], name='notification_hstry_sender_idx'),
            models.Index(fields=['type'], name='notification_hstry_type_idx'),
        ]

    @classmethod
    def add(cls, save=True, only_document=False, **kwargs):
        # pylint: disable=cyclic-import
        from webapps.notification.elasticsearch import NotificationHistoryDocument

        main, meta = split_meta_dict(kwargs)

        # set created/updated for es doc
        now = tznow()
        main['created'] = main.get('created', now)
        main['updated'] = main.get('updated', now)

        # Try to guess task_type, allowing also to set null if needed
        cls.guess_and_set_task_type(main)

        if 'parameters' in main:
            if isinstance(main['parameters'], str):
                main['parameters'] = json.loads(main['parameters'])
        entry = NotificationHistoryDocument(metadata=meta, **main)
        if save:
            entry.save()
            if not only_document and entry.is_paid_sms():
                # remove fields missing in db model
                for field in ('updated', 'errors', 'webhook_id'):
                    main.pop(field, None)
                db_entry = NotificationHistory(metadata=safe_json.dumps(meta, pretty=True), **main)
                db_entry.save()

        return entry

    @classmethod
    def from_document(cls, doc, save=True):
        fields = doc.to_dict()
        # remove fields missing in db model
        for field in ('updated', 'errors', 'webhook_id'):
            fields.pop(field, None)
        entry = NotificationHistory(
            metadata=safe_json.dumps(fields.pop('metadata', {}), pretty=True),
            **fields,
        )
        if save:
            if not doc.is_paid_sms():
                raise ValueError('Only paid sms and waitlist records are saved in db')
            entry.save()
        return entry

    @classmethod
    def tasks_create(cls, objs, batch_size=2000):
        # pylint: disable=cyclic-import
        from webapps.notification.elasticsearch import NotificationHistoryDocument

        NotificationHistoryDocument.bulk_create(objs)
        db_objs = [
            NotificationHistory(**entry.to_dict())
            for entry in filter(NotificationHistoryDocument.is_paid_sms, objs)
        ]
        if db_objs:
            NotificationHistory.objects.bulk_create(
                db_objs,
                batch_size=batch_size,
            )

    @classmethod
    def guess_and_set_task_type(cls, data):
        """
        Try to guess task_type based on task_id and insert it into data dict.
        """
        if 'task_type' not in data:
            task_id = data.get('task_id') or ''
            data['task_type'] = cls.task_id_to_task_type_mapping.get(task_id.split(':')[0])

    @classmethod
    def task_name_by_type_or_category(cls, task_type):
        available_types = dict(NotificationCategory.choices())
        available_types.update(dict(cls.TASK_TYPE_CHOICES))
        return available_types.get(task_type)

    @classmethod
    @deprecated
    def sms_summary(cls, business):  # pylint: disable=too-many-branches
        """Returns sms usage by calendar months.
        Also includes "demo_account" period, which indicates how many sms
        merchant has used in trial period."""
        now = business.tznow

        records = NotificationHistory.objects.filter(
            business_id=business.id,
            sender=NotificationHistory.SENDER_BUSINESS,
            type=UserNotification.SMS_NOTIFICATION,
            sms_cost__isnull=False,
            free_sms_bunch_id__isnull=True,
        ).values_list('created', 'sms_count')

        result_keys = ['demo_account']
        sub = business.subscriptions.only('start').order_by('start').first()
        if sub is not None:
            for dt in rounded_month_range(sub.start, now):
                result_keys.append(dt.strftime('%Y-%m'))

        sms_limit_history = expand_sms_limit_postpaid_history(business)
        sms_prepaid_history = expand_sms_prepaid_history(business)

        result = {}
        for result_key in result_keys:
            result[result_key] = {
                'period': result_key,
                'limit_free': sms_prepaid_history.get(
                    result_key,
                    (
                        settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT
                        if result_key == 'demo_account'
                        else settings.SMS_PAID_ACCOUNT_PREPAID_COUNT
                    ),
                ),
                'limit_paid': sms_limit_history.get(result_key, 0),
                'count_free': 0,
                'count_paid': 0,
                'payable_cost': 0.0,
                'payable_cost_formatted': SMSCosts.sms_cost_format(0),
            }

        if sub is not None:
            demo_till = sub.start
        else:
            demo_till = now

        for created, sms_count in records:
            if created < demo_till:
                key = 'demo_account'
            else:
                key = created.strftime('%Y-%m')

            if key not in result:
                continue

            item = result[key]
            if item['count_free'] < item['limit_free']:
                item['count_free'] += sms_count
            else:
                item['count_paid'] += sms_count

        for item in list(result.values()):
            if not item['count_paid']:
                continue
            item['payable_cost'] = SMSCosts.sms_cost_calculate(item['count_paid'])
            item['payable_cost_formatted'] = SMSCosts.sms_cost_format(item['count_paid'])

        return [result[i] for i in reversed(result_keys)]

    @classmethod
    @deprecated
    def sms_summary_by_billing_cycles(cls, business, billing_cycles_amount=2):
        """Returns sms usage by billing cycles. Billing cycles are calculated
        based on latest (already started) subscriptions.
        Can also include "demo_account" period, which indicates how many sms
        merchant has used in trial period.

        Example: Merchant has bought subscription on 17.01.2020 00:00,
        we check sms summary on 23.02.2020, using billing_cycles_amount=2.
        In return we will get 2 periods:
        "demo account" - showing how many sms merchant has used before buying
        subscription, so till 17.01.2020 00:00 exclusive
        "2020-01" - showing how many sms merchant has used from 17.01.2020 00:00
        till 17.02.2020 00:00 exclusive

        For multiple subscriptions active at the same time:
        s1 - first subscription
        s2 - second subscription

                                                |-s2-|-s2-|-s2-|-s2-|----- now
        |-----trial------|-s1-|-s1-|-s1-|-s1-|-s1-|-s1-|------------------ now

        SMS summary will look like this:

        | "demo account" |-s1-|-s1-|-s1-|-s1-|-s1-|-s2-|-s2-|-s2-|-s2-|    now

        """
        from webapps.purchase.utils import (
            collect_billing_cycles_for_business,
            get_subscription_prepaid_sms_allowance,
        )

        result = []
        sms_count_qs = NotificationHistory.objects.filter(
            business_id=business.id,
            sender=NotificationHistory.SENDER_BUSINESS,
            type=UserNotification.SMS_NOTIFICATION,
            sms_cost__isnull=False,
            free_sms_bunch_id__isnull=True,
        )
        sms_limit_history = expand_sms_limit_postpaid_history(business)
        billing_cycles_data = collect_billing_cycles_for_business(
            business, amount=billing_cycles_amount
        )

        for data in billing_cycles_data:
            sub = data['subscription']
            billing_cycles = data['billing_cycles']
            for start, end in billing_cycles:
                result_key = start.strftime('%Y-%m')
                limit_prepaid = get_subscription_prepaid_sms_allowance(sub, start, end)
                sms_count = (
                    sms_count_qs.filter(
                        created__gte=start,
                        created__lt=end,
                    ).aggregate(
                        cnt=models.Sum('sms_count')
                    )['cnt']
                    or 0
                )
                count_prepaid = min(sms_count, limit_prepaid)
                count_postpaid = max(sms_count - count_prepaid, 0)
                res = {
                    'period': result_key,
                    'limit_free': limit_prepaid,
                    'limit_paid': sms_limit_history.get(result_key, 0),
                    'count_free': count_prepaid,
                    'count_paid': count_postpaid,
                    'payable_cost': SMSCosts.sms_cost_calculate(count_postpaid),
                    'payable_cost_formatted': SMSCosts.sms_cost_format(count_postpaid),
                }
                result.append(res)
        if len(result) < billing_cycles_amount:
            # Add demo period
            result_key = 'demo_account'
            paid_from = business.paid_from
            if not paid_from and billing_cycles_data:
                paid_from = billing_cycles_data[-1]['subscription'].start
            sms_count = (
                sms_count_qs.filter(
                    created__lt=paid_from or tznow(),
                ).aggregate(
                    cnt=models.Sum('sms_count')
                )['cnt']
                or 0
            )
            limit_prepaid = settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT
            count_prepaid = min(sms_count, limit_prepaid)
            count_postpaid = max(sms_count - count_prepaid, 0)
            res = {
                'period': result_key,
                'limit_free': limit_prepaid,
                'limit_paid': sms_limit_history.get(result_key, 0),
                'count_free': count_prepaid,
                'count_paid': count_postpaid,
                'payable_cost': SMSCosts.sms_cost_calculate(count_postpaid),
                'payable_cost_formatted': SMSCosts.sms_cost_format(count_postpaid),
            }
            result.append(res)
        return result

    @classmethod
    @deprecated
    def sms_stats(cls, business):
        now = tznow()

        if business.status == Business.Status.BLOCKED_OVERDUE:
            return 'expired', 0, 0, 0

        sub = business.subscriptions.only('start').order_by('start').first()
        paid_from = sub.start if sub is not None else None
        if paid_from is not None and paid_from < now:
            if settings.SMS_LIMITS_IN_SUBSCRIPTION:
                from webapps.purchase.utils import calculate_current_billing_cycle

                # we are already in paid period
                latest_sub = (
                    business.subscriptions.filter(start__lt=now)
                    .order_by('-expiry', '-start', '-id')
                    .only(
                        'start',
                        'expiry',
                        'current_billing_cycle_start',
                        'current_billing_cycle_end',
                        'source',
                    )
                    .first()
                )
                # Subscription expiry can be already in the past. We have to
                # calculate "current" billing cycle as if subscription was still
                # valid, otherwise merchant could send unlimited amount of sms
                # as we would check text messages sent long time ago (not
                # necessarily this month).
                created_from, created_till = calculate_current_billing_cycle(latest_sub)
            else:
                created_from = max(round_to_month(now), paid_from)
                created_till = round_to_month(now, month_diff=1)

            predicates = {
                'created__gte': created_from,
                'created__lt': created_till,
            }

            sms_status = 'paid'
            sms_limit_free = get_non_trial_sms_limits(business.id)['prepaid_sms_count']
            sms_limit_payable = business.sms_limit
        else:
            if paid_from is not None:
                # we are before paid period
                predicates = {
                    'created__lt': paid_from,
                }
            else:
                # there is no paid period
                predicates = {}

            sms_status = 'demo'
            sms_limit_free = settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT
            sms_limit_payable = 0

        result = NotificationHistory.objects.filter(
            business_id=business.id,
            sender=NotificationHistory.SENDER_BUSINESS,
            sms_cost__isnull=False,
            free_sms_bunch_id__isnull=True,
            **predicates,
        ).aggregate(
            sms_count_total=models.Sum('sms_count'),
        )

        sms_sent_count = result['sms_count_total'] or 0

        return sms_status, sms_sent_count, sms_limit_free, sms_limit_payable

    @classmethod
    def sms_history(cls, business_id):
        def months_ago(months):
            dt = tznow()
            year, month = divmod(dt.year * 12 + dt.month - 1 - months, 12)
            return datetime(year, month + 1, 1, tzinfo=dt.tzinfo)

        data = (
            NotificationHistory.objects.filter(
                type=UserNotification.SMS_NOTIFICATION,
                sender=NotificationHistory.SENDER_BUSINESS,
                created__gte=months_ago(months=3),
                business_id=business_id,
            )
            .order_by('created')
            .values_list(
                'metadata',
                'sms_count',
                'created',
                'title',
            )
        )
        smses = []

        previous_month = ''
        current_index = 0
        current_cost = 0
        for metadata, sms_count, created, title in data:
            try:
                json_data = json.loads(metadata)
            except Exception:  # pylint: disable=broad-except
                json_data = {}

            if created.strftime('%Y-%m') != previous_month:
                current_index = 0
                current_cost = 0
            previous_month = created.strftime('%Y-%m')

            customer_phone = parse_phone_number(json_data.get('to_original')).global_nice

            smses.append(
                {
                    'index': current_index + 1,
                    'cost': sms_count,
                    'date': created.strftime('%Y-%m-%dT%H:%M:%S'),
                    'customer_phone': customer_phone,
                    'customer_name': json_data.get('recipient_name'),
                    'message_type_code': 'booking_reminder',
                    'message_type_label': _("Booking reminder"),
                    'message_text': title,
                }
            )

            current_index += 1
            current_cost += sms_count

        return smses

    FORMAT_CSV_FIELDS = [
        'index',
        'cost',
        'date',
        'customer_phone',
        'customer_name',
        'message_type_code',
        'message_type_label',
        'message_text',
    ]

    @classmethod
    def sms_history_format_csv(cls, records):
        def to_utf8(value):
            if isinstance(value, str):
                return value.encode('utf-8')
            return str(value).encode('utf-8')

        f = io.StringIO()
        writer = csv.writer(f)
        writer.writerow([i.replace('_', ' ').title() for i in cls.FORMAT_CSV_FIELDS])
        for record in records:
            writer.writerow([to_utf8(record[i]) for i in cls.FORMAT_CSV_FIELDS])

        return f.getvalue()


class NotificationSMSStatistics(ArchiveModel):
    business = models.ForeignKey(
        'business.Business', on_delete=models.DO_NOTHING, db_constraint=False
    )
    date = models.DateField()
    parts_count = models.IntegerField()
    sms_count = models.IntegerField()

    class Meta:
        unique_together = ('business', 'date')

    @staticmethod
    def is_business_paying(business_id):
        from webapps.billing.models import BillingSubscription
        from webapps.purchase.models import Subscription

        return (
            Subscription.has_business_ever_had_subscription(business_id)
            or BillingSubscription.objects.filter(
                business_id=business_id, date_start__lte=tznow()
            ).exists()
        )

    @staticmethod
    def _limit_free_from_settings(period_str):
        """This is old approach when limit free not included in subscription"""

        if period_str == NotificationSMSStatistics._trial_label():
            return settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT
        return settings.SMS_PAID_ACCOUNT_PREPAID_COUNT

    @staticmethod
    def _limit_free_paid_business(business):
        return get_non_trial_sms_limits(business.id)['prepaid_sms_count']

    @staticmethod
    def get_postpaid_sms_limit(business):
        if business.has_new_billing:
            return settings.BILLING_SMS_HARD_LIMIT
        return business.sms_limit

    @classmethod
    def sms_stats(cls, business):
        """Computes sms basic business usage stats for the current billing_cycle/month/trial

        It returns:
            sms_status, -> on which sms status business is. Possible values:
                            'expired', 'demo', 'paid'
            parts_count, -> how many sms parts were used by this business
                            in the current month (or on the trial period)
            sms_limit_free, -> how many sms parts a business has for free (prepaid)
            sms_limit_postpaid -> how many sms parts a business can pay for (postpaid)

        :param business: business for which current sms parts usage stats
                         are computed
        :return: See above
        """
        if business.status in (
            Business.Status.BLOCKED_OVERDUE,
            Business.Status.CHURNED,
        ):
            # business churned
            return 'expired', 0, 0, 0
        if cls.is_business_paying(business.id):
            sms_status = 'paid'
        else:
            sms_status = cls._trial_status()
        sms_summary = cls.sms_summary(business, periods_amount=1)[0]
        return (
            sms_status,
            sms_summary['parts_count'],
            sms_summary['limit_free'],
            sms_summary['limit_paid'],
        )

    @staticmethod
    def get_summary_entry(business, period_str, parts_count, limit_free=None, limit_paid=None):
        if limit_free is None:
            limit_free = NotificationSMSStatistics._limit_free_from_settings(period_str)
        if limit_paid is None:
            sms_limit_history = expand_sms_limit_postpaid_history(business)
            limit_paid = sms_limit_history.get(period_str, 0)
        (
            free_parts_count,
            paid_parts_count,
        ) = NotificationSMSStatistics._compute_free_and_paid_sms_parts(
            parts_count,
            limit_free,
        )
        return {
            'count_paid': paid_parts_count,
            'current_limit_free': max(0, limit_free - parts_count),
            'current_sms_price': SMSCosts.get_part_price_and_currency()[0],
            'limit_free': limit_free,
            'limit_paid': limit_paid,
            'parts_count': parts_count,
            'count_free': free_parts_count,
            'payable_cost': float(SMSCosts.sms_cost_calculate(paid_parts_count)),
            'payable_cost_formatted': SMSCosts.sms_cost_format(paid_parts_count),
            'period': period_str,
        }

    @classmethod
    def get_parts_count_in_period(
        cls,
        business_id,
        date_start: datetime.date,
        date_end: datetime.date,
    ):
        return (
            cls.objects.filter(
                business_id=business_id,
                date__gte=date_start,
                date__lte=date_end,
            ).aggregate(cnt=models.Sum('parts_count'))['cnt']
            or 0
        )

    @classmethod
    def sms_summary(cls, business, periods_amount=2):
        from webapps.billing.models import BillingSubscription

        if business.has_new_billing and (
            subscription := BillingSubscription.get_current_subscription(business.id)
        ):
            from webapps.billing.utils import billing_sms_summary_by_billing_cycles

            results = billing_sms_summary_by_billing_cycles(
                subscription,
                billing_cycles_amount=periods_amount,
            )

        else:
            results = cls.sms_summary_by_billing_cycles(
                business, billing_cycles_amount=periods_amount
            )

        if len(results) < periods_amount:
            results.append(cls.trial_sms_summary(business))

        return results

    @classmethod
    def sms_limits(cls, business):
        current_sms_stats = cls.sms_summary(business, periods_amount=1)[0]
        parts_count = current_sms_stats['parts_count']
        limit_free = current_sms_stats['limit_free']
        limit_paid = current_sms_stats['limit_paid']
        return max(limit_free + limit_paid - parts_count, 0)

    @classmethod
    def sms_summary_by_billing_cycles(
        cls,
        business,
        billing_cycles_amount=2,
    ):
        """
        [ACHTUNG] Method supports old subscriptions (webapps.purchase).
        In case of similar method for Booksy Billing use billing_sms_summary_by_billing_cycles or
        cls.sms_summary

        Returns sms usage by billing cycles. Billing cycles are calculated
        based on latest (already started) subscriptions.
        Can also include trial period, which indicates how many sms
        merchant has used in trial period.

        Example: Merchant has bought subscription on 17.01.2020 00:00,
        we check sms summary on 23.02.2020, using billing_cycles_amount=2.
        In return we will get 2 periods:
        "demo account" - showing how many sms merchant has used before buying
        subscription, so till 17.01.2020 00:00 exclusive
        "2020-01-17" -
            showing how many sms merchant has used from 17.01.2020 00:00
        till 17.02.2020 00:00 exclusive

        For multiple subscriptions active at the same time:
        s1 - first subscription
        s2 - second subscription

                                                |-s2-|-s2-|-s2-|-s2-|----- now
        |-----trial------|-s1-|-s1-|-s1-|-s1-|-s1-|-s1-|------------------ now

        SMS summary will look like this:

        | "demo account" |-s1-|-s1-|-s1-|-s1-|-s1-|-s2-|-s2-|-s2-|-s2-|    now
        """

        from webapps.purchase.utils import (
            collect_billing_cycles_for_business,
            get_subscription_prepaid_sms_allowance,
        )

        billing_cycles_data = collect_billing_cycles_for_business(
            business, amount=billing_cycles_amount
        )
        stats_qs = cls.objects.filter(business_id=business.id)
        results = []

        for data in billing_cycles_data:
            sub = data['subscription']
            billing_cycles = data['billing_cycles']
            for start, end in billing_cycles:
                period_str = start.strftime('%Y-%m')
                limit_free = get_subscription_prepaid_sms_allowance(sub, start, end)
                parts_count = (
                    stats_qs.filter(
                        date__gte=start.date(),
                        date__lt=end.date(),
                    ).aggregate(
                        cnt=models.Sum('parts_count')
                    )['cnt']
                    or 0
                )
                results.append(
                    cls.get_summary_entry(
                        business,
                        period_str,
                        parts_count,
                        limit_free,
                    )
                )
        return results

    @classmethod
    def trial_sms_summary(cls, business):
        parts_count = 0
        if entry := cls.objects.filter(
            business_id=business.id,
            date=cls._trial_date(),
        ).first():
            parts_count = entry.parts_count

        return cls.get_summary_entry(
            business,
            cls._trial_label(),
            parts_count,
            limit_paid=0,
        )

    @staticmethod
    def _trial_date():
        return datetime.min.date()

    @staticmethod
    def _trial_label():
        return 'demo_account'

    @staticmethod
    def _trial_status():
        return 'demo'

    @staticmethod
    def _compute_free_and_paid_sms_parts(parts_count, limit_free):
        """Computes how many sms parts were for free and payable

        :param parts_count: number of sms parts used
        :param limit_free: how many sms parts were for free
        :return: tuple containing free and paid parts count
        """
        free_parts = min(parts_count, limit_free)
        return free_parts, parts_count - free_parts

    @classmethod
    @transaction.atomic
    def upsert_for_business(cls, business_id, parts_count):
        """Inserts into or updates notification sms statistics table.

        For every given business id it creates a new row in
        notification_notificationsmsstatistics table or updates existing one.
        A key for insert/update consists of business_id and date value
        (we collect daily statistics). Sms count is incremented by one and
        parts count is incremented by a value given as the argument.
        Rules:
            - if business is not paying yet or will start to pay in the future
              (subscription check) a special demo/trial case row is
              inserted/updated. Demo/trial case row can be distinguished by
              date value of 0001-01-01 date(datetime.min.date())
            - else, if business is already paying, a row with date value of
              a current day is inserted/updated. For example, if the current
              date is 2019-02-24 a row with date value of 2019-02-24
              is inserted/updated.

        :param business_id: business for which we track sms statistics
        :param parts_count: sms parts count used for updating
        :return:
        """
        if cls.is_business_paying(business_id):
            date = tznow().date()
        else:
            date = cls._trial_date()

        statistics, created = NotificationSMSStatistics.objects.get_or_create(
            business_id=business_id,
            date=date,
            defaults={
                'parts_count': parts_count,
                'sms_count': 1,
            },
        )
        if not created:
            statistics.parts_count = F('parts_count') + parts_count
            statistics.sms_count = F('sms_count') + 1
            statistics.save(update_fields=['parts_count', 'sms_count'])

        sms_statistics_upsert_event.send(business_id)


class NotificationSMSCodes(models.Model):
    id = models.AutoField(primary_key=True, db_column='sms_code_id')
    created = models.DateTimeField(
        auto_now_add=True,
        db_index=True,
        verbose_name='Created (UTC)',
    )
    consumed = models.DateTimeField(null=True, blank=True, verbose_name='Used (UTC)')
    phone = models.CharField(max_length=20, blank=False)
    sms_code = models.CharField(max_length=20, blank=False)

    abuse_ip_address = models.CharField(max_length=40, null=True, blank=True)
    abuse_fingerprint = models.CharField(max_length=50, null=True, blank=True)

    metadata = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['-created']
        verbose_name = _('Notification SMS Code')
        verbose_name_plural = _('Notification SMS Codes')

    @classmethod
    def add(  # pylint: disable=too-many-positional-arguments
        cls, phone, sms_code, abuse_ip_address, abuse_fingerprint, metadata
    ):  # pylint: disable=too-many-arguments
        entry = NotificationSMSCodes(
            phone=phone,
            sms_code=sms_code,
            abuse_ip_address=abuse_ip_address,
            abuse_fingerprint=abuse_fingerprint,
            metadata=safe_json.dumps(metadata),
        )
        entry.save()

    @classmethod
    def get_recent(cls, phone, seconds):
        result = NotificationSMSCodes.objects.filter(
            phone=phone,
            created__gte=(tznow() - timedelta(seconds=seconds)),
        )
        return result

    @classmethod
    def is_valid(cls, phone, sms_code, seconds):
        if not sms_code:
            return False
        return NotificationSMSCodes.objects.filter(
            phone=phone,
            sms_code=sms_code,
            created__gte=(tznow() - timedelta(seconds=seconds)),
            consumed=None,
        ).exists()

    @classmethod
    def get_usage_count(cls, abuse_fingerprint, seconds):
        # 23224-widget-sms-limit: current (2015-10-22) implementation of
        # Widget does not send abuse_fingerprint so it is set to None
        # and in result there is small (4 per hour), global limit for all
        # registrations made from one country
        if not abuse_fingerprint:
            return 0

        return NotificationSMSCodes.objects.filter(
            abuse_fingerprint=abuse_fingerprint,
            created__gte=(tznow() - timedelta(seconds=seconds)),
        ).count()


class NotificationEmailCodes(TimestampedModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    email_address = models.EmailField(max_length=75, blank=False)
    confirmation_code = models.CharField(max_length=20, blank=False)

    class Meta:
        ordering = ['-created']
        verbose_name = 'Notification Email Code'
        verbose_name_plural = 'Notification Email Codes'
        index_together = ('user', 'email_address', 'confirmation_code')

    @classmethod
    def add(
        cls,
        email_address: str,
        confirmation_code: str,
        user: User,
    ) -> Self:  # pylint: disable=too-many-arguments

        return cls.objects.create(
            email_address=email_address.lower(),
            confirmation_code=confirmation_code,
            user=user,
        )

    @classmethod
    def is_valid(
        cls, user_id: int, email_address: str, confirmation_code: str, minutes: int
    ) -> bool:
        if not confirmation_code:
            return False
        return cls.objects.filter(
            user_id=user_id,
            email_address=email_address.lower(),
            confirmation_code=confirmation_code,
            created__gte=(tznow() - timedelta(minutes=minutes)),
        ).exists()


class NotificationSchedule(models.Model):
    id = models.BigAutoField(primary_key=True)

    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    updated = models.DateTimeField(auto_now=True, verbose_name='Updated (UTC)')

    scheduled = models.DateTimeField(null=False, blank=False, db_index=True)
    finished = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Finished (UTC)',
    )

    state = models.CharField(max_length=16, choices=ScheduleState.choices())
    task_id = models.CharField(max_length=255, unique=True)

    # JSON with parameters and result (returned value or exception) of task
    parameters = models.JSONField(null=True, blank=True, encoder=DjangoJSONEncoder)
    result = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = _('Notification Schedule')
        verbose_name_plural = _('Notification Schedules')
        indexes = [
            models.Index(
                fields=['state', 'scheduled'],
                name='n_ns_state_scheduled_idx',
            ),
        ]

    @classmethod
    def upsert(cls, task_id, when, parameters):
        from webapps.notification.scenarios import task_result_appended

        instant_execution = when <= tznow()
        state = ScheduleState.RECEIVED if instant_execution else ScheduleState.PENDING
        entry, created = NotificationSchedule.objects.get_or_create(
            task_id=task_id,
            defaults={
                'scheduled': when,
                'state': state,
                'parameters': parameters,
            },
        )
        if not created:
            result = {
                'state': state,
                'created': entry.created.strftime('%F_%T.%f'),
                'updated': tznow().strftime('%F_%T.%f'),
                'scheduled': when.strftime('%F_%T.%f'),
            }
            entry.scheduled = when
            entry.state = state
            entry.parameters = parameters
            entry.result = safe_json.dumps(
                task_result_appended(entry.result, result),
                pretty=True,
            )
            entry.save()
        if instant_execution:
            # no need to wait for process_notification_schedules_task_with_cache
            single_notification_schedule_task.apply_async(
                args=[
                    entry.id,
                    tznow().isoformat(),
                ],
                # use priority queue, users expect it to be sent instantly
                queue='scenarios_single_priority',
            )

    @classmethod
    def get_or_create(cls, task_id, when, parameters):
        entry, _created = NotificationSchedule.objects.get_or_create(
            task_id=task_id,
            defaults={
                'scheduled': when,
                'state': ScheduleState.RECEIVED,
                'parameters': parameters,
            },
        )
        return entry

    def __repr__(self):
        return f'<{self._meta.object_name}: {self.task_id} {self.state}>'

    def __str__(self):
        return f'{self._meta.object_name} {self.task_id} {self.state}'


class FreeSMSBunch(models.Model):
    """
    Used to keep "first N free invitation per phone number" events etc.

    For countries we don't want to charge for type X we don't
    save free sms events with type X (where X is any of possible
    sms_type values).
    As of 21.09.2018 we are interested only in INVITE sms_type.
    For counting total sent (free & paid) sms amount we use NotificationHistory.

    ACHTUNG: If ex. in PL 1st invitation for each end customer is free,
    the merchant can still use his sms subscription's "free sms limit" to send
    the 2nd one and don't pay for it.
    Sms messages sent using sms subscription's "free sms limit" are NOT
    reflected here.

    Has to be separate model as NotificationHistory is deleted periodically.
    """

    business = models.ForeignKey(
        Business,
        on_delete=models.CASCADE,
    )
    # addressee
    # 1 cell phone == 1 end customer
    cell_phone = BooksyPhoneNumberField(null=True)
    # Used sms count (NOT sms parts)
    sms_count = models.PositiveIntegerField(default=1)

    updated = models.DateTimeField(auto_now=True)
    sms_type = models.CharField(max_length=1, choices=SMSTypeEnum.choices(), null=True, blank=True)

    class Meta:
        index_together = (('business', 'cell_phone'),)


class LimitedSMSBunch(models.Model):
    """
    AKA "Total M sms of type ..." limit

    Keeps sent sms amount for unpaid businesses (only for specific countries,
    see settings). Each sms type X (where X can be one of the following:
    invitation, notification, marketing) has its own limit in settings.
    Once limit is reached by business, no more smses with type X can be sent.

    sms_count is been reset to 0 once Business is in Business.Status.PAID
    status.
    """

    business = models.ForeignKey(
        'business.Business',
        db_index=True,
        related_name='limited_smses',
        on_delete=models.CASCADE,
    )
    # Used sms count (NOT sms parts)
    sms_count = models.IntegerField(default=0)

    updated = models.DateTimeField(auto_now=True)
    sms_type = models.CharField(max_length=1, choices=SMSTypeEnum.choices(), null=True, blank=True)

    @classmethod
    def get_limit_available(cls, business, sms_type):
        """Checks how many smses of type <sms_type> can Business send
        according to country settings.
        Doesn't take into account Business specific sms_limit.
        Returns None in case of unlimited amount.
        """
        # "Total M" SMS limit makes no sense for non-trial merchants and
        # is not included in subscription plans config - DO NOT change following
        # 2 lines of code unless you include "total M" sms allowance in
        # subscription plans.
        if business.sms_limits_paid_status == SMSPaidEnum.NON_TRIAL:
            return None
        # None means unlimited! Always take 0 as default
        limit = settings.TOTAL_M_SMS_LIMITS.get(business.sms_limits_paid_status, {}).get(
            sms_type, 0
        )
        if limit is not None and limit > 0:
            # Special case for lavito
            importer = business.integrations.get('importer')
            if importer and importer in ['lavito', 'lavito-demo']:
                return None
            # Usual case
            sms_bunch, _created = cls.objects.get_or_create(
                business_id=business.id, sms_type=sms_type.value
            )
            return limit - sms_bunch.sms_count
        return limit

    @classmethod
    def update_limit_used(cls, business_id, sms_type, paid_status, amount=1):
        # None means unlimited! Always take 0 as default
        limit = settings.TOTAL_M_SMS_LIMITS.get(paid_status, {}).get(sms_type, 0)
        # TODO: If M limit has to be less important than X+Y then
        # change to if limit is not None and limit > 0 and amount > 0 (?)
        if limit is not None and amount > 0:
            updated_count = cls.objects.filter(
                business_id=business_id, sms_type=sms_type.value
            ).update(
                sms_count=models.F('sms_count') + amount,
            )
            # Shouldn't happen, as we create object in get_limit_available()
            # (unless we've sent sms against M limit == 0)
            if not updated_count:
                cls.objects.get_or_create(
                    business_id=business_id,
                    sms_type=sms_type.value,
                    defaults={'sms_count': amount},
                )


class SpecialDay(models.Model):
    name = models.CharField(max_length=64)
    date = models.DateField()


class TapToPayNotificationRecord(models.Model):
    # DEPRECATED IN FAVOR OF PMAvailabilityNotificationRecord (will be removed soon)

    # in selected salons, clients get a notification reminding them that they can pay with
    # tap to pay, we want to be able to limit the total number of notifications sent,
    # hence this tracking
    task_id = models.CharField(max_length=255, unique=True)
    user_id = models.IntegerField()


class PMAvailabilityNotificationRecord(ArchiveModel):
    # in selected salons, clients get a notification reminding them that they can pay with
    # some payment method, we want to be able to limit the total number of notifications sent,
    # hence this tracking
    task_id = models.CharField(max_length=255, unique=True)
    user_id = models.IntegerField()
    payment_methods = ArrayField(
        models.CharField(
            max_length=20,
            choices=PaymentMethodType.choices(),
        )
    )


class TippingAppetiteNotificationRecord(ArchiveModel):
    # After booking for some user we send fakedoor notification about tipping
    # for the service as experiment if they are eager to tip staffer
    task_id = models.CharField(max_length=255, unique=True)
    user_id = models.IntegerField(db_index=True)
