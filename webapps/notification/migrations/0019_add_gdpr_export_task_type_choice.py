# Generated by Django 1.11.11 on 2018-08-08 12:36
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0018_merge_20180806_0910'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationhistory',
            name='task_type',
            field=models.CharField(
                choices=[
                    ('GE', 'gdpr_export'),
                    ('EB', 'external_business_recommendation'),
                    ('PO', 'pos'),
                    ('SE', 'sms_external_business'),
                    ('ST', 'sms_tool'),
                    ('BT', 'business_activity'),
                    ('IC', 'import_customers_report'),
                    ('SC', 'statistics'),
                    ('TT', 'template_test'),
                    ('SB', 'sms_blast'),
                    ('BF', 'booking_finished'),
                    ('BI', 'business_account_invite'),
                    ('IT', 'invitation'),
                    ('RA', 'review_added'),
                    ('BC', 'booking_changed'),
                    ('IR', 'invitation_reminder'),
                    ('BL', 'blast'),
                    ('TR', 'transaction'),
                    ('PR', 'password_reset'),
                    ('SG', 'sms_gate'),
                    ('UR', 'update_repeating_report'),
                    ('RR', 'review_reply_added'),
                    ('SR', 'sms_registration_code'),
                    ('AA', 'account_added'),
                    ('BA', 'business_added'),
                ],
                db_index=True,
                max_length=2,
                null=True,
            ),
        ),
    ]
