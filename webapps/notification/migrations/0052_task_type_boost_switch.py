# Generated by Django 3.1.7 on 2021-03-17 11:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0051_notificationhistory_service_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationhistory',
            name='task_type',
            field=models.CharField(
                choices=[
                    ('AA', 'account_added'),
                    ('BL', 'blast'),
                    ('BD', 'b_listing_claimed'),
                    ('BC', 'booking_changed'),
                    ('BF', 'booking_finished'),
                    ('BO', 'booking_other'),
                    ('BS', 'boost_switch'),
                    ('BI', 'business_account_invite'),
                    ('BT', 'business_activity'),
                    ('BA', 'business_added'),
                    ('EB', 'external_business_recommendation'),
                    ('GE', 'gdpr_export'),
                    ('IC', 'import_customers_report'),
                    ('IT', 'invitation'),
                    ('IR', 'invitation_reminder'),
                    ('PR', 'password_reset'),
                    ('PO', 'pos'),
                    ('RA', 'review_added'),
                    ('RR', 'review_reply_added'),
                    ('SB', 'sms_blast'),
                    ('SE', 'sms_external_business'),
                    ('SG', 'sms_gate'),
                    ('SR', 'sms_registration_code'),
                    ('ST', 'sms_tool'),
                    ('SC', 'statistics'),
                    ('TT', 'template_test'),
                    ('TR', 'transaction'),
                    ('UR', 'update_repeating_report'),
                    ('CF', 'no_show_proposition'),
                    ('NS', 'no_show_information'),
                    ('LM', 'last_minute_incentive'),
                    ('HH', 'happy_hours_incentive'),
                    ('WL', 'waitlist'),
                    ('DF', 'df'),
                    ('IW', 'import_warehouse_commodities_report'),
                    ('WC', 'import_wholesaler_commodities_report'),
                    ('BR', 'sms_booking_reactivation'),
                ],
                db_index=True,
                max_length=2,
                null=True,
            ),
        ),
    ]
