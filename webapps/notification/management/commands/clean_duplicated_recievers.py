import time
import typing as t
from dataclasses import dataclass

from django.core.management import CommandError
from django.core.management.base import BaseCommand
from django.db.models import Value, CharField, Count
from django.db.models.functions import Concat

from lib.db import get_replica_delay
from webapps.notification.models import Reciever


@dataclass
class OptionsData:
    remove_all: t.Optional[bool]
    remove_x_number: t.Optional[int]
    removing_speed: int


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            '--remove-all',
            required=False,
            dest='remove_all',
            action='store_true',
        )
        parser.add_argument(
            '--remove-x-number',
            required=False,
            dest='remove_x_number',
            type=int,
        )
        parser.add_argument(
            '--removing-speed',
            required=False,
            dest='removing_speed',
            type=int,
            default=1000,
        )

    def handle(self, *args, **options):
        options_data = self._validate_options(options)
        divider = '&&&'

        query = Reciever.objects.annotate(
            full_key=Concat(
                'business_id',
                Value(divider),
                'identifier',
                Value(divider),
                'customer_notifications_id',
                output_field=CharField(),
            ),
        )
        query_with_counters = query.values('full_key').annotate(
            full_key_count=Count('full_key'),
        )
        duplication_keys = (
            query_with_counters.filter(
                full_key_count__gt=1,
            )
            .values_list(
                'full_key',
                flat=True,
            )
            .order_by('full_key')
            .distinct()
        )

        number_of_removed_duplicates = 0

        ids_to_remove = []

        for duplicate_key in duplication_keys.iterator():
            if (
                options_data.remove_x_number
                and (number_of_removed_duplicates + len(ids_to_remove))
                >= options_data.remove_x_number
            ):
                break

            business_id, identifier, customer_notifications_id = duplicate_key.split(divider)
            reciever_ids = Reciever.objects.filter(
                business_id=business_id or None,
                identifier=identifier,
                customer_notifications_id=customer_notifications_id or None,
            ).values_list('id', flat=True)

            if len(reciever_ids) <= 1:
                continue
            duplicates_ids_to_remove = reciever_ids[1:]
            ids_to_remove.extend(duplicates_ids_to_remove)

            if len(ids_to_remove) >= options_data.removing_speed:
                self.hard_delete_recievers(ids_to_remove)
                number_of_removed_duplicates += len(ids_to_remove)
                self._stdout_number_of_removed_duplicates(number_of_removed_duplicates)
                ids_to_remove = []
                self._care_about_replica_sync()

        self.hard_delete_recievers(ids_to_remove)
        number_of_removed_duplicates += len(ids_to_remove)
        self._stdout_number_of_removed_duplicates(number_of_removed_duplicates)
        self.stdout.write("Script successfully finished")

    @staticmethod
    def _validate_options(options: dict) -> OptionsData:
        remove_x_number = options.get('remove_x_number')
        remove_all = options.get('remove_all')
        removing_speed = options.get('removing_speed')
        if remove_x_number and remove_all:
            raise CommandError(
                '--remove-x-number and --remove-all flags provided. Use --help for more info.'
            )
        if not remove_x_number and not remove_all:
            raise CommandError(
                'No --remove-x-number or --remove-all flags provided. Use --help for more info.'
            )
        if removing_speed and removing_speed < 1:
            raise CommandError('Invalid --removing-speed flag value. Use --help for more info.')
        return OptionsData(
            removing_speed=removing_speed or 100,
            remove_all=remove_all,
            remove_x_number=remove_x_number,
        )

    @staticmethod
    def hard_delete_recievers(duplicates_ids_to_remove: t.List[int]) -> None:
        if not duplicates_ids_to_remove:
            return
        Reciever.all_objects.filter(id__in=duplicates_ids_to_remove).delete()

    def _stdout_number_of_removed_duplicates(self, number_of_removed_duplicates: int):
        self.stdout.write(
            f"{number_of_removed_duplicates} Recievers duplicates successfully removed."
        )

    def _care_about_replica_sync(self):
        time.sleep(2)
        delay = get_replica_delay()
        if delay and delay > 10:
            self.stdout.write("Exceptional wait time (30 sec) because of sync problem with replica")
            time.sleep(30)
