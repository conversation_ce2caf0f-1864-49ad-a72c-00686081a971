import json

from django.core.cache import cache


class NotificationDispatcher<PERSON><PERSON>oryCache:
    """History of dispatched ids which can held MAX_LEN_LIST batch histories"""

    notification_history_queue_name = '_notification_scheduled_lately'
    CACHE = cache
    MAX_LEN_LIST = 10

    @classmethod
    def add_to_history(cls, ids: list[int]) -> None:
        client = cls.CACHE.client.get_client()
        client.lpush(cls.notification_history_queue_name, json.dumps(ids))
        cls.remove_old_history()

    @classmethod
    def remove_old_history(cls):
        client = cls.CACHE.client.get_client()
        old_histories = client.lrange(
            cls.notification_history_queue_name,
            cls.MAX_LEN_LIST,
            cls.MAX_LEN_LIST * 2,
        )
        for history_elem in old_histories:
            client.lrem(cls.notification_history_queue_name, 1, history_elem)

    @classmethod
    def get_history(cls) -> set[int]:
        client = cls.CACHE.client.get_client()
        encoded_message = client.lrange(cls.notification_history_queue_name, 0, cls.MAX_LEN_LIST)
        return set(id for history_elem in encoded_message for id in json.loads(history_elem))

    @classmethod
    def clear_all_history(cls):
        client = cls.CACHE.client.get_client()
        client.delete(cls.notification_history_queue_name)
