import unittest

import pytest
from mock import (
    call,
    MagicMock,
    patch,
)
from model_bakery import baker
from django.test import override_settings

from lib.enums import (
    SMSPaidEnum,
    SMSTypeEnum,
)
from webapps.business.models import Business
from webapps.notification.models import (
    NotificationHistory,
    FreeSMSBunch,
    LimitedSMSBunch,
)
from webapps.notification.tools import (
    bulk_apply_sms_limits,
    set_sms_notification_sender,
    limit_sms_invitations_by_period,
    update_sms_invitations_per_period_count,
)


@pytest.mark.django_db
class SetSMSSenderTestCase(unittest.TestCase):

    def setUp(self):
        self.recipient_phone = '123456789'
        self.business = baker.make(Business, status=Business.Status.TRIAL)
        self.sms_type = SMSTypeEnum.INVITATION

    @patch.object(NotificationHistory, 'guess_and_set_task_type')
    def test_history_data_missing(self, guess_and_set_mock):
        history_data = {}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertEqual(guess_and_set_mock.call_count, 0)
        self.assertTrue('sender' not in history_data)

        history_data = {'task_id': 'supertaskid'}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertEqual(guess_and_set_mock.call_count, 0)

        history_data = {'business_id': 1234}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertEqual(guess_and_set_mock.call_count, 0)

        history_data = {'task_type': 'supertasktype'}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertEqual(guess_and_set_mock.call_count, 0)

        history_data = {'business_id': 1234, 'task_id': 'supertaskid'}
        set_sms_notification_sender(history_data, self.recipient_phone)
        call_count = guess_and_set_mock.call_count
        self.assertEqual(call_count, 1)

        history_data = {'business_id': 1234, 'task_type': 'supertasktype'}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertEqual(guess_and_set_mock.call_count, call_count + 1)

    @patch.object(NotificationHistory, 'task_type_to_sms_type_mapping', {})
    def test_no_sms_type(self):
        history_data = {'business_id': 1234, 'task_type': 'supertasktype'}
        set_sms_notification_sender(history_data, self.recipient_phone)
        self.assertNotIn('sender', history_data)

    @override_settings(
        FIRST_N_SMS_LIMITS={},
        TOTAL_M_SMS_LIMITS={},
    )
    def test_sms_default_values(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        )
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            cell_phone=self.recipient_phone,
        )
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_BUSINESS)
        self.assertTrue(limited_s.exists())
        self.assertEqual(limited_s.first().sms_count, 1)
        self.assertFalse(first_n.exists())

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                # Doesn't matter
                SMSTypeEnum.INVITATION: 100,
            },
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 0,
            },
        },
    )
    def test_sms_m_0(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        set_sms_notification_sender(history_data, self.recipient_phone)
        # History data sender depends on "first N" limit,
        # so we don't check it here
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        )
        self.assertTrue(limited_s.exists())
        self.assertEqual(limited_s.first().sms_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                # Doesn't matter
                SMSTypeEnum.INVITATION: 100,
            },
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: None,
            },
        },
    )
    def test_sms_m_unlimited(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        set_sms_notification_sender(history_data, self.recipient_phone)
        # History data sender depends on "first N" limit,
        # so we don't check it here
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        )
        self.assertFalse(limited_s.exists())

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                # Doesn't matter
                SMSTypeEnum.INVITATION: 100,
            },
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 5,
            },
        },
    )
    def test_sms_m_over_limit(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        LimitedSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=10,
        )
        set_sms_notification_sender(history_data, self.recipient_phone)
        # History data sender depends on "first N" limit,
        # so we don't check it here
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        self.assertEqual(limited_s.sms_count, 11)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_0(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        # If sms was sent from prepaid/postpaid limit it is always
        # "sent by business"
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_BUSINESS)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_gt_0_no_n_remaining(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            cell_phone=self.recipient_phone,
            sms_type=self.sms_type.value,
            sms_count=10,
        )
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            cell_phone=self.recipient_phone,
        ).first()
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_BUSINESS)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)
        # Wasn't marked as free so we do not save FreeSMSBunch object
        self.assertEqual(first_n.sms_count, 10)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_over_limit(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            cell_phone=self.recipient_phone,
            sms_count=11,
        )
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            cell_phone=self.recipient_phone,
        ).first()
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_BUSINESS)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)
        # Wasn't marked as free so we do not save FreeSMSBunch object
        self.assertEqual(first_n.sms_count, 11)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_gt_0_nothing_sent(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            cell_phone=self.recipient_phone,
        ).first()
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_SYSTEM)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)
        self.assertEqual(first_n.sms_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_gt_0_n_remaining(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            cell_phone=self.recipient_phone,
            sms_type=self.sms_type.value,
        )
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            cell_phone=self.recipient_phone,
            sms_type=self.sms_type.value,
        )
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_SYSTEM)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)
        # 1 per phone number
        self.assertEqual(first_n.count(), 1)
        self.assertEqual(first_n.first().sms_count, 2)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
    )
    def test_sms_n_gt_0_n_unlimited(self):
        history_data = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
        }
        # Shouldn't matter
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            cell_phone=self.recipient_phone,
            sms_type=self.sms_type.value,
        )
        set_sms_notification_sender(history_data, self.recipient_phone)
        limited_s = LimitedSMSBunch.objects.filter(
            business_id=self.business.id, sms_type=self.sms_type.value
        ).first()
        first_n = FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            cell_phone=self.recipient_phone,
            sms_type=self.sms_type.value,
        )
        self.assertEqual(history_data['sender'], NotificationHistory.SENDER_SYSTEM)
        self.assertIsNotNone(limited_s)
        self.assertEqual(limited_s.sms_count, 1)
        # 1 per phone number
        self.assertEqual(first_n.count(), 1)
        # Nothing should change
        self.assertEqual(first_n.first().sms_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 2,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: 2,
            },
        },
    )
    def test_sms_with_free_limit(self):
        history_data_1 = {
            'business_id': self.business.id,
            'task_type': NotificationHistory.TASK_TYPE__INVITATION,
            # Those are sms parts, not whole smses count
            'sms_count': 10,
        }
        history_data_2 = history_data_1.copy()
        history_data_3 = history_data_1.copy()

        free_sms_bunch = FreeSMSBunch.objects.filter(
            business_id=self.business.id, sms_type='I', cell_phone=self.recipient_phone
        )

        set_sms_notification_sender(history_data_1, self.recipient_phone)
        self.assertEqual(history_data_1['sender'], NotificationHistory.SENDER_SYSTEM)
        self.assertEqual(free_sms_bunch.count(), 1)

        set_sms_notification_sender(history_data_2, self.recipient_phone)
        self.assertEqual(history_data_2['sender'], NotificationHistory.SENDER_SYSTEM)
        self.assertEqual(free_sms_bunch.count(), 1)
        free_sms_bunch_obj = free_sms_bunch.first()
        self.assertEqual(free_sms_bunch_obj.sms_count, 2)

        set_sms_notification_sender(history_data_3, self.recipient_phone)
        self.assertEqual(history_data_3['sender'], NotificationHistory.SENDER_BUSINESS)
        self.assertEqual(free_sms_bunch.count(), 1)
        free_sms_bunch_obj = free_sms_bunch.first()
        self.assertEqual(free_sms_bunch_obj.sms_count, 2)
        self.assertEqual(free_sms_bunch_obj.cell_phone, self.recipient_phone)


class LimitSMSInvitationsTestCase(unittest.TestCase):
    """
    Only limit by period is considered here.
    All other limits are checked in BulkApplySMSLimitsTestCase.
    """

    @override_settings(SMS_INVITATION_PERIOD_LIMIT_ON=False)
    @patch('webapps.marketing.models.SMSInvitationEvent.add_multiple')
    @patch('webapps.notification.models.LimitedSMSBunch.update_limit_used')
    def test_update_sms_invitation_count_no_period_limit(
        self, total_m_update_limit_mock, add_multiple_mock
    ):
        business = MagicMock()
        phone_numbers = [1, 2, 3]
        update_sms_invitations_per_period_count(business, phone_numbers)

        # Total M limit shouldn't be updated here
        self.assertEqual(total_m_update_limit_mock.call_count, 0)
        # Setting turned off
        self.assertEqual(add_multiple_mock.call_count, 0)

    @override_settings(SMS_INVITATION_PERIOD_LIMIT_ON=True)
    @patch('webapps.marketing.models.SMSInvitationEvent.add_multiple')
    @patch('webapps.notification.models.LimitedSMSBunch.update_limit_used')
    def test_update_sms_invitation_count_period_limit(
        self, total_m_update_limit_mock, add_multiple_mock
    ):
        business = MagicMock()
        business.id = 99
        phone_numbers = [1, 2, 3]
        update_sms_invitations_per_period_count(business, phone_numbers)

        # Total M limit shouldn't be updated here
        self.assertEqual(total_m_update_limit_mock.call_count, 0)
        # Setting turned on
        self.assertEqual(
            add_multiple_mock.mock_calls,
            [call(99, phone_numbers)],
        )

    @override_settings(SMS_INVITATION_PERIOD_LIMIT_ON=False)
    @patch('webapps.marketing.models.SMSInvitationEvent.period_too_short')
    def test_limit_sms_inv_by_period_no_limit(self, too_short_mock):
        business = MagicMock()
        phone_numbers = [1, 2, 3]
        phones_result, rejected_result = limit_sms_invitations_by_period(business, phone_numbers)
        self.assertEqual(phones_result, phone_numbers)
        self.assertEqual(rejected_result, 0)
        self.assertEqual(too_short_mock.call_count, 0)

    @override_settings(SMS_INVITATION_PERIOD_LIMIT_ON=True)
    @patch('webapps.marketing.models.SMSInvitationEvent.period_too_short')
    def test_limit_sms_inv_by_period_ok(self, too_short_mock):
        business = MagicMock()
        phone_numbers = [1, 2, 3]
        too_short_mock.side_effect = lambda x, y: bool(y % 2)

        phones_result, rejected_result = limit_sms_invitations_by_period(business, phone_numbers)
        self.assertEqual(phones_result, [2])
        self.assertEqual(rejected_result, 2)
        self.assertEqual(too_short_mock.call_count, 3)


@pytest.mark.django_db
class BulkApplySMSLimitsTestCase(unittest.TestCase):
    """See workflow here:
    https://gl2.booksy.net:8443/booksy/core/wikis/SMS-flow-03.01.2020"""

    def setUp(self):
        self.phone_1 = '111111111'
        self.phone_numbers = [
            '123456789',
            '234567891',
            '345678912',
            '456789123',
            self.phone_1,
            '567891234',
            '678912345',
            '789123456',
            '891234567',
            '912345678',
        ]
        self.task_type = NotificationHistory.TASK_TYPE__INVITATION
        self.sms_type = SMSTypeEnum.INVITATION
        self.business = baker.make(Business, status=Business.Status.TRIAL)
        self.sms_body = 'nanannananananan'
        self.history_data = {
            'business_id': self.business.id,
            'task_type': self.task_type,
        }
        self.counter_mock_value = {
            'messages': 2,
        }

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 1},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 1},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    def test_no_sufficient_data_for_limits(self):
        out_phones, _ = bulk_apply_sms_limits({}, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_unlimited_m_unlimited(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        # Case 1: Nothing sent yet
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)

        # Case 2: FreeSMSBunch object present for self.phone_1
        # (shouldn't matter)
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=1,
            cell_phone=self.phone_1,
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)

        # Case 2: LimitedSMSBunch object present (shouldn't matter)
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=100
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_unlimited_m_0(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_0_m_unlimited(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        # Case 1: Prepaid/postpaid limit left: 5 parts (so 2 whole sms)
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 2)
        self.assertEqual(sms_stats_mock.call_count, 1)

        # Case 2: No prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 5, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 2)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_0_m_0(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_unlimited_m_gt_0_gt_subscription(self, counter_mock, sms_stats_mock):
        # prepaid/postpaid limit will never be touched
        counter_mock.return_value = self.counter_mock_value
        # Case 1: No LimitedSMSBunch
        # FreeSMSBunch for self.phone_1 (shouldn't matter)
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=1,
            cell_phone=self.phone_1,
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 2: LimitedSMSBunch cnt == 1
        LimitedSMSBunch.update_limit_used(self.business.id, self.sms_type, SMSPaidEnum.TRIAL)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 3: LimitedSMSBunch cnt == 10
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=9
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 4: LimitedSMSBunch cnt == 11
        # IMPORTANT ex. when settings changed
        LimitedSMSBunch.update_limit_used(self.business.id, self.sms_type, SMSPaidEnum.TRIAL)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 3},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_unlimited_m_gt_0_lt_subscription(self, counter_mock, sms_stats_mock):
        # prepaid/postpaid limit will never be touched
        counter_mock.return_value = self.counter_mock_value
        # Case 1: No LimitedSMSBunch
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 3)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 2: LimitedSMSBunch cnt == 1
        LimitedSMSBunch.update_limit_used(self.business.id, self.sms_type, SMSPaidEnum.TRIAL)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 2)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 3: LimitedSMSBunch cnt == 3
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=2
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 5: LimitedSMSBunch cnt == 10
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 1},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_gt_0_lt_subscription_m_unlimited(self, counter_mock, sms_stats_mock):
        # "1st invite per phone number always for free and beyond limits"
        counter_mock.return_value = self.counter_mock_value
        # We are in step 2.1
        # Case 1: No FreeSMSBunch for self.phone_1 yet,
        # no prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 5, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 2: FreeSMSBunch for self.phone_1, no prepaid/postpaid limit left
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=1,
            cell_phone=self.phone_1,
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 3: FreeSMSBunch for self.phone_1 more than allowed,
        # no prepaid/postpaid limit left
        FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type,
            cell_phone=self.phone_1,
        ).update(sms_count=3)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 4: FreeSMSBunch for self.phone_1, prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 5: LimitedSMSBunch object present (shouldn't matter)
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=100
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 1},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: None},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=0,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_gt_0_gt_subscription_m_unlimited(self, counter_mock, sms_stats_mock):
        # "1st invite per phone number always for free and beyond limits"
        counter_mock.return_value = self.counter_mock_value
        # We are in step 2.1
        # Case 1: No FreeSMSBunch for self.phone_1 yet,
        # no prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 5, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 2: FreeSMSBunch for self.phone_1, no prepaid/postpaid limit left
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=1,
            cell_phone=self.phone_1,
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 3: FreeSMSBunch for self.phone_1 more than allowed,
        # no prepaid/postpaid limit left
        FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type,
            cell_phone=self.phone_1,
        ).update(sms_count=3)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 4: LimitedSMSBunch object present (shouldn't matter)
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=100
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 100},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=500,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_gt_0_m_0(self, counter_mock, sms_stats_mock):
        # M == 0
        counter_mock.return_value = self.counter_mock_value
        # Case 1: No FreeSMSBunch yet,
        # prepaid/postpaid limit left, no LimitedSMSBunch objects
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 50, 500, 50)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        TOTAL_M_SMS_LIMITS={
            # 2 x 2 parts = 4 parts
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 2},
        },
        # 10 parts
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=10,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_0_m_gt_0_lt_subscription(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        # Case 1: Nothing sent yet
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 10, 0)
        # 10 phone numbers with 2-part sms message in
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        # only 2 phone numbers should be out as M == 2
        # prepaid/postpaid limit should be decreased, but it has been mocked :P
        self.assertEqual(len(out_phones), 2)
        self.assertEqual(sms_stats_mock.call_count, 1)
        # Case 2: M reached, prepaid/postpaid available
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 10, 0)
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=2
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 1)
        # Case 3: M reached and settings have been changed meanwhile,
        # prepaid/postpaid available
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=3
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 0},
        },
        TOTAL_M_SMS_LIMITS={
            # 10 x 2 parts = 20 parts
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        # 10 parts
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_0_m_gt_0_gt_subscription(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        # Case 1: Nothing sent yet
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        # 10 phone numbers with 2-part sms message in
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        # 5 prepaid/postpaid allows to send 2 whole sms messages
        # so 2 phone numbers should be out
        self.assertEqual(len(out_phones), 2)
        self.assertEqual(sms_stats_mock.call_count, 1)

    @override_settings(
        FIRST_N_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 1},
        },
        TOTAL_M_SMS_LIMITS={
            SMSPaidEnum.TRIAL: {SMSTypeEnum.INVITATION: 10},
        },
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=5,
    )
    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    @patch('lib.booksy_sms.utils.SMSCounter.count')
    def test_n_gt_0_m_gt_0(self, counter_mock, sms_stats_mock):
        counter_mock.return_value = self.counter_mock_value
        # We are in step 2.1
        # Case 1: Nothing has been sent yet
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 10)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 2: FreeSMSBunch for self.phone_1, prepaid/postpaid limit left
        FreeSMSBunch.objects.create(
            business_id=self.business.id,
            sms_type=self.sms_type.value,
            sms_count=1,
            cell_phone=self.phone_1,
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 3: FreeSMSBunch for self.phone_1, no prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 5, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 4: FreeSMSBunch for self.phone_1 more than allowed,
        # no prepaid/postpaid limit left
        FreeSMSBunch.objects.filter(
            business_id=self.business.id,
            sms_type=self.sms_type,
            cell_phone=self.phone_1,
        ).update(sms_count=3)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 5: FreeSMSBunch for self.phone_1 more than allowed,
        # prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 9)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Case 6: M almost reached, prepaid/postpaid limit left
        # sms status, sms_sent_count, sms_limit_free, sms_limit_payable
        sms_stats_mock.return_value = ('demo', 0, 5, 0)
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=3
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 7)
        self.assertEqual(sms_stats_mock.call_count, 0)
        # Free invitations are preferred
        self.assertFalse(self.phone_1 in out_phones)
        # Case 7: M reached, prepaid/postpaid limit left
        LimitedSMSBunch.update_limit_used(
            self.business.id, self.sms_type, SMSPaidEnum.TRIAL, amount=7
        )
        out_phones, _ = bulk_apply_sms_limits(self.history_data, self.phone_numbers, self.sms_body)
        self.assertEqual(len(out_phones), 0)
        self.assertEqual(sms_stats_mock.call_count, 0)
