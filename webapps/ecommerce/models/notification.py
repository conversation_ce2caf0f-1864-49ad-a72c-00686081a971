from django.contrib.postgres.fields import Array<PERSON>ield
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models

from lib.models import ArchiveModel
from lib.tools import tznow
from settings.storage import EcommerceNotificationCampaignImageStorage
from webapps.images.tools import get_ecommerce_notification_campaign_image_path


class NotificationCampaign(ArchiveModel):
    business_categories = models.ManyToManyField(
        'business.BusinessCategory',
        related_name='notification_campaigns',
        blank=True,
    )
    business_ids = ArrayField(base_field=models.CharField(), default=list, blank=True)
    message_line_1 = models.CharField()
    message_line_2 = models.CharField()
    message_line_3 = models.CharField()
    relevance = models.PositiveSmallIntegerField(
        default=1,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(4),
        ],
    )
    deeplink = models.Char<PERSON><PERSON>(max_length=255)
    start_date = models.DateTimeField(default=tznow)
    end_date = models.DateTimeField(null=True, blank=True)
    last_triggered = models.DateTimeField(null=True, blank=True)
    image = models.ImageField(
        blank=True,
        null=True,
        storage=EcommerceNotificationCampaignImageStorage(),
        upload_to=get_ecommerce_notification_campaign_image_path,
    )


class NotificationCampaignBusinessesLog(ArchiveModel):
    notification_campaign = models.ForeignKey(
        'ecommerce.NotificationCampaign',
        on_delete=models.CASCADE,
        related_name='businesses_log',
    )
    business_ids = ArrayField(base_field=models.CharField(), default=list, blank=True)
    user_ids = ArrayField(base_field=models.CharField(), default=list, blank=True)
    send_date = models.DateTimeField(default=tznow)
