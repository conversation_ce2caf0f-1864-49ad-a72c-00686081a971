# Generated by Django 4.2.18 on 2025-04-08 20:55

from django.db import migrations, models
import settings.storage
import webapps.images.tools


class Migration(migrations.Migration):

    dependencies = [
        ('ecommerce', '0002_ecommercepermission'),
    ]

    operations = [
        migrations.AddField(
            model_name='notificationcampaign',
            name='image',
            field=models.ImageField(
                blank=True,
                null=True,
                storage=settings.storage.EcommerceNotificationCampaignImageStorage(),
                upload_to=webapps.images.tools.get_ecommerce_notification_campaign_image_path,
            ),
        ),
    ]
