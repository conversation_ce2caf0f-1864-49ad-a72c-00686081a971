# Generated by Django 3.1.2 on 2021-03-02 13:58

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('third_tier_wait_list', '0002_autoupdate_queryset_as_manager_67904'),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name='waitlist',
            name='country_code',
            field=models.CharField(
                choices=[
                    ('af', 'Afghanistan'),
                    ('ax', 'Åland Islands'),
                    ('al', 'Albania'),
                    ('dz', 'Algeria'),
                    ('as', 'American Samoa'),
                    ('ad', 'Andorra'),
                    ('ao', 'Angola'),
                    ('ai', 'Anguilla'),
                    ('aq', 'Antarctica'),
                    ('ag', 'Antigua and Barbuda'),
                    ('ar', 'Argentina'),
                    ('am', 'Armenia'),
                    ('aw', 'Aruba'),
                    ('au', 'Australia'),
                    ('at', 'Austria'),
                    ('az', 'Azerbaijan'),
                    ('bs', 'Bahamas'),
                    ('bh', 'Bahrain'),
                    ('bd', 'Bangladesh'),
                    ('bb', 'Barbados'),
                    ('by', 'Belarus'),
                    ('be', 'Belgium'),
                    ('bz', 'Belize'),
                    ('bj', 'Benin'),
                    ('bm', 'Bermuda'),
                    ('bt', 'Bhutan'),
                    ('bo', 'Bolivia'),
                    ('bq', 'Bonaire, Sint Eustatius and Saba'),
                    ('ba', 'Bosnia and Herzegovina'),
                    ('bw', 'Botswana'),
                    ('bv', 'Bouvet Island'),
                    ('br', 'Brazil'),
                    ('io', 'British Indian Ocean Territory'),
                    ('bn', 'Brunei'),
                    ('bg', 'Bulgaria'),
                    ('bf', 'Burkina Faso'),
                    ('bi', 'Burundi'),
                    ('cv', 'Cabo Verde'),
                    ('kh', 'Cambodia'),
                    ('cm', 'Cameroon'),
                    ('ca', 'Canada'),
                    ('ky', 'Cayman Islands'),
                    ('cf', 'Central African Republic'),
                    ('td', 'Chad'),
                    ('cl', 'Chile'),
                    ('cn', 'China'),
                    ('cx', 'Christmas Island'),
                    ('cc', 'Cocos (Keeling) Islands'),
                    ('co', 'Colombia'),
                    ('km', 'Comoros'),
                    ('cg', 'Congo'),
                    ('cd', 'Congo (the Democratic Republic of the)'),
                    ('ck', 'Cook Islands'),
                    ('cr', 'Costa Rica'),
                    ('ci', "Côte d'Ivoire"),
                    ('hr', 'Croatia'),
                    ('cu', 'Cuba'),
                    ('cw', 'Curaçao'),
                    ('cy', 'Cyprus'),
                    ('cz', 'Czechia'),
                    ('dk', 'Denmark'),
                    ('dj', 'Djibouti'),
                    ('dm', 'Dominica'),
                    ('do', 'Dominican Republic'),
                    ('ec', 'Ecuador'),
                    ('eg', 'Egypt'),
                    ('sv', 'El Salvador'),
                    ('gq', 'Equatorial Guinea'),
                    ('er', 'Eritrea'),
                    ('ee', 'Estonia'),
                    ('sz', 'Eswatini'),
                    ('et', 'Ethiopia'),
                    ('fk', 'Falkland Islands (Malvinas)'),
                    ('fo', 'Faroe Islands'),
                    ('fj', 'Fiji'),
                    ('fi', 'Finland'),
                    ('fr', 'France'),
                    ('gf', 'French Guiana'),
                    ('pf', 'French Polynesia'),
                    ('tf', 'French Southern Territories'),
                    ('ga', 'Gabon'),
                    ('gm', 'Gambia'),
                    ('ge', 'Georgia'),
                    ('de', 'Germany'),
                    ('gh', 'Ghana'),
                    ('gi', 'Gibraltar'),
                    ('gr', 'Greece'),
                    ('gl', 'Greenland'),
                    ('gd', 'Grenada'),
                    ('gp', 'Guadeloupe'),
                    ('gu', 'Guam'),
                    ('gt', 'Guatemala'),
                    ('gg', 'Guernsey'),
                    ('gn', 'Guinea'),
                    ('gw', 'Guinea-Bissau'),
                    ('gy', 'Guyana'),
                    ('ht', 'Haiti'),
                    ('hm', 'Heard Island and McDonald Islands'),
                    ('va', 'Holy See'),
                    ('hn', 'Honduras'),
                    ('hk', 'Hong Kong'),
                    ('hu', 'Hungary'),
                    ('is', 'Iceland'),
                    ('in', 'India'),
                    ('id', 'Indonesia'),
                    ('ir', 'Iran'),
                    ('iq', 'Iraq'),
                    ('ie', 'Ireland'),
                    ('im', 'Isle of Man'),
                    ('il', 'Israel'),
                    ('it', 'Italy'),
                    ('jm', 'Jamaica'),
                    ('jp', 'Japan'),
                    ('je', 'Jersey'),
                    ('jo', 'Jordan'),
                    ('kz', 'Kazakhstan'),
                    ('ke', 'Kenya'),
                    ('ki', 'Kiribati'),
                    ('kw', 'Kuwait'),
                    ('kg', 'Kyrgyzstan'),
                    ('la', 'Laos'),
                    ('lv', 'Latvia'),
                    ('lb', 'Lebanon'),
                    ('ls', 'Lesotho'),
                    ('lr', 'Liberia'),
                    ('ly', 'Libya'),
                    ('li', 'Liechtenstein'),
                    ('lt', 'Lithuania'),
                    ('lu', 'Luxembourg'),
                    ('mo', 'Macao'),
                    ('mg', 'Madagascar'),
                    ('mw', 'Malawi'),
                    ('my', 'Malaysia'),
                    ('mv', 'Maldives'),
                    ('ml', 'Mali'),
                    ('mt', 'Malta'),
                    ('mh', 'Marshall Islands'),
                    ('mq', 'Martinique'),
                    ('mr', 'Mauritania'),
                    ('mu', 'Mauritius'),
                    ('yt', 'Mayotte'),
                    ('mx', 'Mexico'),
                    ('fm', 'Micronesia (Federated States of)'),
                    ('md', 'Moldova'),
                    ('mc', 'Monaco'),
                    ('mn', 'Mongolia'),
                    ('me', 'Montenegro'),
                    ('ms', 'Montserrat'),
                    ('ma', 'Morocco'),
                    ('mz', 'Mozambique'),
                    ('mm', 'Myanmar'),
                    ('na', 'Namibia'),
                    ('nr', 'Nauru'),
                    ('np', 'Nepal'),
                    ('nl', 'Netherlands'),
                    ('nc', 'New Caledonia'),
                    ('nz', 'New Zealand'),
                    ('ni', 'Nicaragua'),
                    ('ne', 'Niger'),
                    ('ng', 'Nigeria'),
                    ('nu', 'Niue'),
                    ('nf', 'Norfolk Island'),
                    ('kp', 'North Korea'),
                    ('mk', 'North Macedonia'),
                    ('mp', 'Northern Mariana Islands'),
                    ('no', 'Norway'),
                    ('om', 'Oman'),
                    ('pk', 'Pakistan'),
                    ('pw', 'Palau'),
                    ('ps', 'Palestine, State of'),
                    ('pa', 'Panama'),
                    ('pg', 'Papua New Guinea'),
                    ('py', 'Paraguay'),
                    ('pe', 'Peru'),
                    ('ph', 'Philippines'),
                    ('pn', 'Pitcairn'),
                    ('pl', 'Poland'),
                    ('pt', 'Portugal'),
                    ('pr', 'Puerto Rico'),
                    ('qa', 'Qatar'),
                    ('re', 'Réunion'),
                    ('ro', 'Romania'),
                    ('ru', 'Russia'),
                    ('rw', 'Rwanda'),
                    ('bl', 'Saint Barthélemy'),
                    ('sh', 'Saint Helena, Ascension and Tristan da Cunha'),
                    ('kn', 'Saint Kitts and Nevis'),
                    ('lc', 'Saint Lucia'),
                    ('mf', 'Saint Martin (French part)'),
                    ('pm', 'Saint Pierre and Miquelon'),
                    ('vc', 'Saint Vincent and the Grenadines'),
                    ('ws', 'Samoa'),
                    ('sm', 'San Marino'),
                    ('st', 'Sao Tome and Principe'),
                    ('sa', 'Saudi Arabia'),
                    ('sn', 'Senegal'),
                    ('rs', 'Serbia'),
                    ('sc', 'Seychelles'),
                    ('sl', 'Sierra Leone'),
                    ('sg', 'Singapore'),
                    ('sx', 'Sint Maarten (Dutch part)'),
                    ('sk', 'Slovakia'),
                    ('si', 'Slovenia'),
                    ('sb', 'Solomon Islands'),
                    ('so', 'Somalia'),
                    ('za', 'South Africa'),
                    ('gs', 'South Georgia and the South Sandwich Islands'),
                    ('kr', 'South Korea'),
                    ('ss', 'South Sudan'),
                    ('es', 'Spain'),
                    ('lk', 'Sri Lanka'),
                    ('sd', 'Sudan'),
                    ('sr', 'Suriname'),
                    ('sj', 'Svalbard and Jan Mayen'),
                    ('se', 'Sweden'),
                    ('ch', 'Switzerland'),
                    ('sy', 'Syria'),
                    ('tw', 'Taiwan'),
                    ('tj', 'Tajikistan'),
                    ('tz', 'Tanzania'),
                    ('th', 'Thailand'),
                    ('tl', 'Timor-Leste'),
                    ('tg', 'Togo'),
                    ('tk', 'Tokelau'),
                    ('to', 'Tonga'),
                    ('tt', 'Trinidad and Tobago'),
                    ('tn', 'Tunisia'),
                    ('tr', 'Turkey'),
                    ('tm', 'Turkmenistan'),
                    ('tc', 'Turks and Caicos Islands'),
                    ('tv', 'Tuvalu'),
                    ('ug', 'Uganda'),
                    ('ua', 'Ukraine'),
                    ('ae', 'United Arab Emirates'),
                    ('gb', 'United Kingdom'),
                    ('um', 'United States Minor Outlying Islands'),
                    ('us', 'United States of America'),
                    ('uy', 'Uruguay'),
                    ('uz', 'Uzbekistan'),
                    ('vu', 'Vanuatu'),
                    ('ve', 'Venezuela'),
                    ('vn', 'Vietnam'),
                    ('vg', 'Virgin Islands (British)'),
                    ('vi', 'Virgin Islands (U.S.)'),
                    ('wf', 'Wallis and Futuna'),
                    ('eh', 'Western Sahara'),
                    ('ye', 'Yemen'),
                    ('zm', 'Zambia'),
                    ('zw', 'Zimbabwe'),
                ],
                max_length=2,
            ),
        ),
    ]
