from django.db import models

from lib.models import ChangeArchivedModel
from webapps.security_blacklist.enums import BlacklistType


class BlacklistItem(ChangeArchivedModel):
    name = models.CharField(max_length=255)
    blacklist_type = models.CharField(max_length=20, choices=BlacklistType.choices, null=True)
    value = models.CharField(max_length=255, unique=True)
    enabled = models.BooleanField(default=True)

    @classmethod
    def get_enabled_values(cls, blacklist_type):
        return set(
            cls.objects.filter(enabled=True, blacklist_type=blacklist_type).values_list(
                'value', flat=True
            )
        )

    def __str__(self):
        return f"{self.get_blacklist_type_display()}: {self.value}"
