from rest_framework import serializers

from lib.elasticsearch.consts import SECONDARY_CATEGORY_SCORE
from webapps.utt.models import Treatment


class BusinessCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Treatment
        fields = [
            'id',
            'name',
            'internal_name',
            'score',
        ]

    id = serializers.IntegerField()
    name = serializers.CharField()
    internal_name = serializers.CharField()
    score = serializers.SerializerMethodField()

    def get_score(self, instance):
        categories_score = self.context.get('categories_score', {})
        booking_rate = categories_score.get(instance.id, 0)
        return booking_rate * SECONDARY_CATEGORY_SCORE


class BusinessTreatmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Treatment
        fields = [
            'id',
            'name',
            'internal_name',
            'level',
        ]

    id = serializers.IntegerField()
    name = serializers.CharField()
    internal_name = serializers.CharField()
    level = serializers.IntegerField()
