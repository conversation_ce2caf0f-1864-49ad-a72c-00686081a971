from decimal import Decimal

from django.db import migrations
from django.utils.translation import gettext

from webapps.warehouse.volume_measures import VolumeMeasureEnum


def migrate_pos_product(apps, schema_editor):
    db_name = schema_editor.connection.alias
    Business = apps.get_model('business', 'Business')
    Commodity = apps.get_model('warehouse', 'Commodity')
    CommodityCategory = apps.get_model('warehouse', 'CommodityCategory')
    Product = apps.get_model("pos", "Product")
    ProductCategory = apps.get_model('pos', 'ProductCategory')
    TaxRate = apps.get_model("pos", "TaxRate")
    Warehouse = apps.get_model('warehouse', 'Warehouse')
    Supplier = apps.get_model('warehouse', 'Supplier')
    VolumeMeasure = apps.get_model('warehouse', 'VolumeMeasure')

    business_qs = Business.objects.using(db_name).exclude(
        # Business.Status.B_LISTING, Business.Status.VENUE
        status__in=('C', 'V'),
    )

    businesses_with_warehouses = (
        Warehouse.objects.using(db_name)
        .filter(
            is_default=True,
        )
        .values_list(
            'business__id',
            flat=True,
        )
        .distinct()
    )
    businesses_without_warehouses = (
        business_qs.exclude(
            id__in=businesses_with_warehouses,
        )
        .values_list('id', flat=True)
        .distinct()
    )
    new_default_warehouses = []
    new_default_suppliers = []
    for business_id in businesses_without_warehouses:
        wh = Warehouse(
            business_id=business_id,
            is_default=True,
            name=gettext('Default Warehouse'),
        )
        new_default_warehouses.append(wh)
    Warehouse.objects.using(db_name).bulk_create(new_default_warehouses, 1000)

    businesses_with_suppliers = (
        Supplier.objects.using(db_name)
        .values_list(
            'business__id',
            flat=True,
        )
        .distinct()
    )
    businesses_without_suppliers = (
        business_qs.exclude(
            id__in=businesses_with_suppliers,
        )
        .values_list('id', flat=True)
        .distinct()
    )
    for business_id in businesses_without_suppliers:
        supplier = Supplier(
            business_id=business_id,
            name=gettext('Default Supplier'),
        )
        new_default_suppliers.append(supplier)
    Supplier.objects.using(db_name).bulk_create(new_default_suppliers, 1000)

    # if somebody migrate Warehouse by
    # old migration (0162_pos_product_to_warehouse_commodity)
    excluded_product_ids = list(
        Commodity.objects.using(db_name).values_list(
            'id',
            flat=True,
        )
    )

    # if somebody migrate Warehouse by
    # old migration (0162_pos_product_to_warehouse_commodity)
    excluded_categories = CommodityCategory.objects.using(db_name).values_list(
        'id',
        flat=True,
    )
    categories_to_migrate = (
        ProductCategory.objects.using(db_name)
        .exclude(
            id__in=excluded_categories,
        )
        .select_related(
            'pos',
        )
    )

    new_commodity_categories = []
    for product_category in categories_to_migrate.iterator():
        commodity_category = CommodityCategory(
            id=product_category.id,
            business_id=product_category.pos.business_id,
            name=product_category.name,
        )
        new_commodity_categories.append(commodity_category)
    categories = CommodityCategory.objects.using(db_name).bulk_create(new_commodity_categories)
    commodity_categories_cache = {c.id: c for c in categories}

    new_commodities = []

    default_volume_measure = (
        VolumeMeasure.objects.using(db_name)
        .filter(
            label=VolumeMeasureEnum.PIECE,
            standard=True,
        )
        .first()
    )

    products_to_migrate = (
        Product.objects.using(db_name)
        .exclude(
            id__in=excluded_product_ids,
        )
        .select_related(
            'pos',
        )
    )
    default_product_tax_rate_for_pos = {
        tax_rate.pos_id: tax_rate
        for tax_rate in TaxRate.objects.using(db_name).filter(
            pos_id__in=products_to_migrate.values('pos_id'),
            default_for_product=True,
            deleted__isnull=True,
        )
    }
    tax_rate_cache = {}

    for product in products_to_migrate.iterator():
        pos = product.pos

        tax_rate_obj = tax_rate_cache.get((pos.id, product.tax_rate))
        if not tax_rate_obj:
            default_tax_rate = default_product_tax_rate_for_pos.get(pos.id)
            tax_rate_obj = (
                TaxRate.objects.using(db_name)
                .filter(
                    rate=product.tax_rate,
                    pos_id=pos.id,
                    deleted__isnull=True,
                )
                .first()
                or default_tax_rate
            )
            tax_rate_cache[(pos.id, product.tax_rate)] = tax_rate_obj

        new_category = None
        if product.category_id:
            new_category = commodity_categories_cache.get(product.category_id)

        tax_rate = tax_rate_obj.rate or Decimal(0) if tax_rate_obj else Decimal(0)
        new_commodity = Commodity(
            id=product.id,
            business_id=product.pos.business_id,
            name=product.name,
            tax_rate=tax_rate_obj,
            tax=tax_rate,
            archived=not product.active,
            product_code=product.sku,
            order=product.order,
            photo=product.photo,
            category=new_category,
            total_pack_capacity=1,  # just because we need some default
            volume_unit=default_volume_measure,
            product_type='r',  # Commodity.TYPE_RETAIL
            deleted=product.deleted,
        )

        # 'E' == POS_TAX_MODE__EXCLUDED tax mode
        # can't use POS.POS_TAX_MODE__EXCLUDED at migration
        # because it's not visible by apps.get_model
        if product.pos.product_tax_mode == 'E':
            net_price = product.item_price or Decimal(0)
            gross_price = net_price + net_price * tax_rate / 100
            new_commodity.net_price = net_price
            new_commodity.gross_price = gross_price
        else:
            gross_price = product.item_price or Decimal(0)
            net_price = gross_price / (1 + tax_rate / 100)
            new_commodity.gross_price = gross_price
            new_commodity.net_price = net_price
        new_commodities.append(new_commodity)
    Commodity.objects.using(db_name).bulk_create(new_commodities, 1000)


def migrate_pos_product_stock_level(apps, schema_editor):
    db_name = schema_editor.connection.alias
    Commodity = apps.get_model('warehouse', 'Commodity')
    CommodityStockLevel = apps.get_model('warehouse', 'CommodityStockLevel')
    Product = apps.get_model("pos", "Product")
    Warehouse = apps.get_model('warehouse', 'Warehouse')
    stock_level_remaining_volume_max_value = 99999999  # 99,999,999.00

    commodities_ids = Commodity.objects.using(db_name).values_list('id', flat=True)
    products_to_migrate = (
        Product.objects.using(db_name)
        .filter(
            id__in=commodities_ids,
            stock_id__isnull=False,
            pos_id__isnull=False,
            stock__current_quantity__gt=0,
        )
        .select_related(
            'stock',
            'pos',
        )
    )

    warehouse_for_business_cache = {}
    commodity_stock_levels = []
    for product in products_to_migrate.iterator():
        current_quantity = product.stock.current_quantity
        if current_quantity > stock_level_remaining_volume_max_value:
            # CommodityStockLevel.remaining_volume has 8 digits while
            # Product.stock.current_quantity is IntegerField which is unlimited
            # in python (but limited in postgres to max int value) and may be
            # much more than 8 digits (99,999,999.00)
            current_quantity = stock_level_remaining_volume_max_value

        business_id = product.pos.business_id
        warehouse_id = warehouse_for_business_cache.get(business_id)
        if not warehouse_id:
            default_warehouse, _ = Warehouse.objects.get_or_create(
                business_id=business_id,
                is_default=True,
            )
            warehouse_for_business_cache[business_id] = default_warehouse.id
            warehouse_id = default_warehouse.id

        commodity_id = (
            Commodity.objects.using(db_name)
            .filter(id=product.id)
            .values_list('id', flat=True)
            .first()
        )
        if not commodity_id:
            continue
        commodity_stock = CommodityStockLevel(
            commodity_id=commodity_id,
            remaining_volume=Decimal(current_quantity),
            maximum_packages=1,  # we just need some default
            warehouse_id=warehouse_id,
        )
        commodity_stock_levels.append(commodity_stock)
    CommodityStockLevel.objects.using(db_name).bulk_create(commodity_stock_levels, 1000)


def reverse_migrate_pos_product(apps, schema_editor):
    db_name = schema_editor.connection.alias
    Product = apps.get_model("pos", "Product")
    Commodity = apps.get_model('warehouse', 'Commodity')
    ProductCategory = apps.get_model('pos', 'ProductCategory')
    CommodityCategory = apps.get_model('warehouse', 'CommodityCategory')
    WarehouseDocumentRow = apps.get_model('warehouse', 'WarehouseDocumentRow')
    WarehouseDocument = apps.get_model('warehouse', 'WarehouseDocument')
    CommodityStockLevel = apps.get_model('warehouse', 'CommodityStockLevel')

    product_categories = ProductCategory.objects.using(db_name)

    product_ids = Product.objects.using(db_name).values_list('id', flat=True)
    categories_ids = product_categories.values_list('id', flat=True)

    document_rows = WarehouseDocumentRow.objects.using(db_name).filter(
        commodity_id__in=product_ids,
    )
    documents = WarehouseDocument.objects.using(db_name).filter(rows__in=document_rows)
    document_rows.delete()
    documents.delete()

    CommodityStockLevel.objects.using(db_name).filter(commodity_id__in=product_ids).delete()
    Commodity.objects.using(db_name).filter(id__in=product_ids).delete()
    CommodityCategory.objects.using(db_name).filter(id__in=categories_ids).delete()


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ('warehouse', '0054_wholesaler_changes'),
    ]

    operations = [
        migrations.RunPython(
            migrate_pos_product,
            reverse_code=reverse_migrate_pos_product,
        ),
        migrations.RunSQL(
            """SELECT setval('warehouse_commodity_id_seq'::regclass, (SELECT MAX(id) + 1000 FROM warehouse_commodity));""",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            """SELECT setval('warehouse_commoditycategory_id_seq'::regclass, (SELECT MAX(id) + 1000 FROM warehouse_commoditycategory));""",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunPython(
            migrate_pos_product_stock_level,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
