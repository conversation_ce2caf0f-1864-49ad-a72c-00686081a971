# pylint: disable=cyclic-import
import datetime
import functools
import json
import math
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

import django
from dateutil.parser import parse
from django.db.models import JSONField
from django.core import serializers, validators
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
)
from django.db import models
from django.db import transaction
from django.db.models import (
    Case,
    F,
    IntegerField,
    ManyToManyRel,
    Q,
    Max,
    Subquery,
    Sum,
    When,
)
from django.db.models.functions import (
    Cast,
    Coalesce,
    Greatest,
    Least,
)
from django.db.models.signals import (
    post_delete,
    post_save,
    m2m_changed,
)
from django.dispatch import receiver
from django.template.defaultfilters import truncatechars
from django.utils.functional import cached_property
from django.utils.html import escape, format_html
from django.utils.translation import (
    gettext as _,
    gettext_lazy,
)
from django_countries.fields import CountryField

from lib.admin_helpers import admin_link
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import ESDocMixin, ESJSONEncoder
from lib.enums import StrChoicesEnum
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.models import (
    ArchiveManager,
    ArchiveModel,
    ArchiveQuerySet,
    AutoUpdateManager,
    AutoUpdateQuerySet,
)
from lib.rivers import River
from lib.tools import (
    tznow,
    switch_locale,
)
from webapps.booking.models import SubBooking
from webapps.images.enums import ImageTypeEnum
from webapps.images.mixins import PhotoModelMixin
from webapps.sequencing_number.models import SequenceRecord  # pylint: disable=cyclic-import
from webapps.sequencing_number.utils import create_sequence_record
from webapps.user.models import User


class AdminLinkMixin:

    @property
    def admin_id_link(self):
        return format_html(
            '<a href="{}">{}</a>', admin_link(self), truncatechars(escape(self), 100)
        )


def set_self_changed_by_document(func):
    """Decorator that will set "document_id" and "document_type" attributes
    on self if they were provided in kwargs.
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        self = args[0]
        if 'document_id' in kwargs:
            self.document_id = kwargs.pop('document_id')
        if 'document_type' in kwargs:
            self.document_type = kwargs.pop('document_type')
        return func(*args, **kwargs)

    return wrapper


class HistoryChangeModel(AdminLinkMixin, models.Model):
    NOT_DISPLAYED_HISTORY = ['updated']
    EMPTY_FIELD_VALUES = (None, '', [])

    history_change = JSONField(blank=True, null=True, default=list)

    refresh_before_serialize_history = False
    history_fields_refresh = None  # should be None or List with fields names

    class Meta:
        abstract = True
        get_latest_by = "updated"

    def __init__(self, *args, **kwargs):
        self.operator_id = kwargs.pop('operator_id', None)
        self.document_id = kwargs.pop('document_id', None)
        self.document_type = kwargs.pop('document_type', None)
        super().__init__(*args, **kwargs)

    def update_history(self, m2m_fields_only=False):
        """
        CAUTION!!! THIS METHOD DO NOT SAVE HISTORY CHANGES!!!
         ITS ONLY UPDATING EXISTING INSTANCE
        """
        history_row = self.get_difference_fields_serialized_to_json(m2m_fields_only)
        if {'updated'}.issuperset(set(history_row['change'].keys())):
            return  # ignore empty rows
        if history_row and self.history_change and m2m_fields_only:
            last_row = self.history_change[-1]
            if last_row['_operator'] != history_row['_operator']:
                return
            timestamp = parse(last_row['timestamp'])
            if timestamp < tznow() - timedelta(seconds=5):
                # pylint: disable=no-member
                self.history_change.append(history_row)
            else:
                self.history_change[-1]['change'].update(**history_row['change'])
        elif history_row:
            self.history_change.append(history_row)  # pylint: disable=no-member

    def save_history(self, m2m_fields_only=False):
        self.update_history(m2m_fields_only)
        self.save(update_fields=['history_change'])

    def get_difference_fields_serialized_to_json(self, m2m_fields_only=False):
        if self.refresh_before_serialize_history:
            self.refresh_from_db(fields=self.history_fields_refresh)
        serialized_object = serializers.serialize('python', [self])
        fields = serialized_object[0]['fields']
        # magic making django model raw python dict with primitive types
        current_state = json.loads(json.dumps(fields, cls=ESJSONEncoder))
        current_state.pop('history_change', None)
        old_state = self.get_old_state()
        difference = current_state
        if old_state:
            difference = {}
            all_keys = set(current_state.keys()) | set(old_state.keys())
            for key in all_keys:
                if m2m_fields_only and not self.is_field_m2m(key):
                    continue
                old_value = old_state.get(key)
                new_value = current_state.get(key)
                if old_value != new_value:
                    difference[key] = new_value

        operator_id = getattr(self, 'operator_id', None)
        user = None
        if operator_id:
            user = User.objects.get(id=operator_id)

        result = {
            '_state': current_state,
            '_operator': self._get_operator_details(operator_id, user),
            'change': self.get_change_for_object(difference, old_state),
            'timestamp': tznow().isoformat(),
        }
        if self.document_type:
            result.setdefault('_document', {})['document_type'] = self.document_type
        if self.document_id:
            result.setdefault('_document', {})['document_id'] = self.document_id
        return result

    @staticmethod
    def _get_operator_details(operator_id, user):
        return {
            'operator_id': operator_id,
            'email': user.email if user else None,
            'full_name': user.full_name if user else None,
        }

    def get_old_state(self):
        if not self.history_change:
            return {}
        return self.history_change[-1].get('_state') or {}

    def get_change_for_object(self, difference, old_state):
        change = {}
        for key, _ in difference.items():
            change[key] = self.get_field_history(key, difference, old_state)
        return json.loads(json.dumps(change, cls=ESJSONEncoder))

    def get_field_history(self, key, difference, old_state):
        def get_names(values, related_model):
            names = []
            values = values if isinstance(values, list) else (values,)
            for value in values:
                name = self.get_related_object_name(related_model, value)
                if name:
                    names.append(name)
            return ', '.join(names)

        new_value = difference.get(key)
        old_value = old_state.get(key)
        return_value = {
            'new_value': new_value,
            'old_value': old_value,
        }
        related_field = self._my_fields_map.get(key)
        if related_field and related_field.is_relation:
            related_model = related_field.related_model
            if hasattr(related_model, 'history_name') and (old_value or new_value):
                return_value['old_name'] = get_names(old_value, related_model)
                return_value['new_name'] = get_names(new_value, related_model)
        return return_value

    @staticmethod
    def get_related_object_name(related_model, object_id):
        if not object_id:
            return ''
        related_object = related_model.objects.filter(id=object_id).first()
        if not related_object:
            return ''
        if hasattr(related_object, 'history_name'):
            return related_object.history_name
        return ''

    @cached_property
    def _my_fields_map(self):
        # pylint: disable=redefined-outer-name
        fields_map = {}
        for field in self._meta.fields:
            fields_map[field.name] = field
        for field in self._meta.many_to_many:
            fields_map[field.name] = field
        return fields_map

    @property
    def history_name(self):
        return ''

    def is_field_m2m(self, key):
        mapped_field = self._my_fields_map.get(key)
        if mapped_field and mapped_field.is_relation:
            many_to_many = isinstance(mapped_field.remote_field, ManyToManyRel)
            return many_to_many
        return False

    def get_history_changes_list(self, only_stock_levels=False):
        result = []
        if not self.history_change:  # create initial history change row
            self.save_history()
        for history_change in self.history_change:
            if isinstance(history_change['change'], dict):
                change = history_change['change']
            else:
                change = json.loads(history_change['change'])
            if not change or only_stock_levels and not change.get('remaining_volume'):
                continue
            for not_display in self.NOT_DISPLAYED_HISTORY:
                change.pop(not_display, None)
            change_working_copy = dict(change)
            for key, key_change in change_working_copy.items():
                new_value = key_change.get('new_value')
                old_value = key_change.get('old_value')
                if new_value in self.EMPTY_FIELD_VALUES and old_value in self.EMPTY_FIELD_VALUES:
                    change.pop(key)
            if not change:  # ignore empty rows
                continue
            result.append(
                {
                    'change': change,
                    'operator': history_change['_operator'],
                    'timestamp': history_change['timestamp'],
                    '_document': history_change.get('_document'),
                }
            )
        result.reverse()
        return result


class Supplier(HistoryChangeModel, ArchiveModel):
    name = models.CharField(max_length=200)
    business = models.ForeignKey(
        'business.Business',
        related_name='suppliers',
        on_delete=models.CASCADE,
    )
    tax_id_number = models.CharField(max_length=50, blank=True)
    email = models.EmailField(max_length=75, blank=True)
    phone = BooksyPhoneNumberField(blank=True)
    description = models.TextField(blank=True)

    country = CountryField(blank=True, null=True)
    zip_code = models.CharField(max_length=25, blank=True)
    city = models.CharField(max_length=100, blank=True)
    address = models.CharField(max_length=150, blank=True)
    archived = models.BooleanField(default=False)

    ecommerce_supplier_id = models.UUIDField(null=True)

    class Meta:
        ordering = ['name']

    def __repr__(self):
        return f'<Supplier: {self.id}>'

    def __str__(self):
        return f'Supplier [{self.id}] "{self.name}"'

    @property
    def history_name(self):
        return self.name

    @classmethod
    def create_default(cls, business):
        Supplier.objects.get_or_create(
            business=business,
            name=_('Default Supplier'),
        )


class WarehouseDocumentType(StrChoicesEnum):
    WZ = 'WZ', gettext_lazy('Stock Request Confirmation')  # Wydanie zewnętrzne
    RW = 'RW', gettext_lazy('Internal Product Expenditure')  # Rozchód wewnętrzny
    MM = 'MM', gettext_lazy('Internal Stock Transfer')  # Przesunięcie międzymagazynowe
    PM = 'PM', gettext_lazy('Stock Intake Confirmation')  # Przyjęcie wewnętrzne

    PZ = 'PZ', gettext_lazy('Supply')  # Przyjęcie zewnętrzne (dostawa)
    ORD = 'ORD', gettext_lazy('Stock Order')  # Zamówienie

    INW = 'INW', gettext_lazy('Stocktake Record')  # Dokument inwentaryzacji


class BaseWarehouseDocumentQuerySet(AutoUpdateQuerySet):

    def from_business(self, business):
        return self.filter(
            Q(supply__business=business)
            | Q(warehousedocument__warehouse__business=business)
            | Q(stocktakingdocument__warehouse__business=business),
        )


class BaseWarehouseDocumentManager(AutoUpdateManager.from_queryset(BaseWarehouseDocumentQuerySet)):
    pass


class BaseWarehouseDocument(ArchiveModel):
    """Abstract base class for warehouse documents.
    Provides single point of retrieving diverse document models.
    Handles document numeration (sequence numbering).
    """

    type = models.CharField(max_length=3, choices=WarehouseDocumentType.choices())
    issue_date = models.DateField(default=datetime.date.today)
    issuing_staffer = models.ForeignKey(
        'business.Resource',
        on_delete=models.PROTECT,
        related_name='issued_documents',
        blank=True,
        null=True,
    )
    confirming_staffer = models.ForeignKey(
        'business.Resource',
        on_delete=models.PROTECT,
        related_name='confirmed_documents',
        blank=True,
        null=True,
    )
    manually_assigned_number = models.CharField(
        max_length=50,
        default=None,
        null=True,
        blank=True,
    )
    note = models.TextField(blank=True, null=True)

    objects = BaseWarehouseDocumentManager()

    def get_business_id(self):
        # pylint: disable=maybe-no-member
        if hasattr(self, 'supply'):
            return self.supply.business_id
        if hasattr(self, 'warehousedocument'):
            return self.warehousedocument.warehouse.business_id
        if hasattr(self, 'stocktakingdocument'):
            return self.stocktakingdocument.warehouse.business_id
        raise NotImplementedError()

    def get_sequence_record(self):
        return SequenceRecord.objects.filter(
            business_id=self.get_business_id(),
            type=self.type,
            related_document_id=self.id,
        ).first()

    @property
    def number(self):
        if self.manually_assigned_number:
            return self.manually_assigned_number
        sequence_record = self.get_sequence_record()
        if not sequence_record:
            return ''
        return sequence_record.assigned_number

    @transaction.atomic
    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        # pylint: disable=unidiomatic-typecheck
        if not self.id and type(self) == BaseWarehouseDocument:
            raise ValueError('Creating BaseWarehouseDocument instances is forbidden')
        super().save(force_insert, force_update, using, update_fields)
        only_history_change = (
            {'history_change', 'updated'}.issuperset(set(update_fields)) if update_fields else False
        )
        if not self.number and not only_history_change:
            create_sequence_record(
                business_id=self.get_business_id(),
                sequence_type=self.type,
                related_instance_id=self.id,
            )

    @property
    def history_name(self):
        return self.number


class ShippingDetails(ArchiveModel):
    name = models.CharField(max_length=100, blank=True)
    net_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    gross_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    tax_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )

    objects = ArchiveManager()
    all_objects = models.Manager()


class Supply(HistoryChangeModel, BaseWarehouseDocument):
    """This class represents documents issued for a particular Supplier:
     - order (ORD)
     - supply (PZ)

    Note that WarehouseDocument represents documents issued within a
    particular Warehouse.
    """

    available_types = (
        WarehouseDocumentType.PZ,
        WarehouseDocumentType.ORD,
    )

    business = models.ForeignKey(
        'business.Business',
        related_name='supplies',
        on_delete=models.CASCADE,
    )
    supplier = models.ForeignKey(
        Supplier,
        related_name='supplies',
        on_delete=models.PROTECT,
    )
    supplier_name = models.CharField(max_length=200, blank=True)
    numbers_from_suppliers = models.CharField(max_length=100, blank=True)

    # Supply may have a reference to its order (for other field remains blank)
    order = models.OneToOneField(
        'self',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='supply',
    )

    shipping_details = models.OneToOneField(
        ShippingDetails,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    # Data from Ecommerce passed via PubSub
    ecommerce_order_id = models.UUIDField(null=True)
    ecommerce_order_created_at = models.DateTimeField(null=True, blank=True)
    supplier_order_id = models.CharField(max_length=100, blank=True)
    billing_address = models.CharField(max_length=300, blank=True)
    shipping_address = models.CharField(max_length=300, blank=True)
    user = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    tax_id = models.CharField(max_length=50, blank=True)
    phone = BooksyPhoneNumberField(blank=True)

    def get_business_id(self):
        return self.business_id

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if (
            self.id
            and self.type == WarehouseDocumentType.PZ
            and update_fields != ['history_change']
        ):
            raise ValueError(_('External reception document cannot be modified'))
        if self.type not in self.available_types:
            raise ValueError(f'Invalid type: {self.type}. Must be one of {self.available_types}')
        return super().save(force_insert, force_update, using, update_fields)

    def send_email_to_supplier(self):
        # pylint: disable=cyclic-import
        from webapps.warehouse.reports import OrderSummary

        if not self.supplier.email:
            return False
        order_summary = OrderSummary(self)
        # TODO: ECOMM-283 disable for ecommerce orders
        order_summary.send_to_supplier()

    @transaction.atomic
    def soft_delete(self):
        if self.type == WarehouseDocumentType.PZ:
            raise ValueError(_('External reception document cannot be deleted'))
        for row in self.rows.all():
            row.soft_delete()
        super().soft_delete()

    @property
    def total_net_price(self):
        """Returns total net price of all rows in this supply with shipping if present"""
        rows_net_price = sum(row.net_price * row.quantity for row in self.rows.all())
        if self.shipping_details and self.shipping_details.net_price:
            rows_net_price += self.shipping_details.net_price
        return rows_net_price

    @property
    def total_gross_price(self):
        """Returns total gross price of all rows in this supply with shipping if present"""
        rows_gross_price = sum(row.gross_price * row.quantity for row in self.rows.all())
        if self.shipping_details and self.shipping_details.gross_price:
            rows_gross_price += self.shipping_details.gross_price
        return rows_gross_price

    @property
    def total_tax_split(self):
        """Returns total tax split per tax rate"""
        tax_split_data = {}
        if self.pk is not None:
            for row in self.rows.all():
                tax_rate_data = tax_split_data.setdefault(str(row.tax_rate), {})
                tax_rate_data['gross_total_cost'] = (
                    tax_rate_data.get('gross_total_cost', 0) + row.gross_price * row.quantity
                )
                tax_rate_data['net_total_cost'] = (
                    tax_rate_data.get('net_total_cost', 0) + row.net_price * row.quantity
                )
                tax_rate_data['tax_total_cost'] = (
                    tax_rate_data.get('tax_total_cost', 0) + row.tax * row.quantity
                )
            if self.shipping_details and self.shipping_details.gross_price:
                tax_rate_data = tax_split_data.setdefault(str(self.shipping_details.tax_rate), {})
                tax_rate_data['gross_total_cost'] = (
                    tax_rate_data.get('gross_total_cost', 0) + self.shipping_details.gross_price
                )
                tax_rate_data['net_total_cost'] = (
                    tax_rate_data.get('net_total_cost', 0) + self.shipping_details.net_price
                )
                tax_rate_data['tax_total_cost'] = tax_rate_data.get('tax_total_cost', 0) + (
                    self.shipping_details.gross_price - self.shipping_details.net_price
                )
        return tax_split_data

    def __str__(self):
        truncated_name = truncatechars(self.supplier_name, 100)
        return f'Supply [{self.id}] "{truncated_name}"'


class SupplyRow(HistoryChangeModel, ArchiveModel):
    supply = models.ForeignKey(
        Supply,
        related_name='rows',
        on_delete=models.CASCADE,
    )
    commodity = models.ForeignKey(
        'warehouse.Commodity',
        on_delete=models.PROTECT,
    )
    commodity_name = models.CharField(max_length=255, blank=True)
    quantity = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
    )
    net_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    gross_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    tax_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    warehouse = models.ForeignKey(
        'warehouse.Warehouse',
        on_delete=models.PROTECT,
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def _alter_stock_level(self):
        if self.supply.type == WarehouseDocumentType.ORD:
            raise ValueError(_('Order cannot alter stock levels'))
        self.commodity.operator_id = self.operator_id
        self.commodity.increase_packages(self.warehouse, self.quantity)

    @transaction.atomic
    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if (
            self.id
            and self.supply.type == WarehouseDocumentType.PZ
            and update_fields != ['history_change']
        ):
            # Edition of PZ documents is forbidden unless only the
            # history_change field is being modified - then it's allowed
            raise ValueError(_('Supply row cannot be modified'))

        if self.supply.type == WarehouseDocumentType.PZ:
            if not self.id:
                # Supply row is being created -> we should alter stock levels
                self._alter_stock_level()
            # Supply documents update commodity current purchase price
            if self.net_price:
                self.commodity.current_net_purchase_price = self.net_price
                self.commodity.operator_id = self.operator_id
                self.commodity.save(
                    update_fields=['current_net_purchase_price'],
                )

        if (supplier := self.supply.supplier) and supplier not in self.commodity.suppliers.all():
            self.commodity.suppliers.add(supplier)

        super().save(force_insert, force_update, using, update_fields)

    @transaction.atomic
    def soft_delete(self):
        if self.supply.type == WarehouseDocumentType.PZ:
            raise ValueError(_('Supply row cannot be deleted'))
        super().soft_delete()


@receiver(post_delete, sender=SupplyRow)
def supply_row_post_delete(sender, instance, **kwargs):  # pylint: disable=unused-argument
    if instance.supply.type == WarehouseDocumentType.PZ:
        instance.commodity.decrease_packages(instance.warehouse, instance.quantity)


class Barcode(HistoryChangeModel, ArchiveModel):
    HOLD_OLD_STATE = False
    CODE39 = 'code39'
    CODE128 = 'code128'
    EAN2 = 'ean2'
    EAN5 = 'ean5'
    EAN8 = 'ean8'
    EAN13 = 'ean13'
    UPC = 'upc'
    ITF14 = 'itf14'
    MSI = 'msi'
    PHARMACODE = 'pharmacode'
    CODABAR = 'codabar'

    BARCODE_TYPES = [
        (CODE39, 'CODE39'),
        (CODE128, 'CODE128'),
        (EAN2, 'EAN-2'),
        (EAN5, 'EAN-5'),
        (EAN8, 'EAN-8'),
        (EAN13, 'EAN-13'),
        (UPC, 'UPC'),
        (ITF14, 'ITF-14'),
        (MSI, 'MSI'),
        (PHARMACODE, 'Pharmacode'),
        (CODABAR, 'Codabar'),
    ]

    code = models.CharField(max_length=50, default='', null=True, blank=True)
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
    )
    type = models.CharField(
        max_length=50,
        default='ean13',
        choices=BARCODE_TYPES,
    )

    @property
    def history_name(self):
        return self.code


class VolumeMeasure(HistoryChangeModel, ArchiveModel):
    HOLD_OLD_STATE = False
    label = models.CharField(max_length=50, default='', null=True, blank=True)
    businesses = models.ManyToManyField(
        'business.Business',
        related_name='measures',
        blank=True,
    )
    symbol = models.CharField(max_length=50, default='', null=True, blank=True)
    standard = models.BooleanField(default=False)  # internal DB field

    def __str__(self):
        return f"{self.label} ({self.symbol})"

    @property
    def history_name(self):
        return self.label

    @classmethod
    @switch_locale
    def get_business_measures(
        cls,
        business_id,
        language=None,  # pylint: disable=unused-argument
    ):
        measures = {}
        for measure in VolumeMeasure.objects.filter(businesses__id=business_id):
            measures[_(measure.label).strip().lower()] = measure.label
            measures[measure.label.strip().lower()] = measure.label
            measures[measure.symbol.strip().lower()] = measure.label

        return measures

    @classmethod
    def add_default_measures(cls, business):
        if not VolumeMeasure.objects.filter(
            businesses=business,
        ).exists():
            standard_measures = VolumeMeasure.objects.filter(
                standard=True,
            )
            business.measures.add(*standard_measures)


class Wholesaler(AdminLinkMixin, ArchiveModel):
    name = models.CharField(max_length=200)
    short_name = models.CharField(max_length=15)
    supplier = models.CharField(max_length=200)
    brand = models.CharField(max_length=100)
    tax_id_number = models.CharField(max_length=50, blank=True)
    email = models.EmailField(max_length=75, blank=True)
    phone = BooksyPhoneNumberField(blank=True)
    description = models.TextField(blank=True)

    country = CountryField(blank=True, null=True)
    zip_code = models.CharField(max_length=25, blank=True)
    city = models.CharField(max_length=100, blank=True)
    address = models.CharField(max_length=150, blank=True)
    default_measure = models.ForeignKey(
        VolumeMeasure,
        related_name='wholesalers',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    default_tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[
            validators.MinValueValidator(0),
            validators.MaxValueValidator(100),
        ],
    )
    commodity_import_col_category = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Category col name in import file',
    )
    commodity_import_col_subcategory = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Subcategory col name in import file',
    )
    commodity_import_col_name = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Commodity name col name in import file',
    )
    commodity_import_col_code = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Product code col name in import file',
    )
    commodity_import_col_net_price = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Net price col name in import file',
    )
    commodity_import_col_tax_rate = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Tax rate col name in import file',
    )
    commodity_import_col_barcode = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Barcode col name in import file',
    )
    commodity_import_col_barcode_type = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Barcode type col name in import file',
    )
    commodity_import_col_total_pack_capacity = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Total pack capacity col name in import file',
    )
    commodity_import_col_volume_unit = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Volume unit col name in import file',
    )
    commodity_import_col_description = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        verbose_name='Description col name in import file',
    )

    ecommerce_supplier_id = models.UUIDField(null=True)

    def __repr__(self):
        return f'<Wholesaler: {self.id}>'

    def __str__(self):
        return f'Wholesaler [{self.id}] "{self.name}"'


class WholesalerCommodityCategory(AdminLinkMixin, ArchiveModel):
    wholesaler = models.ForeignKey(
        Wholesaler,
        on_delete=models.CASCADE,
    )
    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        related_name='children',
        on_delete=models.PROTECT,
    )
    name = models.CharField(max_length=200)

    class Meta:
        unique_together = [('wholesaler', 'name')]

    def __repr__(self):
        return f'<WholesalerCommodityCategory: {self.id}>'

    def __str__(self):
        return f'WholesalerCommodityCategory [{self.id}] "{self.name}"'


class WholesalerCommodity(AdminLinkMixin, ArchiveModel):
    wholesaler = models.ForeignKey(
        Wholesaler,
        related_name='wholesaler_commodities',
        on_delete=models.CASCADE,
    )
    category = models.ForeignKey(
        WholesalerCommodityCategory,
        related_name='wholesaler_commodities',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    name = models.CharField(max_length=200)
    product_code = models.CharField(
        max_length=50,
        null=True,
    )
    net_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
    )
    gross_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
    )
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100),
        ],
    )
    tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
    )
    barcode = models.CharField(max_length=50, default='', null=True, blank=True)
    barcode_type = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        default='ean13',
        choices=Barcode.BARCODE_TYPES,
    )
    volume_unit = models.ForeignKey(
        VolumeMeasure,
        related_name='wholesaler_commodities',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    total_pack_capacity = models.IntegerField()
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='Description',
    )
    # archived is TRUE if it wasn't in the last import
    archived = models.BooleanField(default=False)

    ecommerce_supplier_product_id = models.UUIDField(null=True)

    class Meta:
        unique_together = [('wholesaler', 'product_code')]

    def __repr__(self):
        return f'<WholesalerCommodity: {self.id}>'

    def __str__(self):
        return f'WholesalerCommodity [{self.id}] "{self.name}"'


class Brand(PhotoModelMixin, AdminLinkMixin, ArchiveModel):
    photo_folder = ImageTypeEnum.WAREHOUSE_BRAND_PHOTOS

    business = models.ForeignKey(
        'business.Business',
        related_name='brands',
        on_delete=models.CASCADE,
    )
    name = models.CharField(max_length=100)
    photo = models.ForeignKey(
        'photo.Photo',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    archived = models.BooleanField(default=False)

    @transaction.atomic
    def soft_delete_with_commodities(self):
        self.commodities.update(deleted=tznow())
        self.soft_delete()

    @property
    def history_name(self):
        return self.name


class CommodityCategory(ESDocMixin, HistoryChangeModel, ArchiveModel):
    es_doc_type = ESDocType.COMMODITY_CATEGORY

    ARCHIVED_ID = -1
    NO_CATEGORY_ID = 0
    HOLD_OLD_STATE = False
    business = models.ForeignKey(
        'business.Business',
        related_name='commodities_categories',
        on_delete=models.CASCADE,
    )
    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        related_name='children',
        on_delete=models.PROTECT,
    )
    name = models.CharField(max_length=255)
    order = models.PositiveSmallIntegerField(default=0)
    ecommerce_id = models.UUIDField(null=True, blank=True)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        ordering = ('order',)

    @property
    def history_name(self):
        return self.name

    @transaction.atomic
    def soft_delete(self):
        Commodity.objects.filter(category=self).update(category=None)
        super().soft_delete()
        CommodityCategory.objects.filter(
            business=self.business,
            order__gt=self.order,
        ).update(order=F('order') - 1)

    def __str__(self):
        return f'CommodityCategory [{self.id}] "{self.name}"'

    @staticmethod
    def reindex_all_subcategories(category_ids):
        categories = CommodityCategory.all_objects.filter(id__in=category_ids)
        top_categories_for_reindex = []
        for category in categories:
            doc = category.get_document()
            top_categories_for_reindex.append(doc.top_level_ancestor)
        top_categories = CommodityCategory.all_objects.filter(id__in=top_categories_for_reindex)
        for top_category in set(top_categories):
            top_category.reindex_with_related_categories()

    def reindex_with_related_categories(
        self,
        old_ancestors=None,
        old_descendants=None,
    ):
        from webapps.warehouse.elasticsearch.commodities import (
            CommodityCategoryDocument,
        )

        self.reindex()
        new_document = self.get_document()
        new_all_ancestors = new_document.all_ancestors
        new_all_descendants = new_document.all_descendants

        documents_to_refresh = set()
        documents_to_refresh.update(new_all_ancestors)
        documents_to_refresh.update(new_all_descendants)

        old_ancestors = set(old_ancestors or [])
        old_descendants = set(old_descendants or [])
        if old_ancestors != set(new_all_ancestors):
            documents_to_refresh.update(old_ancestors - set(new_all_ancestors))
        if old_descendants != set(new_all_descendants):
            documents_to_refresh.update(old_descendants - set(new_all_descendants))

        CommodityCategoryDocument.reindex(documents_to_refresh)


class CommodityQuerySet(ArchiveQuerySet):

    def annotate_stock_level_remaining_volume(self) -> models.QuerySet:
        _remaining_volume = Coalesce(Sum('commoditystocklevel__remaining_volume'), 0)
        _stock_level = Cast(
            _remaining_volume / F('total_pack_capacity'), output_field=IntegerField()
        )
        not_null_stock_level = Case(
            When(
                Q(total_pack_capacity__isnull=True) | Q(total_pack_capacity=0), then=Decimal('0.00')
            ),
            default=_stock_level,
            output_field=models.DecimalField(),
        )

        return self.order_by().annotate(
            stock_level=not_null_stock_level,
        )

    def annotate_is_in_stock(self) -> models.QuerySet:
        is_in_stock = Case(
            When(
                Q(enable_stock_control=False) | Q(enable_stock_control=True, stock_level__gt=0),
                then=True,
            ),
            default=False,
            output_field=models.BooleanField(),
        )

        return (
            self.annotate_stock_level_remaining_volume()
            .order_by()
            .annotate(is_in_stock=is_in_stock)
        )


class CommodityManager(ArchiveManager.from_queryset(CommodityQuerySet)):
    pass


class Commodity(ESDocMixin, PhotoModelMixin, HistoryChangeModel, ArchiveModel):
    es_doc_type = ESDocType.COMMODITY
    river = River.COMMODITY

    photo_folder = ImageTypeEnum.WAREHOUSE_COMMODITY_PHOTOS
    NOT_DISPLAYED_HISTORY = HistoryChangeModel.NOT_DISPLAYED_HISTORY + [
        'order',
        'subbookings',
        'business',
        'wholesaler_commodity',
    ]

    TYPE_RETAIL = 'r'
    TYPE_PRO = 'p'
    PRODUCT_TYPE_CHOICES = (
        (TYPE_PRO, 'Professional'),
        (TYPE_RETAIL, 'Retail'),
    )

    objects = CommodityManager()
    all_objects = models.Manager()
    business = models.ForeignKey(
        'business.Business',
        related_name='commodities',
        on_delete=models.CASCADE,
    )
    wholesaler_commodity = models.ForeignKey(
        WholesalerCommodity,
        related_name='commodities',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    category = models.ForeignKey(
        CommodityCategory,
        related_name='commodities',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    extra_categories = models.ManyToManyField(
        CommodityCategory,
        related_name='commodities_extra',
        blank=True,
    )
    name = models.CharField(max_length=255)
    catalog_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    product_code = models.CharField(max_length=50)  # "SKU" (in frontend apps)
    product_type = models.CharField(
        max_length=1,
        choices=PRODUCT_TYPE_CHOICES,
        default=TYPE_RETAIL,
    )
    archived = models.BooleanField(default=False)
    net_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    gross_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    tax_rate = models.ForeignKey(
        'pos.TaxRate',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    current_net_purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    suppliers = models.ManyToManyField(
        Supplier,
        related_name='commodities',
        blank=True,
    )
    barcodes = models.ManyToManyField(
        Barcode,
        related_name='commodities',
        blank=True,
    )
    # volume_unit is demanded only for professional product type
    volume_unit = models.ForeignKey(
        VolumeMeasure,
        related_name='commodities',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    total_pack_capacity = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
    )

    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='Description',
    )
    subbookings = models.ManyToManyField(
        SubBooking,
        related_name='commodities',
        blank=True,
    )
    photo = models.ForeignKey(
        to='photo.Photo',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    additional_photos = models.ManyToManyField(
        to='photo.Photo',
        related_name='commodities',
    )
    brand = models.ForeignKey(
        to=Brand,
        on_delete=models.SET_NULL,
        related_name='commodities',
        null=True,
        blank=True,
    )
    order = models.PositiveSmallIntegerField()
    enable_stock_control = models.BooleanField(default=True)
    ecommerce_catalog_product_id = models.UUIDField(null=True)
    ecommerce_catalog_product_variant_id = models.UUIDField(null=True)

    class Meta:
        verbose_name = 'Commodity'
        verbose_name_plural = 'Commodities'

    def __repr__(self):
        return f'<Commodity: {self.id}>'

    def __str__(self):
        return f'Commodity [{self.id}] "{self.name}"'

    @property
    def full_packages_left(self) -> int:
        return sum(
            stock_level.full_packages_left for stock_level in self.commoditystocklevel_set.all()
        )

    @property
    def full_packages_left_in_default_warehouse(self) -> int:
        default_warehouse_stock_level = self.commoditystocklevel_set.filter(
            warehouse__is_default=True,
        ).first()
        if default_warehouse_stock_level:
            return default_warehouse_stock_level.full_packages_left
        return 0

    @property
    def volume_remaining_in_open_packages(self) -> Decimal:
        """Note that volume remaining in open packages may be higher than
        the volume of single package (total pack capacity).
        """
        return sum(
            stock_level.volume_in_last_open_package
            for stock_level in self.commoditystocklevel_set.all()
        )

    def get_gross_purchase_price(self):
        if self.tax_rate and self.tax_rate.rate:
            gross_purchase_price = Decimal(
                self.current_net_purchase_price
                + (self.current_net_purchase_price * self.tax_rate.rate / Decimal('100'))
            ).quantize(Decimal('.01'))
        else:
            gross_purchase_price = self.current_net_purchase_price
        return gross_purchase_price

    def get_remaining_stock_label(self, full_packages_txt, single_package_txt) -> str:
        if self.full_packages_left == 1:
            full_packages_txt = single_package_txt
        label = f'{self.full_packages_left} {full_packages_txt}'
        if self.volume_remaining_in_open_packages:
            stripped_volume = str(self.volume_remaining_in_open_packages).rstrip('0').rstrip('.')
            label += f' + {stripped_volume}'
            if self.volume_unit:
                label += f' {self.volume_unit.symbol}'
        return label

    @property
    def remaining_stock_label(self) -> str:
        """Returns human-readable string representing the remaining amount of
        commodity.
        """
        full_packages_txt = _('full packages')
        single_package_txt = _('full package')
        return self.get_remaining_stock_label(
            full_packages_txt,
            single_package_txt,
        )

    @property
    def remaining_stock_short_label(self) -> str:
        """Returns human-readable short string representing the
        remaining amount of commodity.
        """
        full_packages_txt = single_package_txt = _('pck.')
        return self.get_remaining_stock_label(
            full_packages_txt,
            single_package_txt,
        )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.id is None and self.order is None:
            # We are creating a new commodity and order has not been specified,
            # so we assign it as a max() + 1. It's performed in a non atomic
            # way but we will have to live with that by now :(
            # In future a unique together constraint (with business) with
            # fallback-and-retry on IntegrityError could be introduced, so as to
            # make order assigning concurrent-proof.
            max_order = (
                Commodity.objects.filter(
                    business=self.business,
                ).aggregate(
                    Max('order')
                )['order__max']
                or 0
            )
            self.order = max_order + 1
        super().save(force_insert, force_update, using, update_fields)

    @classmethod
    def get_commodities_for_pos_checkout(cls, business_id: int):
        """Return commodities available for sale in POS Checkout.
        For backward compatibility reasons we can return only commodities
        from default warehouse. That's because mobile apps can't (by now)
        specify from which warehouse they intend to sell commodity, so by
        default we sell commodities only from the default warehouse.

        We also can't return professional products, because they are not
        allowed for sale and mobile apps can't handle such error properly.
        """
        return (
            Commodity.objects.filter(
                archived=False,
                commoditystocklevel__warehouse__business_id=business_id,
                commoditystocklevel__warehouse__is_default=True,
            )
            .exclude(
                product_type=Commodity.TYPE_PRO,
            )
            .order_by('order')
        )

    def get_stock_level_for_pos(self, warehouse=None):
        if not warehouse:
            warehouse = Warehouse.get_default_warehouse(self.business)
        return self.get_stock_level(warehouse)

    def get_stock_level(self, warehouse, create=True):
        if self.business.id != warehouse.business.id:
            raise ValueError('Cannot get stock level for warehouse of different business')
        if create:
            stock_level = CommodityStockLevel.objects.get_or_create(
                commodity=self,
                warehouse=warehouse,
            )[0]
            stock_level.operator_id = self.operator_id
        else:
            stock_level = CommodityStockLevel.objects.filter(
                commodity=self,
                warehouse=warehouse,
            ).first()
        return stock_level

    def increase_packages(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.increase_packages(quantity, *args, **kwargs)

    def decrease_packages(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.decrease_packages(quantity, *args, **kwargs)

    def set_packages(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.set_packages(quantity, *args, **kwargs)

    def increase_volume(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.increase_volume(quantity, *args, **kwargs)

    def decrease_volume(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.decrease_volume(quantity, *args, **kwargs)

    def set_volume(self, warehouse, quantity, *args, **kwargs):
        stock_level = self.get_stock_level(warehouse)
        stock_level.set_volume(quantity, *args, **kwargs)

    def soft_delete(self):
        """Overridden because deleted is something different than archived"""
        self.deleted = tznow()
        self.save()

    @transaction.atomic
    def reorder_before(self, reference_commodity_id: int, refresh_order: bool = False) -> None:
        reference_commodity_qs = Commodity.objects.filter(
            id=reference_commodity_id,
            business=self.business,
        )
        if not reference_commodity_qs.exists():
            raise ValueError(_('Invalid reference commodity for reordering'))

        # Move the commodity to the same place as selected reference commodity
        Commodity.objects.filter(id=self.id).update(
            order=Subquery(reference_commodity_qs.values('order')[:1])
        )
        # Move all latter ones by one place
        Commodity.objects.filter(
            business=self.business,
            order__gte=Subquery(reference_commodity_qs.values('order')[:1]),
        ).exclude(
            id=self.id,  # Do not move the commodity that has just been moved
        ).update(
            order=F('order') + 1
        )
        if refresh_order:
            self.refresh_from_db(fields=['order'])

    @transaction.atomic
    def reorder_after(self, reference_commodity_id: int, refresh_order: bool = False) -> None:
        reference_commodity_qs = Commodity.objects.filter(
            id=reference_commodity_id,
            business=self.business,
        )
        if not reference_commodity_qs.exists():
            raise ValueError(_('Invalid reference commodity for reordering'))

        # Make space for the commodity that is being moved
        # Move all latter ones by one place
        Commodity.objects.filter(
            business=self.business,
            order__gt=Subquery(reference_commodity_qs.values('order')[:1]),
        ).update(order=F('order') + 1)
        # Move our commodity after the selected one
        Commodity.objects.filter(id=self.id).update(
            order=Subquery(
                reference_commodity_qs.annotate(
                    new_order=F('order') + 1,
                ).values(
                    'new_order'
                )[:1]
            )
        )
        if refresh_order:
            self.refresh_from_db(fields=['order'])

    @property
    def history_name(self):
        return self.name

    @property
    def history_field_name(self):
        return self.name

    def get_last_financial_info(self):
        supply_row = SupplyRow.objects.filter(commodity=self).last()
        if not supply_row:
            return 0, 0, 0
        return supply_row.net_price, supply_row.gross_price, supply_row.tax

    # POS backward compatibility (pos.Product has item_price field)
    @property
    def item_price(self):
        from webapps.pos.models import POS

        pos = POS.objects.get(business=self.business)
        product_tax_mode = pos.product_tax_mode
        if product_tax_mode == POS.POS_TAX_MODE__INCLUDED:
            return self.gross_price
        return self.net_price

    @item_price.setter
    def item_price(self, value):
        from webapps.pos.models import POS

        pos = POS.objects.get(business=self.business)
        product_tax_mode = pos.product_tax_mode
        if product_tax_mode == POS.POS_TAX_MODE__INCLUDED:
            self.gross_price = value
        else:
            self.net_price = value

    def can_be_safely_deleted(self):
        """We don't want to delete commodities which are connected
        to any transaction"""
        return not self.transaction_rows.exists()

    # pos.models.ActionMixin internal, temporary implementation
    # for compatibility
    def execute_action(self, action, data=None):
        action_method = f'{action}_action'
        if hasattr(self, action_method):
            return getattr(self, action_method)(data)
        return None

    def status_action(self, data):
        self.archived = not data['active']
        self.save()

    def add_action(self, data):
        self._change_quantity_action(data, 'add')

    def remove_action(self, data):
        self._change_quantity_action(data, 'remove')

    def _change_quantity_action(self, data, action):
        quantity = data['quantity']
        change_notes = data.get('change_notes', '')
        operator_id = data.get('operator_id')
        stock_level = self.get_stock_level_for_pos()
        stock_level.operator_id = operator_id
        stock_level.change_notes = change_notes
        if action == 'remove':
            stock_level.decrease_packages(quantity)
        elif action == 'add':
            stock_level.increase_packages(quantity)

    @property
    def is_attached_to_formula(self):
        formula_rows = self.formula_rows.all()
        formulas = WarehouseFormula.objects.filter(
            rows__in=formula_rows,
            service_variants__active=True,
        )
        return formulas.exists()

    @classmethod
    def create_based_on_wholesaler(
        cls,
        wholesaler_id,
        wholesaler_commodity_id,
        business,
        operator_id=None,
    ):
        operator_arg = {'operator_id': operator_id} if operator_id else {}
        wholesaler_commodity = WholesalerCommodity.objects.filter(
            wholesaler__id=wholesaler_id,
            id=wholesaler_commodity_id,
        ).first()
        if not wholesaler_commodity:
            raise ValueError('Unknown wholesaler commodity')

        category_name = (
            f"{wholesaler_commodity.wholesaler.short_name} {wholesaler_commodity.category.name}"
        ).strip()
        commodity_categories = CommodityCategory.objects.filter(
            business=business,
            name=category_name,
        )
        if commodity_categories.exists():
            category = commodity_categories.first()
        else:
            category = CommodityCategory(
                business=business,
                name=category_name,
                **operator_arg,
            )
            category.save()

        if wholesaler_commodity.product_code:
            product_code = wholesaler_commodity.product_code
        elif wholesaler_commodity.barcode:
            product_code = wholesaler_commodity.barcode
        else:
            product_code = f"{wholesaler_commodity.wholesaler.short_name}_{wholesaler_commodity.id}"

        tax_rate = business.pos.tax_rates.filter(
            rate=wholesaler_commodity.tax_rate,
        ).first()
        if not tax_rate:
            raise ValueError(
                f'{wholesaler_commodity.name} from {wholesaler_commodity.wholesaler.short_name} '
                f'has unknown tax rate: {wholesaler_commodity.tax_rate}'
            )

        volume_unit = wholesaler_commodity.volume_unit
        if volume_unit:
            volume_unit.businesses.add(business)

        brand = Brand.objects.filter(
            business=business,
            name=wholesaler_commodity.wholesaler.short_name,
        ).first()
        if not brand:
            brand = Brand(
                business=business,
                name=wholesaler_commodity.wholesaler.short_name,
            )
            brand.save()

        order = (
            Commodity.objects.filter(
                business=business,
                category=category,
            ).aggregate(
                Max('order')
            )['order__max']
            or 0
        )

        commodity = Commodity(
            business=business,
            wholesaler_commodity=wholesaler_commodity,
            category=category,
            name=wholesaler_commodity.name,
            product_code=product_code,
            net_price=wholesaler_commodity.net_price,
            gross_price=wholesaler_commodity.gross_price,
            tax_rate=tax_rate,
            tax=wholesaler_commodity.tax,
            volume_unit=volume_unit,
            total_pack_capacity=wholesaler_commodity.total_pack_capacity,
            description=wholesaler_commodity.description,
            brand=brand,
            order=order + 1,
            **operator_arg,
        )
        commodity.save()

        if wholesaler_commodity.barcode:
            barcode = Barcode.objects.filter(
                code=wholesaler_commodity.barcode,
                business=business,
                type=wholesaler_commodity.barcode_type,
            ).first()
            if not barcode:
                barcode = Barcode(
                    code=wholesaler_commodity.barcode,
                    business=business,
                    type=wholesaler_commodity.barcode_type,
                    **operator_arg,
                )
                barcode.save()
            commodity.barcodes.add(barcode)

        return commodity


class Warehouse(HistoryChangeModel, ArchiveModel):
    TYPE_RETAIL = 'r'
    TYPE_PROFESSIONAL = 'p'
    TYPE_OVERALL = 'o'
    WAREHOUSE_TYPE_CHOICES = (
        (TYPE_RETAIL, 'Retail'),
        (TYPE_PROFESSIONAL, 'Professional'),
        (TYPE_OVERALL, 'Overall'),
    )

    business = models.ForeignKey(
        'business.Business',
        related_name='warehouses',
        on_delete=models.CASCADE,
    )
    name = models.CharField(max_length=200)
    address = models.CharField(max_length=150, null=True, blank=True)
    zip_code = models.CharField(max_length=25, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    country = CountryField(blank=True, null=True)
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=False)
    type = models.CharField(
        max_length=1,
        choices=WAREHOUSE_TYPE_CHOICES,
        default=TYPE_RETAIL,
    )
    commodities = models.ManyToManyField(
        Commodity,
        through='CommodityStockLevel',
        related_name='warehouses',
    )

    @property
    def is_during_stocktaking(self):
        return self.stocktakings.filter(
            acceptance_date__isnull=True,
            deleted__isnull=True,
        ).exists()

    @property
    def history_name(self):
        return self.name

    @classmethod
    def get_default_warehouse(cls, business):
        return cls.objects.filter(
            business=business,
            is_default=True,
        ).first()

    @classmethod
    def get_or_create_default_warehouse(cls, business):
        """BEWARE that this method is non-atomic - concurrent calls may
        produce multiple default warehouses to be created. However for now
        we don't need it to be concurrent-proof.
        """
        default_warehouse = cls.get_default_warehouse(business)
        if not default_warehouse:
            default_warehouse = cls.objects.create(
                name=_('Default Storage Location'),
                business=business,
                is_default=True,
            )
        return default_warehouse

    @transaction.atomic
    def soft_delete(self):
        if self.is_default:
            raise ValueError(_('Default warehouse cannot be deleted'))
        has_stock_levels = self.commodities.filter(
            commoditystocklevel__remaining_volume__gt=0,
        ).exists()
        if has_stock_levels:
            raise ValueError(_('Warehouse with stock levels cannot be deleted'))

        super().soft_delete()

        self.commoditystocklevel_set(manager='all_objects').filter(
            deleted__isnull=True,
        ).update(
            deleted=self.deleted,
        )

    def __str__(self):
        truncated_name = truncatechars(self.name, 100)
        return f'Warehouse [{self.id}] "{truncated_name}"'

    @staticmethod
    def post_save_handler(
        sender, instance, created=False, **kwargs
    ):  # pylint: disable=unused-argument
        if created and not Supplier.objects.filter(business=instance.business).exists():
            Supplier.create_default(instance.business)
        if (
            created
            and not VolumeMeasure.objects.filter(
                businesses=instance.business,
            ).exists()
        ):
            VolumeMeasure.add_default_measures(instance.business)


post_save.connect(Warehouse.post_save_handler, Warehouse)


class CommodityStockLevel(HistoryChangeModel, ArchiveModel):
    """M2M Commodity-Warehouse through model
    Represents the stock levels of a commodity in a particular warehouses
    """

    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE)
    commodity = models.ForeignKey(Commodity, on_delete=models.CASCADE)

    remaining_volume = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    remaining_volume_max_value = 10**8 - 1  # 99,999,999.00

    minimum_packages = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
    )
    maximum_packages = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
    )
    change_notes = models.CharField(max_length=255, blank=True)

    # HistoryChangeModel attributes
    refresh_before_serialize_history = True
    history_fields_refresh = ['remaining_volume']

    class Meta:
        unique_together = ('warehouse', 'commodity')

    @property
    def total_packages_left(self) -> int:
        if not self.commodity.total_pack_capacity:
            return 0
        return math.ceil(self.remaining_volume / self.commodity.total_pack_capacity)

    @property
    def full_packages_left(self) -> int:
        if not self.commodity.total_pack_capacity:
            return 0
        return int(self.remaining_volume / self.commodity.total_pack_capacity)

    @property
    def volume_in_last_open_package(self) -> Decimal:
        self.remaining_volume: Decimal
        return self.remaining_volume - self.full_packages_left * self.commodity.total_pack_capacity

    @property
    def remaining_stock_label(self) -> str:
        """Returns human-readable string representing the remaining amount of
        commodity.
        """
        full_packages_txt = _('full packages')
        if self.full_packages_left == 1:
            full_packages_txt = _('full package')
        label = f'{self.full_packages_left} {full_packages_txt}'
        if self.volume_in_last_open_package:
            stripped_volume = str(self.volume_in_last_open_package).rstrip('0').rstrip('.')

            label += f' + {stripped_volume}/{self.commodity.total_pack_capacity}'
            if self.commodity.volume_unit:
                label += f' {self.commodity.volume_unit.symbol}'
        return label

    @property
    def reorder_amount(self) -> int:
        return self.maximum_packages - self.total_packages_left

    @set_self_changed_by_document
    def increase_volume(self, quantity):
        self._create_initial_history_row_if_not_exists()
        self.remaining_volume = Least(
            Greatest(F('remaining_volume') + quantity, 0),
            self.remaining_volume_max_value,
        )
        self.save()
        # Refresh is necessary cause otherwise there would be a
        # CombinedExpression in remaining_volume attribute
        self.refresh_from_db(fields=['remaining_volume'])

    @set_self_changed_by_document
    def decrease_volume(self, quantity):
        self.increase_volume(-quantity)

    @set_self_changed_by_document
    def set_volume(self, quantity, save=True):
        if save:
            self._create_initial_history_row_if_not_exists()
        self.remaining_volume = quantity if quantity > 0 else 0
        if save:
            self.save()
        return quantity if quantity > 0 else 0

    @set_self_changed_by_document
    def increase_packages(self, quantity):
        self.increase_volume(quantity * self.commodity.total_pack_capacity)

    @set_self_changed_by_document
    def decrease_packages(self, quantity):
        self.increase_packages(-quantity)

    @set_self_changed_by_document
    def set_packages(self, quantity, save=True):
        return self.set_volume(
            quantity * self.commodity.total_pack_capacity,
            save,
        )

    def set_remaining_volume(self, quantity, is_full_package_inventoried, save, **kwargs):
        if is_full_package_inventoried:
            new_volume = self.set_packages(quantity, save, **kwargs)
        else:
            new_volume = self.set_volume(quantity, save, **kwargs)
        return new_volume

    def _create_initial_history_row_if_not_exists(self):
        if self.history_change:
            return
        self.save_history()


class InternalExpenditureReason(StrChoicesEnum):
    DAMAGED = 'DMG', gettext_lazy('Damaged')
    PAST_SELLBY_DATE = 'EXP', gettext_lazy('Past the sell-by date')
    REDUNDANT_USAGE = 'RDU', gettext_lazy('Redundant usage')
    SPECIAL_OFFER = 'SPO', gettext_lazy('Special offer')
    EXTERNAL_TRANSFER = 'EXT', gettext_lazy('Transfer outside the salon')
    USED_FOR_SERVICE = 'UFS', gettext_lazy('Used for service')
    OTHER = 'OTH', gettext_lazy('Other')


class WarehouseDocument(HistoryChangeModel, BaseWarehouseDocument):
    """This class represents documents issued within a particular Warehouse:
     - internal expenditures (RW)
     - inter stock transfers (MM)
     - stock issue confirmation (WZ)

    Note that Supply represents documents issued for a particular Supplier.
    """

    available_types = (
        WarehouseDocumentType.WZ,
        WarehouseDocumentType.RW,
        WarehouseDocumentType.MM,
        WarehouseDocumentType.PM,
    )

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='documents',
    )

    # Wydanie zewnętrzne (WZ)
    transaction = models.ForeignKey(
        'pos.Transaction',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='warehouse_documents',
    )

    # Rozchód wewnętrzny (RW)
    subbooking = models.ForeignKey(
        'booking.SubBooking',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    rw_reason = models.CharField(
        max_length=3,
        choices=InternalExpenditureReason.choices(),
        blank=True,
    )

    # Przesunięcie międzymagazynowe (MM)
    warehouse_to = models.ForeignKey(
        Warehouse,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='documents_to',
    )
    # only for data migration from POS to Warehouse
    hidden = models.BooleanField(default=False)

    @property
    def number(self):
        if self.manually_assigned_number:
            return self.manually_assigned_number
        business = self.warehouse.business
        sequence_record = SequenceRecord.objects.filter(
            business=business,
            type=self.type,
            related_document_id=self.id,
        ).first()
        if not sequence_record:
            return ''
        return sequence_record.assigned_number

    def get_business_id(self):
        return self.warehouse.business_id

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.type not in self.available_types:
            raise ValueError(
                f'Invalid type: {self.type}. '
                f'WarehouseDocument must be one of {self.available_types}'
            )
        return super().save(force_insert, force_update, using, update_fields)

    @django.db.transaction.atomic
    def soft_delete(self):
        # Note that we cannot use bulk operator for rows deletion since it would
        # not fire stock updates logic
        for row in self.rows.all():
            row.soft_delete()
        super().soft_delete()

    @property
    def history_name(self):
        return self.number

    def __str__(self):
        return f'WarehouseDocument [{self.id}] "{self.number}"'


class WarehouseDocumentRow(HistoryChangeModel, ArchiveModel):
    document = models.ForeignKey(WarehouseDocument, on_delete=models.CASCADE, related_name='rows')
    commodity_name = models.CharField(max_length=255)
    commodity = models.ForeignKey(Commodity, on_delete=models.PROTECT)
    quantity = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=1,
        validators=[MinValueValidator(Decimal('0.00'))],
    )

    # decimal_places=3 is strange but intended
    net_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    gross_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    # Tax amount
    tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
        null=True,
        blank=True,
    )
    is_full_package_expenditure = models.BooleanField(null=False, blank=False)

    objects = ArchiveManager()
    all_objects = models.Manager()

    def increase_volume(self, quantity, is_full_package_expenditure=None):
        if is_full_package_expenditure is None:
            is_full_package_expenditure = self.is_full_package_expenditure

        history_change_by_document = {
            'document_id': self.document.id,
            'document_type': self.document.type,
        }
        self.commodity.operator_id = self.operator_id
        if is_full_package_expenditure:
            self.commodity.increase_packages(
                self.document.warehouse,
                quantity,
                **history_change_by_document,
            )
        else:
            self.commodity.increase_volume(
                self.document.warehouse,
                quantity,
                **history_change_by_document,
            )

        # If it's a stock transfer we apply a negative operation on the
        # target warehouse
        if self.document.type == WarehouseDocumentType.MM and self.document.warehouse_to:
            if is_full_package_expenditure:
                self.commodity.increase_packages(
                    self.document.warehouse_to,
                    -quantity,
                    **history_change_by_document,
                )
            else:
                self.commodity.increase_volume(
                    self.document.warehouse_to,
                    -quantity,
                    **history_change_by_document,
                )

    def decrease_volume(self, quantity, is_full_package_expenditure=None):
        self.increase_volume(-quantity, is_full_package_expenditure)

    def forward_operation(self, quantity, is_full_package_expenditure=None):
        if self.document.type == WarehouseDocumentType.PM:
            self.increase_volume(quantity, is_full_package_expenditure)
        else:
            self.decrease_volume(quantity, is_full_package_expenditure)

    def reverse_operation(self, quantity, is_full_package_expenditure=None):
        if self.document.type == WarehouseDocumentType.PM:
            self.decrease_volume(quantity, is_full_package_expenditure)
        else:
            self.increase_volume(quantity, is_full_package_expenditure)

    @transaction.atomic
    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if not self.id:
            self.forward_operation(self.quantity)
        else:
            # Document Row is being updated
            old = WarehouseDocumentRow.all_objects.get(id=self.id)

            if self.is_full_package_expenditure != old.is_full_package_expenditure:
                # Row mode has changed -> we have to revert old stock
                # modification and apply new one
                self.reverse_operation(old.quantity, old.is_full_package_expenditure)
                self.forward_operation(self.quantity)
            else:
                # Only quantity has changed -> calculate delta and apply stock
                # modification
                quantity_delta = self.quantity - old.quantity
                if quantity_delta:
                    self.forward_operation(quantity_delta)

        super().save(force_insert, force_update, using, update_fields)

    @transaction.atomic
    def soft_delete(self):
        """Note that soft_delete() in fact fires up save() method"""
        super().soft_delete()
        self.reverse_operation(self.quantity)


@receiver(post_delete, sender=WarehouseDocumentRow)
def warehouse_document_row_post_delete(
    sender, instance, **kwargs
):  # pylint: disable=unused-argument
    instance.reverse_operation(instance.quantity)


class StocktakingDocument(HistoryChangeModel, BaseWarehouseDocument):
    available_types = (WarehouseDocumentType.INW,)

    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, related_name='stocktakings')
    acceptance_date = models.DateField(null=True, blank=True)

    def get_business_id(self):
        return self.warehouse.business_id

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.type not in self.available_types:
            raise ValueError(
                f'Invalid type: {self.type}.'
                f' StocktakingDocument must be one of {self.available_types}'
            )
        return super().save(force_insert, force_update, using, update_fields)

    @transaction.atomic
    def soft_delete(self):
        if self.acceptance_date:
            raise ValueError(_('Accepted stocktaking document cannot be deleted'))
        for row in self.rows.all():
            row.soft_delete()
        super().soft_delete()

    @property
    def history_name(self):
        return self.number

    def __str__(self):
        return f"StocktakingDocument [{self.id}], number: {self.number}"


class StocktakingDocumentRow(HistoryChangeModel, ArchiveModel):
    stocktaking = models.ForeignKey(
        StocktakingDocument,
        on_delete=models.CASCADE,
        related_name='rows',
    )
    commodity_name = models.CharField(max_length=255)
    commodity = models.ForeignKey(Commodity, on_delete=models.PROTECT)
    initial_volume = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    quantity_inventoried = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    is_full_package_inventoried = models.BooleanField()
    description = models.CharField(max_length=100, null=True, blank=True)

    objects = ArchiveManager()
    all_objects = models.Manager()

    @property
    def initial_full_packages(self) -> int:
        if not self.commodity.total_pack_capacity:
            return 0
        return int(self.initial_volume / self.commodity.total_pack_capacity)

    @property
    def initial_volume_in_last_open_package(self) -> Decimal:
        return self.initial_volume - self.initial_full_packages * self.commodity.total_pack_capacity

    @transaction.atomic
    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        super().save(force_insert, force_update, using, update_fields)
        if self.stocktaking.acceptance_date:
            self.update_stock_levels_with_inventoried_volume()

    def update_stock_levels_with_inventoried_volume(self) -> None:
        history_change_by_document = {
            'document_id': self.stocktaking.id,
            'document_type': self.stocktaking.type,
        }

        if self.is_full_package_inventoried:
            self.commodity.set_packages(
                self.stocktaking.warehouse,
                self.quantity_inventoried,
                **history_change_by_document,
            )
        else:
            self.commodity.set_volume(
                self.stocktaking.warehouse,
                self.quantity_inventoried,
                **history_change_by_document,
            )

    def soft_delete(self):
        if self.stocktaking.acceptance_date:
            raise ValueError(_('Row of accepted stocktaking document cannot be deleted'))
        super().soft_delete()


class WarehouseFormulaRow(HistoryChangeModel, ArchiveModel):
    commodity = models.ForeignKey(
        Commodity,
        on_delete=models.CASCADE,
        related_name='formula_rows',
    )
    count = models.FloatField(
        default=0,
        validators=[MinValueValidator(0)],
    )
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE)
    transactions = models.ManyToManyField(
        'pos.Transaction',
        blank=True,
        db_constraint=False,
        related_name='formula_rows',
    )

    def __str__(self):
        return (
            f'WarehouseFormulaRow [{self.id}]'
            f' Warehouse[{self.warehouse.id}]'
            f' Business[{self.warehouse.business.id}]'
        )


class WarehouseFormula(HistoryChangeModel, ArchiveModel):
    service_variants = models.ManyToManyField(
        'business.ServiceVariant',
        blank=True,
        db_constraint=False,
        related_name='formulas',
    )
    subbookings = models.ManyToManyField(
        SubBooking,
        blank=True,
        db_constraint=False,
    )
    rows = models.ManyToManyField(
        WarehouseFormulaRow,
        blank=True,
        related_name='formulas',
    )

    @classmethod
    def get_formula(cls, service_variant_id):
        formula = WarehouseFormula.objects.filter(
            service_variants__id=service_variant_id,
        ).first()
        return formula

    @classmethod
    def get_formula_rows(cls, service_variant_id):
        return WarehouseFormulaRow.objects.filter(
            formulas__service_variants__id=service_variant_id,
        )

    def __str__(self):
        return f'WarehouseFormula [{self.id}]'


MODEL_CLASSES = [
    Barcode,
    Commodity,
    CommodityCategory,
    CommodityStockLevel,
    StocktakingDocument,
    Supplier,
    Supply,
    SupplyRow,
    VolumeMeasure,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseFormula,
]


def history_change_signal_receiver(sender, instance, **kwargs):
    if not getattr(instance, 'save_history', False):
        return
    update_fields = kwargs.get('update_fields') or {}
    action = kwargs.get('action')
    if 'history_change' not in update_fields and sender in MODEL_CLASSES:
        instance.save_history()
    elif action == 'post_add':
        instance.save_history(m2m_fields_only=True)


for model_class in MODEL_CLASSES:
    post_save.connect(
        history_change_signal_receiver,
        sender=model_class,
        dispatch_uid="att_post_save_" + model_class.__name__,
    )
    for field in model_class._meta.many_to_many:
        related_field_key = field.name
        related_field_m2m = getattr(model_class, related_field_key)
        m2m_changed.connect(
            history_change_signal_receiver,
            sender=related_field_m2m.through,
            dispatch_uid="att_m2m_changed_" + model_class.__name__,
        )
