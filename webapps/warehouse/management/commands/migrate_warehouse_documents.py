import time
import math
import datetime

from django.db import transaction
from django.core.management.base import BaseCommand
from lib.tools import grouper

from webapps.pos.models import TransactionRow, Transaction

from webapps.business.models import (
    Resource,
)
from webapps.warehouse.models import (
    Commodity,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
)


class Command(BaseCommand):
    help = 'Command that gonna end process of documents data migration' ' from pos to warehouse.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all-transactions',
            action='store_true',
            dest='all_transactions',
            default=False,
        )

    def handle(self, *args, **opts):  # pylint: disable=unused-argument
        transaction_rows = TransactionRow.objects.filter(
            product__isnull=False,
        )
        transactions_ids = transaction_rows.values_list('transaction', flat=True).distinct()
        transactions_ids = set(transactions_ids) - set(
            WarehouseDocument.objects.values_list('transaction', flat=True)
        )
        transactions_to_migrate = self._get_transactions_to_migrate(
            transactions_ids,
            opts.get('all_transactions'),
        )
        number_of_packages = math.ceil(transactions_to_migrate.count() / 1000)
        for package_index, transactions_package in enumerate(
            grouper(transactions_to_migrate, 1000)
        ):
            print(f"package {package_index+1}/{number_of_packages}")
            self._migrate_transactions(transactions_package)

    @staticmethod
    def _get_transactions_to_migrate(transactions_ids, all_transactions):
        transactions = Transaction.objects.filter(
            id__in=transactions_ids,
        )
        if not all_transactions:
            transactions = transactions.filter(
                created__lt=datetime.datetime(2019, 11, 1, 0, 0),
            )
        migrated_wz_documents = WarehouseDocument.objects.filter(
            transaction__in=transactions,
        )
        excluded_transactions_ids = migrated_wz_documents.values_list('transaction', flat=True)
        transactions = transactions.exclude(
            id__in=excluded_transactions_ids,
        )
        transactions = transactions.prefetch_related(
            'pos__business',
            'operator',
        )
        print("--- %s transactions ---" % (transactions.count()))
        return transactions

    @staticmethod
    def _migrate_transactions(transactions):
        start_time = time.time()

        with transaction.atomic():
            wz_documents_by_transaction_id = {}
            for single_transaction in transactions:
                business = single_transaction.pos.business
                issuing_staffer = (
                    Resource.objects.filter(
                        staff_user=single_transaction.operator,
                    ).first()
                    or Resource.objects.filter(staff_user=business.owner).first()
                )
                warehouse = Warehouse.objects.filter(
                    business=business,
                    is_default=True,
                ).first()
                wz_document = WarehouseDocument(
                    transaction=single_transaction,
                    warehouse=warehouse,
                    hidden=True,
                    issuing_staffer=issuing_staffer,
                    type='WZ',
                )
                wz_documents_by_transaction_id[single_transaction.id] = wz_document
                wz_document.save()

        with transaction.atomic():
            wz_rows = []
            transaction_rows = TransactionRow.objects.filter(
                product__isnull=False,
                transaction__in=transactions,
            ).prefetch_related('product', 'transaction')
            for transaction_row in transaction_rows:
                try:
                    commodity = transaction_row.product
                except Commodity.DoesNotExist:
                    continue
                if not commodity:
                    continue
                wz_document = wz_documents_by_transaction_id.get(
                    transaction_row.transaction.id,
                )
                if not wz_document:
                    continue

                wz_row = WarehouseDocumentRow(
                    commodity_name=commodity.name,
                    commodity=transaction_row.product,
                    quantity=transaction_row.quantity or 1,
                    net_price=commodity.net_price,
                    gross_price=commodity.gross_price,
                    tax=commodity.tax,
                    is_full_package_expenditure=True,
                    document=wz_document,
                )
                wz_rows.append(wz_row)

            WarehouseDocumentRow.objects.bulk_create(wz_rows, 1000)
        print("--- %s seconds ---" % (time.time() - start_time))
