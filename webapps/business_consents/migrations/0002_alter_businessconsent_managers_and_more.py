# Generated by Django 4.1.10 on 2023-11-10 13:09

from django.db import migrations, models
import django.utils.timezone
import lib.models


class Migration(migrations.Migration):
    dependencies = [
        ("business_consents", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelManagers(
            name="businessconsent",
            managers=[
                ("objects", lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddField(
            model_name="businessconsent",
            name="created",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="Created (UTC)",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="businessconsent",
            name="deleted",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Deleted (UTC)"),
        ),
        migrations.AddField(
            model_name="businessconsent",
            name="updated",
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name="Updated (UTC)"),
        ),
    ]
