# Generated by Django 4.2.18 on 2025-02-11 13:47

from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('message_blast', '0027_alter_messageblastcategoryimage_category_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MBSpamAnalyzerSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=128, unique=True)),
                ('ai_model', models.Char<PERSON>ield(default='gemini-2.0-flash', max_length=128)),
                (
                    'explanation_for_ai',
                    models.TextField(
                        default='\n        Check if a message follows spam guidelines.\n\n        Guidelines:\n        1. Allowed content:\n            - Promotional campaigns from verified Booksy merchants\n            - Gift cards redeemable on booksy.com\n            - Credits for use within Booksy platform\n            - Links to *.booksy.com domains\n        \n        2. Not allowed:\n            - External promotional links\n            - Non-Booksy gift cards/credits\n            - Gibberish or meaningless content\n            - Malicious content"\n        )\n        '
                    ),
                ),
                (
                    'explanation_for_response',
                    models.TextField(
                        default='\n        Please response in JSON format: \'message_id\': \'id\', \'is_spam\': true/false, score:0-100,\n        explain_result_for_dummers: few sentences.  \n        ##########\n        Message to check for spam is: [placeholder]  \n        Output schema:\n        {\n            "message_id": str,          # Same as input ID\n            "is_spam": bool,           # True if spam detected\n            "score": int,              # Spam confidence score (0-100)\n            "explain_result": str,     # User-friendly explanation of decision\n            "flags": List[str]         # Optional: List of specific rule violations\n        }\n        Return: Resp\n        '
                    ),
                ),
                (
                    'ai_params',
                    models.JSONField(
                        blank=True,
                        default={
                            'max_output_tokens': 800,
                            'temperature': 1.0,
                            'top_k': 10,
                            'top_p': 0.8,
                        },
                        null=True,
                    ),
                ),
                ('active', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Spam Analyzer Settings',
                'verbose_name_plural': 'Spam Analyzer Settings',
            },
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
    ]
