# pylint: skip-file
from webapps.message_blast.enums import (
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)

ES_MESSAGE_BLAST_SPECIAL_OCCASIONS = [
    dict(
        name="Bienvenida a un nuevo cliente",
        description="Enviado a los clientes dos horas después de su primera visita",
        title="¡Gracias por venir!",
        body=(
            'Esperamos que estés satisfecho con la experiencia que tuviste en {business_name}. Estamos muy contentos de que hayas venido.\n\n'
            'Visita {subdomain} para ver los otros servicios que ofrecemos y para programar tu próxima cita.\n\n'
            '¡Esperamos volver a verte pronto!\n {business_name}'
        ),
        body_short=(
            '¡Esperamos que hayas disfrutado de tu experiencia en {business_name}! '
            'Estamos muy contentos de que hayas venido. Visita {subdomain} para programar tu próxima cita.'
        ),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
    ),
    dict(
        name="Informar a nuevos clientes sobre otros servicios",
        description="Enviado a los clientes 21 días después de su primera visita",
        title="Servicios adicionales que te encantarán",
        body=(
            'Gracias por tu visita a {business_name}. ¿Sabías que ofrecemos una variedad de otros servicios, todos diseñados pensando en ti?\n\n'
            'Dirígete a {subdomain}  para verlos y programar tu próxima cita.\n\n'
            '¡Nos vemos pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'Gracias por tu visita a {business_name}. Dirígete a {subdomain} para ver nuestros otros servicios y programar tu próxima cita. ¡Te esperamos!'
        ),
        image=None,
        recommended=True,
        order=2,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
    ),
    dict(
        name="Informar a los nuevos clientes sobre las tarjetas de regalo",
        description="Enviado a los clientes 30 días después de su primera visita si ha creado al menos una Tarjeta de regalo",
        title="¡Tarjetas Regalo Ahora disponibles! ",
        body=(
            '¿Necesitas un regalo para una ocasión especial o para hacerle saber a alguien que estás pensando en ell@s? '
            '¡Estás de suerte porque {business_name} ofrece tarjetas regalo!\n\n'
            'Visita {subdomain} y navega hasta Tarjetas de regalo para adquirir una.\n\n'
            '¡Hasta pronto!\n'
            '{business_name}'
        ),
        body_short=(
            '¿Necesitas un regalo para una ocasión especial o para hacerle saber a alguien que estás pensando en ellos? '
            'Estás de suerte porque {business_name} tiene las tarjetas regalo más inter'
        ),
        image=None,
        recommended=True,
        order=3,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.PROMOTE_GIFT_CARDS,
    ),
    dict(
        name="Invitar a otra visita",
        description="Enviado a los clientes 21 días después de su última visita si no tienen citas futuras en sus calendarios",
        title="¡Es hora de tu próxima cita! ",
        body=(
            '¿Nos extrañas tanto como nosotros a ti?\n\n'
            '¡Visita {subdomain} para reservar una de nuestras citas disponibles!\n\n'
            '¡Te esperamos!\n'
            '{business_name}'
        ),
        body_short=(
            '¿Nos extrañas tanto como nosotros te extrañamos a ti? '
            '¡Visita {subdomain} para conseguir una citas abiertas!'
        ),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INVITE_FOR_VISIT,
    ),
    dict(
        name="Volver a invitar y ofrecer un servicio gratuito",
        description="Enviado a clientes que no te han visitado en los últimos 90 días",
        title="¿Hora de un regalo? ¡Reserva tu próxima cita!",
        body=(
            'Vamos a consentirte… porque sí.\n\n'
            '¡Visita {subdomain} y obtén una de nuestras citas abiertas! '
            'Agregaremos un [AÑADIR SERVICIO] gratis si presentas este mensaje al finalizar la compra.\n\n'
            '¡Espero verte pronto!\n'
            '{business_name}'
        ),
        body_short=(
            '¡Déjanos consentirte! Visite {subdomain} y obtén una cita con nosotros. '
            'Presenta este mensaje al finalizar la compra para reclamar tu oferta de bonificación.'
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_FREE_SERVICE,
    ),
    dict(
        name="Vuelve a invitar y ofrece un descuento",
        description="Enviado a clientes que no te han visitado en los últimos 45 días",
        title="Una oferta especial porque... ¡Te extrañamos!",
        body=(
            'No te hemos visto en un tiempo y nos encantaría darte la bienvenida con un descuento.\n\n'
            'Visita {subdomain} para hacer una cita con nosotros. '
            'Aplicaremos tu [ADD DISCOUNT]% si presentas este mensaje al finalizar la compra.\n\n'
            '¡Espero verte pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'No te hemos visto en un tiempo. '
            'Consigue una cita y disfrute de un descuento de bienvenida. Visita {subdomain} para reservar.'
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_DISCOUNT,
    ),
    dict(
        name="Ofrece un descuento de feliz cumpleaños",
        description="Enviado a clientes que cumplirán años en los próximos 7 días",
        title="¡Este día solo llega una vez al año!",
        body=(
            '¡Feliz cumpleaños!\n\n'
            '¡Queremos celebrar juntos tu cumpleaños ofreciéndote un descuento! '
            'Muestra este mensaje la próxima vez que vengas y te haremos un [AÑADIR DESCUENTO] de descuento en tu cita.\n\n'
            'Ve a {subdomain} para reservar ahora.\n\n'
            '¡Que tengas un buen día!\n'
            "{business_name}"
        ),
        body_short=(
            '¡Feliz cumpleaños! Muéstranos este mensaje la próxima vez que vengas y '
            'te haremos un descuento. Ve a {subdomain} para reservar una cita.'
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HAPPY_BIRTHDAY,
    ),
    #  Links
    dict(
        name='Ocasiones especiales',
        description=(
            'Mensajes enviados en ocasiones especiales a los clientes para fomentar las reservas y más'
        ),
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        internal_name=MessageBlastInternalNames.MORE_OCCASIONS_LINK,
    ),
    dict(
        name='Personaliza tu mensaje',
        description=(
            'Envía mensajes a tus clientes en tiempo real. Elige entre nuestras plantillas o crea una propia.'
        ),
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        internal_name=MessageBlastInternalNames.ONE_TIME_MESSAGE_LINK,
    ),
    dict(
        name='Mensajes de grupos especiales',
        description='Mantente en contacto con tus clientes',
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        internal_name=MessageBlastInternalNames.SPECIAL_MESSAGE_BLASTS_LINK,
    ),
    # Special Occasions
    dict(
        name="Aumenta tus reservas en Navidad",
        description="Segundo lunes de noviembre",
        title="¡Esta Navidad te ofrecemos lo mejor de nuestros servicios!",
        body=(
            'Las Navidades están a la vuelta de la esquina y nuestras citas se están llenando rápidamente. '
            'Ve a {subdomain} para reservar una cita antes de que comiencen las vacaciones.\n\n'
            'Te deseamos unas felices fiestas.\n\n'
            'Hasta pronto,\n'
            '{business_name}'
        ),
        body_short=(
            'Las Navidades están a la vuelta de la esquina y nuestras citas se están llenando rápidamente. '
            'Visita {subdomain} para reservar una cita antes de que comiencen las vacaciones.'
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    dict(
        name="Aumenta tus reservas en primavera",
        description="21 de marzo",
        title="Hora de refrescarse para la primavera",
        body=(
            'La primavera ya está aquí, y queremos ayudarte a florecer después del invierno. '
            'Visita {subdomain} para reservar una cita y dejar que te mimen como te mereces.\n\n'
            '¡Hasta pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'La primavera ya está aquí, y queremos ayudarte a florecer después del invierno. Visita {subdomain} para concertar una cita.'
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.SPRING,
        date_type=MessageBlastDateType.STRICT,
        date_month=3,
        date_day=21,
    ),
    dict(
        name="Vende Tarjetas regalo para el Día del Padre",
        description="Segundo lunes de marzo",
        title="Dale las gracias a tu padre",
        body=(
            'Muéstrale a tu padre cuánto lo aprecias con una tarjeta regalo de {business_name} '
            'y deja que él elija el servicio. Visita {subdomain} y navega hasta Tarjetas Regalo para adquirir la tuya.\n\n'
            '¡Feliz Día del Padre a todos!\n\n'
            'Te deseamos lo mejor,\n'
            '{business_name}'
        ),
        body_short=(
            'Muéstrale a tu padre cuánto lo aprecias con una tarjeta regalo de {business_name}. Visita {subdomain} para comprarla.'
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.FATHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=3,
        date_monday=2,
    ),
    dict(
        name="Vende Tarjetas regalo para el Día de la Madre",
        description="Segundo lunes de mayo",
        title="Hazle un regalo a tu madre",
        body=(
            '¿Estás buscando el regalo perfecto para el Día de la Madre? Compra una tarjeta regalo de {business_name} '
            'y deja que ella elija el servicio. Visita {subdomain} y ve a Tarjetas regalo para comprar la tuya.\n\n'
            '¡Feliz Día de la Madre a todas!\n\n'
            'Te deseamos lo mejor,\n'
            '{business_name}'
        ),
        body_short=(
            '¿Buscas el regalo perfecto para el Día de la Madre? Compra una tarjeta de regalo de {business_name}. '
            'Visita {subdomain} para comprar la tuya.'
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MOTHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=5,
        date_monday=2,
    ),
    dict(
        name="Promociona tus servicios en Halloween",
        description="Primer lunes de octubre",
        title="¡Sé el centro de las miradas este Halloween!",
        body=(
            '¿Quieres ser la envidia de todos con tu disfraz de Halloween? '
            'Nosotros te ayudaremos a conseguir ese look especial que estás buscando. '
            '¡Visita {subdomain} para reservar una cita antes de que se agoten!\n\n'
            'Hasta pronto,\n'
            '{business_name}'
        ),
        body_short=(
            'Ten el mejor disfraz de Halloween con nuestra ayuda y consigue el look que estás buscando. '
            '¡Visita {subdomain} para reservar una cita antes de que se agoten!'
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HALLOWEEN,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=10,
        date_monday=1,
    ),
    dict(
        name="Obtén reservas de Año Nuevo",
        description="Primer lunes del año",
        title="Año Nuevo, ¿nuevo tú?",
        body=(
            'Ha llegado el Año Nuevo y te mereces darle la bienvenida con la mejor versión de ti.\n'
            'Visita {subdomain} para reservar una cita y celebrar el nuevo comienzo. ¡Estamos deseando volver a verte!\n\n'
            '¡Feliz Año Nuevo!\n'
            '{business_name}'
        ),
        body_short=(
            'Ha llegado el Año Nuevo y te mereces darle la bienvenida con la mejor versión de ti. '
            'Visita {subdomain} para reservar una cita y celebrar el nuevo comienzo.'
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.NEW_YEAR,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=1,
        date_monday=1,
    ),
    dict(
        name="Vende Tarjetas regalo navideñas",
        description="Primer lunes de diciembre",
        title="Simplifica tus compras navideñas",
        body=(
            '¿Quieres simplificar tus compras navideñas este año? Compra Tarjetas regalo y '
            'deja que tus seres queridos elijan cómo quieren utilizarlas.\n\n'
            'Visita {subdomain} y navega hasta Tarjetas regalo para comprar.\n\n'
            '¡Feliz Navidad!\n'
            '{business_name}'
        ),
        body_short=(
            'Simplifica tus compras navideñas este año y compra Tarjetas regalo de {business_name}. Visita {subdomain} para adquirirlas.'
        ),
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLDAY_GIFTCARD,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=12,
        date_monday=1,
    ),
    dict(
        name="Vende Tarjetas regalo este San Valentín",
        description="Primer lunes de febrero",
        title="¡Mima a tu pareja!",
        body=(
            'El día de San Valentín está a la vuelta de la esquina. ¡Compra una Tarjeta regalo y '
            'demuestra cuánto quieres a esa persona tan especial!\n'
            'Visita {subdomain} y navega hasta Tarjetas regalo para comprar la tuya.\n\n'
            'Que tengas un buen día,\n'
            '{business_name}'
        ),
        body_short=(
            'Sorprende a a tu pareja con una tarjeta regalo de {business_name}. Visita {subdomain} para comprar la tuya.'
        ),
        image=None,
        recommended=False,
        order=8,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.VALENTINES_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=2,
        date_monday=1,
    ),
    dict(
        name="Promociona servicios por la vuelta al cole",
        description="Primer lunes en Agosto",
        title="¡Reserva una cita por la vuelta al cole!",
        body=(
            'Con la vuelta al cole, seguro que tu look (o el de tus hijos) merece una atención especial. '
            'Visita {subdomain} para reservar una cita ya.\n\n'
            '¡Nos vemos pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'Con la vuelta al cole, seguro que tu look (o el de tus hijos) merece una atención especial. '
            'Visita {subdomain} para reservar una cita ya.'
        ),
        image=None,
        recommended=False,
        order=9,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BACK_TO_SCHOOL,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=8,
        date_monday=1,
    ),
    dict(
        name="Promociona servicios de cara a la Semana Santa",
        description="Segundo lunes de marzo",
        title="¡Nuevo look para la Semana Santa!",
        body=(
            '¡Date un capricho antes de Semana Santa! Visita {subdomain}. '
            '¡Celebra estas festividades con un look nuevo!\n\n'
            '¡Te esperamos!\n'
            '{business_name}'
        ),
        body_short=(
            '¡Date un capricho antes de Semana Santa! Visita {subdomain}.\n'
            '¡Te esperamos!\n'
            '{business_name}'
        ),
        image=None,
        recommended=False,
        order=10,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.EASTER,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=3,
        date_monday=2,
    ),
    dict(
        name="Ofrece descuentos de Black Friday",
        description="Segundo lunes de noviembre",
        title="¡Disfruta de unos descuentos increíbles este Black Friday!",
        body=(
            '¡Se acerca el Black Friday!\n\n'
            'Hemos preparado un descuento para ti del [AÑADIR DESCUENTO]% en '
            'tu próxima cita. Ve a {subdomain} para reservar tu cita y enseña este mensaje al llegar '
            'para validar el descuento.\n\n'
            '¡Nos vemos pronto!\n'
            '{business_name}'
        ),
        body_short=(
            '¡Se acerca el Black Friday! Hemos preparado un descuento para ti. Ve a {subdomain} para reservar tu cita.'
        ),
        image=None,
        recommended=False,
        order=11,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BLACK_FRIDAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    # One time message
    dict(
        name="Informa a tus clientes de nuevas aperturas",
        description="",
        title="Citas de última hora disponibles",
        body=(
            '¡Eres el primero en saberlo! Nos quedan algunas citas disponibles para el [FECHA].\n'
            '¡Reserva ahora en {subdomain} para no quedarte sin tu cita!\n\n'
            '¡Te esperamos!\n'
            '{business_name}'
        ),
        body_short=(
            '¡Eres el primero en saberlo! Nos quedan algunas citas disponibles para el [FECHA]. '
            '¡Reserva ahora en {subdomain} para no quedarte sin tu cita!'
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
    ),
    dict(
        name="Informa a tus clientes de los próximos días libres o descansos",
        description="",
        title="Salida temporal para descansar",
        body=(
            'Nos tomaremos unos días libres y nuestra agenda no estará disponible el [AÑADIR FECHA].\n'
            '¿Necesitas programar servicios antes de esta fecha? ¡Ve a {subdomain} y reserva una cita!\n\n'
            '¡Hasta pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'Nos tomaremos unos días libres los días [AÑADIR FECHA]. '
            '¿Necesitas reservar una cita antes de estas fechas? ¡Ve a {subdomain} y programa tu cita!'
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF,
    ),
    dict(
        name='Informa a tus clientes de nuevos servicios',
        description="",
        title="Consulta nuestros nuevos servicios",
        body=(
            '¡Noticia de última hora! Hemos añadido servicios nuevos que pueden interesarte. '
            'Visita {subdomain} para consultar los servicios que ofrecemos y reserva una cita con nosotros.\n'
            '¡Esperamos verte pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'Hemos añadido nuevos servicios que pueden interesarte. '
            'Ve a {subdomain} para ver la lista completa de servicios y reservar tu próxima cita.'
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES,
    ),
    dict(
        name="Invita a tus clientes a un evento",
        description="",
        title="¡Estás invitado/a!",
        body=(
            '{business_name} te invita a un evento y queremos que estés allí. '
            'Únete a nosotros en [AÑADIR NOMBRE DE EVENTO] el [AÑADIR FECHA DE EVENTO] '
            'para pasar un rato increíble.\n\n'
            '[AÑADIR DETALLES DE EVENTO]\n\n'
            '¡Esperamos verte allí!\n'
            '{business_name}'
        ),
        body_short=(
            '{business_name} te invita a un evento y queremos que estés allí. '
            '¡Únete a nosotros para pasar un rato increíble en la mejor compañía!'
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INVITE_TO_EVENT,
    ),
    dict(
        name="Pide a tus clientes una referencia",
        description="",
        title="¡Cuenta tu experiencia a tus amigos!",
        body=(
            '¿Quieres obtener un descuento en tu próxima cita? Recomienda {business_name} a un amigo. '
            'Pídeles que visiten {subdomain} para reservar una cita y diles que mencionen tu nombre cuando '
            'lleguen a su cita.\n\n'
            'Te haremos un descuento especial a ti y a tu amigo. ¡Todos salen ganando!\n\n'
            '¡Cuantos más, mejor!\n'
            '{business_name}'
        ),
        body_short=(
            'Recomienda {business_name} a un amigo y os haremos un descuento especial a ambos. ¡Todos salen ganando!'
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.ASK_FOR_REFERRAL,
    ),
    dict(
        name="Informa a tus clientes de los cambios de precios.",
        description="",
        title="¡Reserva una cita antes de que suban los precios!",
        body=(
            'Queremos informarte de que vamos a subir nuestros precios próximamente. '
            '¡Ve a {subdomain} para reservar una cita antes de que suban!\n\n'
            '¡Valoramos a los clientes como tú y estamos deseando volver a verte pronto!\n'
            '{business_name}'
        ),
        body_short=(
            'Queremos informarte de que vamos a subir nuestros precios próximamente. '
            '¡Ve a {subdomain} para reservar una cita antes de que suban!'
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES,
    ),
    dict(
        name='Escribe tu propio mensaje',
        description='',
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.OWN_MESSAGE,
    ),
    dict(
        name='Anima a tus clientes a usar el saldo de sus tarjetas regalo',
        description='',
        title='Usa tu tarjeta regalo',
        body=(
            "Parece que tienes saldo restante en tu tarjeta regalo de "
            "{business_name}. Visita {subdomain} para consultar nuestras "
            "reservas y usar tu tarjeta.\n\n"
            "¡Esperamos verte pronto!\n\n"
            "Saludos,\n"
            "{business_name}"
        ),
        body_short=(
            "Todavía tienes saldo en tu tarjeta regalo de {business_name}. "
            "Visita {subdomain} para consultar nuestras reservas."
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.GIFT_CARD_BALANCES,
    ),
    dict(
        name='Pídeles comentarios y opiniones a tus clientes',
        description='',
        title='Dinos qué te parece',
        body=(
            "Nuestros clientes son nuestra motivación. ¡Gracias a ti podemos "
            "hacer lo que nos apasiona! \n\n"
            "Dinos cómo lo estamos haciendo para asegurarnos de que siempre "
            "tengas la mejor experiencia en {business_name}. ¡Y puedes "
            "correr la voz! Visita {subdomain} y déjanos tu opinión en "
            "nuestro perfil de Booksy.\n\n"
            "Te damos las gracias.\n\n"
            "Saludos,\n"
            "{business_name}"
        ),
        body_short=(
            "Dinos cómo lo estamos haciendo. ¡Y puedes correr la voz! "
            "Visita {subdomain} para dejarnos tu opinión en nuestro "
            "perfil de Booksy."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.ASK_FOR_REVIEWS,
    ),
    dict(
        name='Diles a tus clientes que los aprecias.',
        description='',
        title='Solo para que lo sepas...',
        body=(
            "Queríamos escribirte unas palabras de agradecimiento.\n\n"
            "Dirigir una empresa de servicios no siempre es lo que parece a "
            "primera vista. A veces, la noche se hace muy larga, otras, toca "
            "madrugar. También hay momentos de incertidumbre mientras el mundo "
            "sigue cambiando sin parar.\n\n"
            "Cuando contemplamos nuestra clientela y pensamos en las personas "
            "que cruzan la puerta todos los días, todo vale la pena. Tú nos "
            "inspiras a seguir adelante. A aspirar siempre a ser mejores. \n\n"
            "Gracias por apoyar a {business_name}. Solo queremos que sepas "
            "lo importante que eres para nosotros."
        ),
        body_short=(
            "Gracias por apoyar a {business_name}. Solo queremos que "
            "sepas lo importante eres para nosotros."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.APPRECIATE_CUSTOMER,
    ),
    dict(
        name='Diles a tus clientes que has actualizado tus reservas',
        description='',
        title='Acabamos de actualizar nuestras reservas',
        body=(
            "Hace poco, hicimos cambios en nuestras reservas. Visita "
            "{subdomain} para ver las novedades y pide tu cita antes "
            "que nadie.\n\n"
            "¡Esperamos verte pronto!\n\n"
            "Saludos,\n"
            "{business_name}"
        ),
        body_short=(
            "Hace poco, hicimos cambios en nuestras reservas. "
            "Visita {subdomain} para ver las novedades y pide una cita."
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.UPDATED_BOOKS,
    ),
    dict(
        name='Diles a tus clientes dónde pueden reservar',
        description='',
        title='Reserva con nosotros estés donde estés',
        body=(
            "Hemos integrado nuestro perfil de Booksy en todos nuestros "
            "canales en línea para que puedas reservar con nosotros desde "
            "cualquier sitio. ¿Estás curioseando por las redes sociales? "
            "Consulta nuestras reservas. ¿Estás navegando por Internet? "
            "Programa tu cita directamente desde Google. ¿Estás mirando "
            "nuestro sitio web? Haz clic para solicitar una cita.\n\n"
            "La aplicación para clientes de Booksy es la forma más sencilla de "
            "gestionar todas tus citas, pero no es el único lugar donde nos "
            "puedes encontrar.\n\n"
            "Nos vemos pronto,\n"
            "{business_name}"
        ),
        body_short=(
            "Hemos integrado nuestro perfil de Booksy en todos nuestros "
            "canales en línea para que puedas reservar con nosotros desde "
            "cualquier sitio."
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.WHERE_YOU_CAN_BOOK,
    ),
]
