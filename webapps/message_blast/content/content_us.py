# pylint: skip-file
from webapps.message_blast.content.base_en import EN_COMMON_BLASTS_TEMPLATES

from webapps.message_blast.enums import (
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)

US_MESSAGE_BLAST_SPECIAL_OCCASIONS = [
    *EN_COMMON_BLASTS_TEMPLATES,
    # Special Occasions
    dict(
        name='Promote Holiday Bookings',
        description='Send to all clients • Second Monday in November',
        title='Get On Our Books This Holiday Season',
        body=(
            "The holidays are just around the corner and our books are filling "
            "up quickly. Visit {subdomain} to make an appointment before "
            "the festivities begin!\n\n"
            "Wishing you a wonderful season.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "The holidays are just around the corner and our books are filling "
            "up quickly. Visit {subdomain} to make an appointment before "
            "the festivities begin!"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    dict(
        name='Encourage Spring Bookings',
        description='Send to all clients • March 21st',
        title='Freshen Up For Spring',
        body=(
            "It's official, spring has arrived and we're here to help you "
            "recover from what winter left behind. Visit {subdomain} to make "
            "an appointment so that you can freshen up and feel better!\n\n"
            "See you soon!\n"
            "{business_name}"
        ),
        body_short=(
            "Spring has arrived and we're here to help you recover from what "
            "winter left behind. Visit {subdomain} to make an appointment."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.SPRING,
        date_type=MessageBlastDateType.STRICT,
        date_month=3,
        date_day=21,
    ),
    dict(
        name='Sell Father\'s Day Gift Cards',
        description='Send to all clients • Second Monday in June',
        title='Say Thanks to Dad',
        body=(
            "Show your dad how much you appreciate him with a gift card to "
            "{business_name} and let him spend it how he chooses. Visit "
            "{subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Happy Father's Day to all the dads out there!\n\n"
            "All the best,\n"
            "{business_name}"
        ),
        body_short=(
            "Show your dad how much you appreciate him with a gift card to "
            "{business_name}. Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.FATHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=6,
        date_monday=2,
    ),
    dict(
        name='Sell Mother\'s Day Gift Cards',
        description='Send to all clients • First Monday in May',
        title='A Gift for Mom',
        body=(
            "Are you looking for the perfect Mother's Day gift? Get her a "
            "gift card from {business_name} and let her spend it how she "
            "chooses. Visit {subdomain} and navigate to Gift Cards "
            "to purchase.\n\n"
            "Happy Mother's Day to all of the moms out there!\n\n"
            "All the best,\n"
            "{business_name}"
        ),
        body_short=(
            "Looking for the perfect Mother's Day gift? Get her a gift card "
            "from {business_name}. Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MOTHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=5,
        date_monday=1,
    ),
    dict(
        name='Promote Halloween Services',
        description='Send to all clients • First Monday in October',
        title='Take Halloween To The Next Level',
        body=(
            "Want to take your Halloween costume to the next level? Let us "
            "help you get the ghoulish look that you're going for. Visit "
            "{subdomain} to book before the witches and warlocks take them "
            "all.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "Let us help you get the look you're going for this Halloween. "
            "Visit {subdomain} to book before the witches and warlocks take "
            "them all."
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HALLOWEEN,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=10,
        date_monday=1,
    ),
    dict(
        name='Get New Year Bookings',
        description='Send to all clients • First Monday of the year',
        title='New Year, New You?',
        body=(
            "The New Year has arrived and you deserve to welcome it with a "
            "whole new you.\n\n"
            "Visit {subdomain} to book an appointment and celebrate your "
            "fresh start. We can't wait to see you!\n\n"
            "Happy New Year!\n"
            "{business_name}"
        ),
        body_short=(
            "The New Year has arrived and you deserve to welcome it with a "
            "whole new you. Visit {subdomain} to book and celebrate your "
            "fresh start."
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.NEW_YEAR,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=1,
        date_monday=1,
    ),
    dict(
        name='Sell Holiday Gift Cards',
        description='Send to all clients • First Monday in December',
        title='Simplify Your Holiday Shopping',
        body=(
            "Want to simplify your holiday shopping this year? Pick up a "
            "couple of gift cards and let your loved ones choose how they "
            "want to apply them.\n\n"
            "Visit {subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Happy Holidays!\n"
            "{business_name}"
        ),
        body_short=(
            "Simplify your holiday shopping this year and pick up a couple of "
            "gift cards from {business_name}. Visit {subdomain} to "
            "purchase."
        ),
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLDAY_GIFTCARD,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=12,
        date_monday=1,
    ),
    dict(
        name='Sell Valentine\'s Day Gift Cards',
        description='Send to all clients • First Monday in February',
        title='Pamper Your Valentine',
        body=(
            "Valentine's Day is just around the corner. Grab a gift card for "
            "that special someone so that they can enjoy a little "
            "pampering.\n\n"
            "Visit {subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Warmly,\n"
            "{business_name}"
        ),
        body_short=(
            "Suprise your Valentine with a gift card from {business_name}. "
            "Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=8,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.VALENTINES_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=2,
        date_monday=1,
    ),
    dict(
        name='Promote Back To School Services',
        description='Send to all clients • First Monday in August',
        title='Schedule Your Back To School Appointments',
        body=(
            "Back to school season is upon us! If your kids need a refreshed "
            "look after a summer of fun, visit {subdomain} to book their "
            "appointments today.\n\n"
            "See you soon!\n"
            "{business_name}"
        ),
        body_short=(
            "Back to school season is here! If your kids need a refreshed look "
            "after summer visit {subdomain} to book their appointments."
        ),
        image=None,
        recommended=False,
        order=9,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BACK_TO_SCHOOL,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=8,
        date_monday=1,
    ),
    dict(
        name='Tell Clients You\'re Closed for Independence Day',
        description='Send to all clients • June 30th',
        title='We\'re Taking Time Off To Celebrate Our Independence',
        body=(
            "We'll be closed on July 4th for Independence Day. If you need an "
            "appointment before the holiday visit {subdomain} to see our "
            "availability. Otherwise, we look forward to seeing you after "
            "the fireworks.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "Just a reminder {business_name} will be closed on July 4th for "
            "Independence Day. We hope that you have a great holiday! "
        ),
        image=None,
        recommended=False,
        order=10,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INDEPENDENCE_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=6,
        date_day=30,
    ),
    dict(
        name='Tell Clients You\'re Closed for Memorial Day',
        description='Send to all clients • May 24th ',
        title='Reminder, We\'ll Be Closed Memorial Day',
        body=(
            "We'll be closed on May 31st for Memorial Day to remember and "
            "honor those who fought for our freedoms. If you need an "
            "appointment before the holiday visit {subdomain} to see our "
            "availability. Otherwise, we look forward to seeing you after "
            "the long weekend.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "Just a reminder {business_name} will be closed on May 31st for "
            "Memorial Day. We hope that you enjoy the long weekend."
        ),
        image=None,
        recommended=False,
        order=11,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MEMORIAL_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=5,
        date_day=24,
    ),
    dict(
        name='Tell Clients You\'re Closed for Labor Day',
        description='Send to all clients • August 31st',
        title='We\'re Taking A Break For Labor Day',
        body=(
            "Just a reminder that we will be closed on Labor Day. If you need "
            "an appointment before the holiday visit {subdomain} to see our "
            "availability. Otherwise, we look forward to seeing you after the "
            "long weekend.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "Just a reminder {business_name} will be closed on on Labor Day. "
            "We hope that you enjoy the long weekend."
        ),
        image=None,
        recommended=False,
        order=12,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.LABOR_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=8,
        date_day=31,
    ),
    dict(
        name='Tell Clients You\'re Closed for MLK Day',
        description='Send to all clients • January 13th',
        title='We\'ll Be Closed To Honor Martin Luther King Jr.',
        body=(
            "Just a reminder that we will be closed on Martin Luther King, "
            "Jr. Day to honor his accomplishments. If you need an appointment "
            "before the holiday visit {subdomain} to see our availability. "
            "Otherwise, we look forward to seeing you after the long "
            "weekend.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "We will be closed on Martin Luther King, Jr. Day to honor his "
            "accomplishments. Visit {subdomain} if you need an appointment "
            "before the holiday."
        ),
        image=None,
        recommended=False,
        order=13,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MLK_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=1,
        date_day=13,
    ),
    dict(
        name='Offer A Veteran\'s Day Discount',
        description='Send to all clients • November 11',
        title='Sending Gratitude To All Who Have Served',
        body=(
            "Happy Veteran's Day.\n\n"
            "Thank you to all who have served for our country. As a token of "
            "our appreciation we would like to offer [ADD DISCOUNT]% off of "
            "your next visit for current and former members of the military. "
            "Visit {subdomain} to book and then present this offer when you "
            "come in.\n\n"
            "All the best,\n"
            "{business_name}"
        ),
        body_short=(
            "Happy Veteran's Day. Current and former members of the military, "
            "please enjoy a discount on your next appointment. Visit "
            "{subdomain} to book."
        ),
        image=None,
        recommended=False,
        order=14,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.VETERANS_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=11,
        date_day=11,
    ),
    dict(
        name='Offer A Thanksgiving Discount',
        description='Send to all clients • November 20',
        title='We\'re Thankful For You',
        body=(
            "As Thanksgiving approaches we wanted to reach out to say ... "
            "thank you.\n\n"
            "Because of you we get to spend every day doing what we love. "
            "To show our gratitude we would like to offer you [ADD DISCOUNT]% "
            "off of your next visit. Visit {subdomain} to book and then "
            "present this offer when you come in.\n\n"
            "Happy Thanksgiving,\n"
            "{business_name}"
        ),
        body_short=(
            "In spirit of Thanksgiving we would like to offer you "
            "[ADD DISCOUNT]% off of your next visit. Visit {subdomain} "
            "to book."
        ),
        image=None,
        recommended=False,
        order=15,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.THANKSGIVING_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=11,
        date_day=20,
    ),
]
