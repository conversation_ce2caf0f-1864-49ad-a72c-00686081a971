import logging
from django.conf import settings

from webapps.business.consts import MEDICAL_CATEGORIES
from webapps.business.models.category import BusinessCategory
from webapps.public_partners.enum import OAuth2ApplicationCategoryEnum
from webapps.public_partners.models import (
    OAuth2Application,
    OAuth2ApplicationCategory,
    OAuth2ApplicationCategoryLink,
)
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import DBScriptRunner

logger = logging.getLogger('booksy.script_runner')
logger_prefix = "script_add_medical_oauth2applicationcategory__pl.py: "


class Script(ReusableScript, DBScriptRunner):
    version = 2

    def run(self):
        medical_oauth2_category, _ = OAuth2ApplicationCategory.objects.get_or_create(
            name=OAuth2ApplicationCategoryEnum.MEDICAL,
            defaults={'description': "TBD", 'is_active': True},
        )
        oauth_booksy_med_app_filter = OAuth2Application.objects.filter(
            client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID
        )
        if not oauth_booksy_med_app_filter.exists():
            # pylint: disable=consider-using-f-string
            logger.warning(
                "{}OAuth2Application with client_id={} "
                "does not exist".format(
                    logger_prefix,
                    settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
                )
            )
            return
        oauth_booksy_med_app = oauth_booksy_med_app_filter.first()
        if oauth_booksy_med_app.category != medical_oauth2_category:
            oauth_booksy_med_app.category = medical_oauth2_category
            oauth_booksy_med_app.save()

        existing_business_categories = BusinessCategory.objects.filter(
            internal_name__in=MEDICAL_CATEGORIES
        )
        for medical_business_category in existing_business_categories:
            OAuth2ApplicationCategoryLink.objects.get_or_create(
                business_category=medical_business_category,
                application_category=medical_oauth2_category,
            )
