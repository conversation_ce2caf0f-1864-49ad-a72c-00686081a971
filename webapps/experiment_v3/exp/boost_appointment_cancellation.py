from lib.enums import StrEnum
from webapps.experiment_v3.exp.base import BaseExperimentV3


class BoostAppointmentCancellation(BaseExperimentV3):
    class Variants(StrEnum):
        CONTROL = 'control'
        VARIANT_A = 'variant_A'
        VARIANT_B = 'variant_B'
        VARIANT_C = 'variant_C'

    name = 'boost_appointment_cancellation'
    config = {
        'active': False,
        'variants': [
            {
                'name': Variants.CONTROL,
                'control_group': True,
                'weight': 1,
            },
            {
                'name': Variants.VARIANT_A,
                'weight': 1,
            },
            {
                'name': Variants.VARIANT_B,
                'weight': 1,
            },
            {
                'name': Variants.VARIANT_C,
                'weight': 1,
            },
        ],
    }
