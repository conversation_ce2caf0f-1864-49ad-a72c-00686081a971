import io
import logging
from typing import Optional

from rest_framework import status

from lib.celery_tools import celery_task
from lib.feature_flag.feature.feeds import FacebookUpdateServiceTaskLimitationFlag
from lib.tools import id_to_external_api
from lib.locks import FacebookUpdateServicesLock, RedlockError
from webapps.business.models import Business
from webapps.feeds.facebook import config
from webapps.feeds.facebook.api_client import FBEAP<PERSON>lient, FBEAPIError
from webapps.feeds.facebook.config import (
    FBE_FEED_FETCH_ENDPOINT,
    FBE_FEED_UPLOAD_ENDPOINT,
)
from webapps.feeds.facebook.enums import FacebookErrorCode


class FBException(Exception):
    pass


class FBTokenExpiredException(FBException):
    pass


logger = logging.getLogger('booksy.facebook.tasks')


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def update_fb_services_task(business_id: int) -> None:
    business = Business.objects.only(
        'city',
        'id',
        'integrations',
        'name',
        'primary_category',
        'region',
    ).get(id=business_id)

    if not business.is_fbe_connected():
        logger.info('Business %s does not have FB integration', business)
        return

    connect_data = business.get_facebook_fbe_connection_data()
    access_token = connect_data.access_token
    catalog_id = connect_data.catalog_id

    api_client = FBEAPIClient()

    if FacebookUpdateServiceTaskLimitationFlag():
        try:
            lock_ = FacebookUpdateServicesLock.lock(business_id)
        except RedlockError:
            lock_ = None
        if not lock_:
            logger.warning(
                'Skip FB services feed for a business %s because of not ended task', business_id
            )
            return

    try:
        if feed_id := _get_feed_id(catalog_id, access_token, api_client):
            _send_services_feed(business, access_token, feed_id, api_client)
        else:
            raise FBException(f'Empty feed_id for business {business}')
    except FBTokenExpiredException:
        business.remove_facebook_fbe_connection()
        raise  # task failed, we only prevented future errors by disabling fb integration
    finally:
        if FacebookUpdateServiceTaskLimitationFlag():
            FacebookUpdateServicesLock.unlock(lock_)


def _get_feed_id(
    catalog_id: str,
    access_token: str,
    client: FBEAPIClient,
) -> Optional[str]:
    endpoint = FBE_FEED_FETCH_ENDPOINT.format(catalog_id=catalog_id)
    logger.info('FBE Graph get feed_id request: endpoint: %s', endpoint)
    try:
        result = client.get(endpoint, params={'access_token': access_token})
    except FBEAPIError as exc:
        if exc.status_code == status.HTTP_400_BAD_REQUEST:
            fb_error_code = exc.body['error']['code']
            if fb_error_code == FacebookErrorCode.ACCESS_TOKEN_HAS_EXPIRED:
                raise FBTokenExpiredException from exc
        raise
    if 'data' in result:
        return result['data'][0]['id']
    raise FBException(f'Cannot get id from: {result}')


def _send_services_feed(
    business: Business,
    access_token: str,
    feed_id: str,
    client: FBEAPIClient,
) -> None:
    from webapps.feeds.facebook.tools import (
        build_services_xml_for_fb,
    )  # pylint: disable=cyclic-import

    xml_data = build_services_xml_for_fb(business)
    files = {
        'file': (
            'catalog.xml',
            io.BytesIO(xml_data),
            'text/xml',
            {'Content-Disposition': 'attachment'},
        ),
        'access_token': (None, access_token),
    }
    endpoint = FBE_FEED_UPLOAD_ENDPOINT.format(feed_id=feed_id)
    logger.info(
        'FBE Graph upload feed request: endpoint: %s, xml_data: %r',
        endpoint,
        xml_data,
    )
    client.post(endpoint, files=files)


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def disconnect_business_from_fbe_task(business_id: int, access_token: str) -> None:
    client = FBEAPIClient()
    params = {
        'access_token': access_token,
        'fbe_external_business_id': id_to_external_api(business_id),
    }
    client.delete(config.FBE_DETAILS_ENDPOINT, params=params)
