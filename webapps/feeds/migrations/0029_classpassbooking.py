# Generated by Django 3.1.8 on 2021-05-18 14:19

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0129_create_partner_booking_sources'),
        ('business', '0331_businesscustomerinfo_client_type'),
        ('feeds', '0028_grouponbooking'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClassPassBooking',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('booked_from', models.DateTimeField(verbose_name='Booked from (UTC)')),
                ('booked_till', models.DateTimeField(verbose_name='Booked till (UTC)')),
                ('appointment_id', models.CharField(max_length=255)),
                ('user_id', models.CharField(max_length=255)),
                ('user_email', models.CharField(max_length=255)),
                ('user_username', models.CharField(max_length=255)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                (
                    'gender',
                    models.CharField(
                        choices=[
                            ('Male', 'MALE'),
                            ('Female', 'FEMALE'),
                            ('Unspecified', 'UNSPECIFIED'),
                        ],
                        max_length=50,
                    ),
                ),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('birthday', models.DateField(blank=True, null=True)),
                ('address_line1', models.CharField(blank=True, max_length=100, null=True)),
                ('address_line2', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('zip', models.CharField(blank=True, max_length=10, null=True)),
                ('country', models.CharField(blank=True, max_length=2, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=255, null=True)),
                (
                    'emergency_contact_phone',
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('ENROLLED', 'ENROLLED'),
                            ('ATTENDED', 'ATTENDED'),
                            ('CANCELLED', 'CANCELLED'),
                            ('LATE_CANCELLED', 'LATE_CANCELLED'),
                            ('APPOINTMENT_CANCELLED', 'APPOINTMENT_CANCELLED'),
                            ('MISSED', 'MISSED'),
                        ],
                        max_length=255,
                    ),
                ),
                ('customization', models.CharField(blank=True, max_length=1024, null=True)),
                (
                    'appliance',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='classpass_appliance',
                        to='business.resource',
                    ),
                ),
                (
                    'booking',
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='classpass_booking',
                        to='booking.subbooking',
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='business.business'
                    ),
                ),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='business.servicevariant'
                    ),
                ),
                (
                    'staffer',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='classpass_staffer',
                        to='business.resource',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
