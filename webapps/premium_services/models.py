from django.db import models
from django.utils.translation import gettext_lazy as _

from lib.models import ArchiveManager, ArchiveModel
from webapps.premium_services.enums import DayOfWeek, SurchargeType


class PeakHourModel(ArchiveModel):
    business_id = models.IntegerField(db_index=True)
    active = models.BooleanField(default=True, db_index=True)
    service_variant_id = models.IntegerField()
    hour_from = models.TimeField()
    hour_till = models.TimeField()
    elevation_rate = models.DecimalField(max_digits=3, decimal_places=0)
    day_of_week = models.PositiveSmallIntegerField(
        choices=DayOfWeek.choices(),
        blank=True,
        null=True,
        db_index=True,
    )


class SubBookingSurcharge(ArchiveModel):
    subbooking = models.ForeignKey(
        'booking.SubBooking',
        related_name='surcharges',
        on_delete=models.DO_NOTHING,
        null=False,
        db_constraint=False,
    )
    surcharge_type = models.CharField(
        max_length=2,
        choices=SurchargeType.choices(),
        null=False,
    )
    rate = models.DecimalField(
        max_digits=3,
        decimal_places=0,
        null=False,
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=False,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['subbooking'],
                condition=models.Q(deleted__isnull=True),
                name='subbooking_surcharge_subbooking_unique',
            ),
        ]

    objects = ArchiveManager()
    all_objects = models.Manager()
