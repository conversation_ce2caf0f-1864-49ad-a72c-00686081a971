from logging import getLogger

from collections import defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal

from lib.tools import tznow
from v2.shared.hexagonal.ports import TransactionPort
from webapps.business.ports.service_variant import ServiceVariantPort
from webapps.premium_services.enums import (
    DayOfWeek,
    FeatureLabel,
    FeatureStatus,
    FeatureStatusColor,
)
from webapps.premium_services.peak_hours.domain.models import PeakHour, PeakHoursFeatureStatus
from webapps.premium_services.peak_hours.domain.repositories import PeakHourRepository
from webapps.premium_services.ports import SubBookingSurchargePort
from webapps.premium_services.surcharges import SubBookingSurchargeData

logger = getLogger('booksy.premium_services')


class PeakHourService:
    def __init__(
        self,
        peak_hour_repo: PeakHourRepository,
        surcharge_port: SubBookingSurchargePort,
        service_variant_port: ServiceVariantPort,
        transaction_port: TransactionPort,
    ):
        self._peak_hour_repo = peak_hour_repo
        self._surcharge_port = surcharge_port
        self._service_variant_port = service_variant_port
        self._transaction_port = transaction_port

    def _group_by_day(
        self, business_id: int, requested_peak_hours: list[PeakHour]
    ) -> list[PeakHour]:
        services_by_day = defaultdict(list)
        for ph in requested_peak_hours:
            services_by_day[ph.day_of_week].extend(ph.service_variants)

        return [
            PeakHour(business_id=business_id, day_of_week=day, service_variants=services)
            for day, services in services_by_day.items()
        ]

    def _validate_against_active_service_variants(
        self, business_id: int, requested_peak_hours: list[PeakHour]
    ) -> list[PeakHour]:
        peak_hours = self._group_by_day(business_id, requested_peak_hours)
        service_variant_ids = {
            sv.service_variant_id for ph in peak_hours for sv in ph.service_variants
        }
        service_variants_with_price = self._service_variant_port.get_all_with_price_by_id(
            service_variant_ids=service_variant_ids
        )
        valid_service_variant_ids = {
            sv.id for sv in service_variants_with_price if sv.business_id == business_id
        }

        return [
            PeakHour(
                business_id=business_id,
                day_of_week=ph.day_of_week,
                service_variants=[
                    sv
                    for sv in ph.service_variants
                    if sv.service_variant_id in valid_service_variant_ids
                ],
            )
            for ph in peak_hours
        ]

    def _calculate_last_action_date(self, business_id: int, *, since: datetime) -> datetime | None:
        peak_hours = self._peak_hour_repo.get_all_for_business(business_id, since=since)
        last_action_dates = (
            ph.deleted if ph.deleted else ph.created for ph in peak_hours if ph.created
        )
        last_action_date = max(last_action_dates, default=None)

        return last_action_date

    def _estimated_revenue(self, surcharges: list[SubBookingSurchargeData]) -> Decimal:
        return Decimal(
            sum(
                surcharge.amount
                for surcharge in surcharges
                if surcharge.appointment_finished_status() and surcharge.combo_parent_id is None
            )
        )

    def _appointments_count(self, surcharges: list[SubBookingSurchargeData]) -> int:
        return len({surcharge.appointment_id for surcharge in surcharges})

    def get_active_services(self, business_id: int) -> list[PeakHour]:
        peak_hours = self._peak_hour_repo.find_for_business(business_id, DayOfWeek)
        return self._validate_against_active_service_variants(business_id, peak_hours)

    def get_services(
        self, business_id: int, *, active_at: datetime | None = None
    ) -> list[PeakHour]:
        peak_hours = self._peak_hour_repo.find_for_business(business_id, DayOfWeek, date=active_at)
        return self._validate_against_active_service_variants(business_id, peak_hours)

    def enable(
        self,
        business_id: int,
        requested_peak_hours: list[PeakHour],
    ) -> list[PeakHour | None]:
        with self._transaction_port.atomic():
            days_of_week = [ph.day_of_week for ph in requested_peak_hours]
            enabled_peak_hours = self._peak_hour_repo.find_for_business(business_id, days_of_week)
            validated_peak_hours = self._validate_against_active_service_variants(
                business_id, requested_peak_hours
            )
            for ph in enabled_peak_hours:
                self._peak_hour_repo.delete(ph)

            self._peak_hour_repo.save(validated_peak_hours)
            logger.info('[PREMIUM SERVICES] Enabled peak hours for business: %s', business_id)
            return requested_peak_hours

    def disable(
        self,
        business_id: int,
        requested_peak_hours: list[PeakHour],
    ) -> None:
        with self._transaction_port.atomic():
            days_of_week = [ph.day_of_week for ph in requested_peak_hours]
            enabled_peak_hours = self._peak_hour_repo.find_for_business(business_id, days_of_week)
            for ph in enabled_peak_hours:
                self._peak_hour_repo.delete(ph)
        logger.info('[PREMIUM SERVICES] Disabled peak hours for business: %s', business_id)

    def get_status(self, business_id: int) -> PeakHoursFeatureStatus:
        year_ago = tznow() - timedelta(days=365)
        has_active_services = bool(self.get_active_services(business_id))
        surcharges = self._surcharge_port.get_all_valid_for_business(business_id, since=year_ago)

        return PeakHoursFeatureStatus(
            estimated_revenue=self._estimated_revenue(surcharges),
            appointments_count=self._appointments_count(surcharges),
            last_action_date=self._calculate_last_action_date(business_id, since=year_ago),
            status=FeatureStatus.ACTIVE if has_active_services else FeatureStatus.INACTIVE,
            status_color=(
                FeatureStatusColor.GREEN if has_active_services else FeatureStatusColor.GRAY
            ),
            label=FeatureLabel.ACTIVE if has_active_services else FeatureLabel.INACTIVE,
        )
