# Generated by Django 4.2.14 on 2024-08-06 07:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business_calendar", "0005_alter_bannerhistory_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="bannerhistory",
            name="type",
            field=models.CharField(
                choices=[
                    (None, "NO_BANNER"),
                    ("PAYOUTS", "ENABLE_PAYOUTS"),
                    ("BLIK_KYCED", "BLIK_KYCED"),
                    ("BLIK_NOT_KYCED", "BLIK_NOT_KYCED"),
                    ("BLIK_FOR_FREE_NOT_KYCED", "BLIK_FOR_FREE_NOT_KYCED"),
                    ("BLIK_FOR_FREE_KYCED", "BLIK_FOR_FREE_KYCED"),
                    ("TTP_REMINDER", "TTP_REMINDER"),
                    ("PAYOUTS_AMOUNT", "PAYOUTS_AMOUNT"),
                    ("MAKE_YOUR_PROFILE_PUBLIC", "MAKE_YOUR_PROFILE_PUBLIC"),
                ]
            ),
        ),
    ]
