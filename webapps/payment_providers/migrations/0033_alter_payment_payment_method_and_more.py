# Generated by Django 4.2.18 on 2025-02-04 16:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payment_providers', '0032_payoutmethodchangelog'),
    ]

    operations = [
        migrations.AlterField(
            model_name='payment',
            name='payment_method',
            field=models.CharField(
                choices=[
                    ('terminal', 'Terminal'),
                    ('card', 'Card (Mobile Payment)'),
                    ('google_pay', 'Google Pay'),
                    ('apple_pay', 'Apple Pay'),
                    ('tap_to_pay', 'Tap To Pay'),
                    ('booksy_gift_card', 'Booksy Gift Card'),
                    ('blik', 'Blik'),
                    ('quick_card_entry', 'Quick Card Entry'),
                ],
                db_index=True,
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name='setupintent',
            name='method_type',
            field=models.CharField(
                choices=[
                    ('terminal', 'Terminal'),
                    ('card', 'Card (Mobile Payment)'),
                    ('google_pay', 'Google Pay'),
                    ('apple_pay', 'Apple Pay'),
                    ('tap_to_pay', 'Tap To Pay'),
                    ('booksy_gift_card', 'Booksy Gift Card'),
                    ('blik', 'Blik'),
                    ('quick_card_entry', 'Quick Card Entry'),
                ],
                db_index=True,
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name='tokenizedpaymentmethod',
            name='method_type',
            field=models.CharField(
                choices=[
                    ('terminal', 'Terminal'),
                    ('card', 'Card (Mobile Payment)'),
                    ('google_pay', 'Google Pay'),
                    ('apple_pay', 'Apple Pay'),
                    ('tap_to_pay', 'Tap To Pay'),
                    ('booksy_gift_card', 'Booksy Gift Card'),
                    ('blik', 'Blik'),
                    ('quick_card_entry', 'Quick Card Entry'),
                ],
                db_index=True,
                max_length=20,
            ),
        ),
    ]
