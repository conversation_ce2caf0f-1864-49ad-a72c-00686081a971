from dataclasses import asdict

import mock
import pytest
from mock import call, MagicMock
from model_bakery import baker

from django.test import TestCase
from django.test.utils import override_settings

import settings
from country_config import Country

from lib.business_consents.events import account_holder_nip_consent_accepted_event
from lib.payment_providers.enums import StripeAccountType
from lib.payment_providers.entities import AccountHolderNIPEntity
from webapps.payment_providers.exceptions.common import BusinessKYCCompanyClosedException

from webapps.payment_providers.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    StripeAccountHolder,
)
from webapps.payment_providers.tests.stripe.samples.rejestr_io.closed_company import (
    REJESTRIO_CLOSED_COMPANY,
)
from webapps.payment_providers.tests.stripe.samples.rejestr_io.oz import (
    REJESTRIO_ODDZIAL_ZAGRANICZNEGO_PRZEDSIEBIORCY,
)
from webapps.payment_providers.tests.stripe.samples.rejestr_io.sa import REJESTRIO_SA
from webapps.payment_providers.tests.stripe.samples.rejestr_io.sa_simple import (
    REJESTRIO_SA_SIMPLE_1,
    REJESTRIO_SA_SIMPLE_2,
)
from webapps.payment_providers.tests.stripe.samples.rejestr_io.sj import REJESTRIO_SJ
from webapps.payment_providers.tests.stripe.samples.rejestr_io.sk import REJESTRIO_SK
from webapps.payment_providers.tests.stripe.samples.rejestr_io.ska import REJESTRIO_SKA
from webapps.payment_providers.tests.stripe.samples.rejestr_io.sp import REJESTRIO_SP
from webapps.payment_providers.tests.stripe.samples.rejestr_io.spzoo import (
    REJESTRIO_SPZOO_1,
    REJESTRIO_SPZOO_2,
    REJESTRIO_SPZOO_3,
    REJESTRIO_SPZOO_4,
    REJESTRIO_SPZOO_5,
    REJESTRIO_SPZOO_6,
)


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.PL)
@mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_account_holder_wallet')
@mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_business_wallet')
@mock.patch(
    'webapps.payment_providers.adapters.stripe_integration.'
    'StripeIntegrationAdapter.get_or_create_stripe_account',
)
@mock.patch('webapps.payment_providers.providers.stripe.StripeProvider.create_person')
@mock.patch('webapps.payment_providers.providers.stripe.StripeProvider.create_person_token')
@mock.patch(
    'webapps.payment_providers.providers.stripe.StripeProvider.create_account_holder_token',
)
@mock.patch(
    'webapps.payment_providers.providers.kyc_data_providers.'
    'rejestrio_pl.RejestrIOPLProvider._fetch_rejestrio',
)
class TestMigrateAccountHolderRejestrIOPL(TestCase):
    def setUp(self):
        self.account_holder = baker.make(
            AccountHolder,
            statement_name='account_holder',
            metadata={'business_id': 1},
        )

    def create_stripe_account_adapter_mock_side_effect(self, account_holder, **kwargs):
        self.assertDictEqual(
            kwargs,
            {
                'account_token': 'create_account_token',
                'force_synchronize_with_new_structure': True,
                'account_type': StripeAccountType.CUSTOM,
                'request_params': {
                    'type': 'custom',
                    'business_profile': {
                        'url': 'www.booksy.com',
                    },
                    'capabilities': {
                        'transfers': {
                            'requested': True,
                        },
                    },
                },
            },
        )
        baker.make(
            StripeAccountHolder,
            account_holder=account_holder,
        )

    def test_create_account_holder_from_external_source__spzoo_1(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_1
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'KANAŁ SPORTOWY SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Konstruktorska 13',
                                    'line2': None,
                                    'postal_code': '02-673',
                                    'city': 'Warszawa',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Tomasz Wojciech',
                            'last_name': 'Smokowski',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,  # only 1 rep (Sawicki)
                            },
                            'dob': {'day': '25', 'month': '05', 'year': '1973'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Michał Krzysztof',
                            'last_name': 'Pol',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '10', 'month': '11', 'year': '1969'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Mateusz Jakub',
                            'last_name': 'Borek',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '02', 'month': '11', 'year': '1973'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Maciej',
                            'last_name': 'Sawicki',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '20', 'month': '01', 'year': '1979'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 4)
        self.assertEqual(create_person_mock.call_count, 4)

    def test_create_account_holder_from_external_source__spzoo_2(  # pylint: disable=line-too-long
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_2
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'BOOKSY INTERNATIONAL SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Prosta 67',
                                    'line2': None,
                                    'postal_code': '00-838',
                                    'city': 'Warszawa',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Paweł',
                            'last_name': 'Sobkowiak',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '03', 'month': '06', 'year': '1982'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Przemysław Łukasz',
                            'last_name': 'Barszcz',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '24', 'month': '06', 'year': '1981'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__spzoo_3(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_3
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'RENZ SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Magazynowa 5A',
                                    'line2': None,
                                    'postal_code': '62-023',
                                    'city': 'Gądki',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Armin',
                            'last_name': 'Renz',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Martin Michael',
                            'last_name': 'Ebinger',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Piotr Antoni',
                            'last_name': 'Dymek',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '15', 'month': '06', 'year': '1973'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 3)
        self.assertEqual(create_person_mock.call_count, 3)

    def test_create_account_holder_from_external_source__spzoo_4(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_4
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'ASANTE GROUP SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Pory 55A',
                                    'line2': '18',
                                    'postal_code': '02-757',
                                    'city': 'Warszawa',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Barbara Natalia',
                            'last_name': 'Buksowicz',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '14', 'month': '06', 'year': '2001'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Tomasz Krzysztof',
                            'last_name': 'Zadrożny',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '01', 'month': '09', 'year': '1975'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__spzoo_5(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_5
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'OAZA SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Dywizjonu 303 21',
                                    'line2': '3',
                                    'postal_code': '80-462',
                                    'city': 'Gdańsk',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Sławomir Tomasz',
                            'last_name': 'Kruszyński',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '08', 'month': '06', 'year': '1960'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Marcin Sławomir',
                            'last_name': 'Kruszyński',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '04', 'month': '01', 'year': '1983'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__spzoo_6(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SPZOO_6
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'BLUEWINGS SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
                                'address': {
                                    'line1': 'Zielna 3',
                                    'line2': None,
                                    'postal_code': '62-002',
                                    'city': 'Suchy Las',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Adam Leopold',
                            'last_name': 'Martin',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '23', 'month': '12', 'year': '1963'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Katarzyna Stefania',
                            'last_name': 'Piotrowska',
                            'relationship': {
                                'owner': True,
                                'director': False,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '03', 'month': '01', 'year': '1970'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__sp(  # pylint: disable=line-too-long
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SP
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'NZOZ STOMATOLOGIA WOJCIECHOWSCY SPÓŁKA PARTNERSKA (DAWNIEJ NZOZ STOMATOLOGIA WOJCIECHOWSCY SPÓŁKA CY',  # should cut at 100
                                'address': {
                                    'line1': 'Mickiewicza 1',
                                    'line2': None,
                                    'postal_code': '32-620',
                                    'city': 'Brzeszcze',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'incorporated_partnership',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Anna',
                            'last_name': 'Wojciechowska',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '24', 'month': '07', 'year': '1975'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Jacek',
                            'last_name': 'Wojciechowski',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '14', 'month': '04', 'year': '1975'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__sk(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SK
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'ZŁOMPOL R. CIMOSZYŃSKI J. MAJEWSKI SPÓŁKA KOMANDYTOWA',
                                'address': {
                                    'line1': 'Leśna 66',
                                    'line2': None,
                                    'postal_code': '05-555',
                                    'city': 'Jeziorzany',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'incorporated_partnership',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Robert Zbigniew',
                            'last_name': 'Cimoszyński',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '26', 'month': '09', 'year': '1969'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Janusz Bogdan',
                            'last_name': 'Majewski',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '10', 'month': '06', 'year': '1961'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__sj(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SJ
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'UNOBRAND SPÓŁKA JAWNA BARTNICZAK',
                                'address': {
                                    'line1': 'Drozdowo 7',
                                    'line2': None,
                                    'postal_code': '06-225',
                                    'city': 'Drozdowo',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'incorporated_partnership',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Arkadiusz',
                            'last_name': 'Bartniczak',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '16', 'month': '02', 'year': '1977'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Anna Magdalena',
                            'last_name': 'Bartniczak',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '23', 'month': '12', 'year': '1977'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__ska(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SKA
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'TBO TASIOR KOMANDYTOWO-AKCYJNA',
                                'address': {
                                    'line1': 'Czarna 444A',
                                    'line2': None,
                                    'postal_code': '37-125',
                                    'city': 'Czarna',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'incorporated_partnership',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Ireneusz Jan',
                            'last_name': 'Tasior',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '17', 'month': '06', 'year': '1987'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 1)
        self.assertEqual(create_person_mock.call_count, 1)

    def test_create_account_holder_from_external_source__sa(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SA
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'KANAŁ ZERO SPÓŁKA AKCYJNA',
                                'address': {
                                    'line1': 'Szara 10',
                                    'line2': None,
                                    'postal_code': '00-420',
                                    'city': 'Warszawa',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'public_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Krzysztof Jakub',
                            'last_name': 'Stanowski',
                            'relationship': {
                                'owner': True,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '21', 'month': '07', 'year': '1982'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Bogusław',
                            'last_name': 'Leśnodorski',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '10', 'month': '07', 'year': '1975'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Marta',
                            'last_name': 'Sosnowska',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '08', 'month': '10', 'year': '1984'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Emil',
                            'last_name': 'Tomaszewski',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '11', 'month': '08', 'year': '1987'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 4)
        self.assertEqual(create_person_mock.call_count, 4)

    def test_create_account_holder_from_external_source__sa_simple_1(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SA_SIMPLE_1
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'LAWSTARS PROSTA SPÓŁKA AKCYJNA',
                                'address': {
                                    'line1': 'Sądowa 2',
                                    'line2': '11',
                                    'postal_code': '20-027',
                                    'city': 'Lublin',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'public_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Piotr Krzysztof',
                            'last_name': 'Swieboda',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                            'dob': {'day': '28', 'month': '09', 'year': '1978'},
                        },
                    }
                ),
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Paweł',
                            'last_name': 'Niniewski',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': False,  # only 1 rep
                            },
                            'dob': {'day': '25', 'month': '05', 'year': '1978'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 2)
        self.assertEqual(create_person_mock.call_count, 2)

    def test_create_account_holder_from_external_source__sa_simple_2(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_SA_SIMPLE_2
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'PROSTA-SPOLKA.PL PROSTA SPÓŁKA AKCYJNA',
                                'address': {
                                    'line1': 'Szkolna 5',
                                    'line2': '18',
                                    'postal_code': '61-832',
                                    'city': 'Poznań',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'public_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Sandra Katarzyna',
                            'last_name': 'Kurpisz Śliwa',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': False,
                                'representative': False,
                            },
                            'dob': {'day': '11', 'month': '07', 'year': '1992'},
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 1)
        self.assertEqual(create_person_mock.call_count, 1)

    def test_create_account_holder_from_external_source__closed_company(
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_CLOSED_COMPANY
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        with self.assertRaises(BusinessKYCCompanyClosedException):
            account_holder_nip_consent_accepted_event.send(
                asdict(
                    AccountHolderNIPEntity(
                        business_id=1,
                        nip="**********",
                        date_of_birth=None,
                    )
                ),
            )

    def test_create_account_holder_from_external_source__oz(  # pylint: disable=line-too-long
        self,
        rejestrio_call_mock,
        create_account_token_mock,
        create_person_token_mock,
        create_person_mock,
        create_stripe_account_adapter_mock,
        get_business_wallet_mock,
        get_account_holder_wallet_mock,
    ):
        account_token_id = 'create_account_token'
        rejestrio_call_mock.return_value = REJESTRIO_ODDZIAL_ZAGRANICZNEGO_PRZEDSIEBIORCY
        create_stripe_account_adapter_mock.side_effect = (
            self.create_stripe_account_adapter_mock_side_effect
        )
        create_account_token_mock.return_value = MagicMock(id=account_token_id)
        get_business_wallet_mock.return_value = MagicMock(account_holder_id=self.account_holder.id)
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=1)

        account_holder_nip_consent_accepted_event.send(
            asdict(
                AccountHolderNIPEntity(
                    business_id=1,
                    nip="**********",
                    date_of_birth=None,
                )
            ),
        )

        create_account_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'account': {
                            'business_type': 'company',
                            'tos_shown_and_accepted': True,
                            'company': {
                                'name': 'POWER SERWICE LTD SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ ODDZIAŁ W POLSCE',
                                'address': {
                                    'line1': 'Stanisława Leszczyńskiego 4',
                                    'line2': '25',
                                    'postal_code': '50-078',
                                    'city': 'Wrocław',
                                },
                                'tax_id': '*********',
                                'vat_id': '**********',
                                'structure': 'private_corporation',
                            },
                        },
                    }
                ),
                call(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    },
                ),
            ]
        )
        self.assertEqual(create_account_token_mock.call_count, 2)

        create_person_token_mock.assert_has_calls(
            [
                call(
                    data={
                        'api_key': '',
                        'person': {
                            'first_name': 'Mateusz',
                            'last_name': 'Surowiec',
                            'relationship': {
                                'owner': False,
                                'director': True,
                                'executive': True,
                                'representative': True,
                            },
                        },
                    }
                ),
            ]
        )
        self.assertEqual(create_person_token_mock.call_count, 1)
        self.assertEqual(create_person_mock.call_count, 1)
