import pytest
from django.core.management import call_command

from webapps.business.baker_recipes import business_recipe
from webapps.pos.baker_recipes import pos_recipe, payment_type_recipe
from webapps.pos.enums import PaymentTypeEnum
from webapps.stripe_integration.baker_recipes import stripe_account_recipe
from webapps.stripe_integration.enums import StripeAccountStatus


@pytest.mark.django_db
def test_create_blik_payment_type():
    business_1 = business_recipe.make()
    business_2 = business_recipe.make()
    business_3 = business_recipe.make()
    pos_1 = pos_recipe.make(business=business_1)
    pos_2 = pos_recipe.make(business=business_2)
    pos_3 = pos_recipe.make(business=business_3)

    stripe_account_recipe.make(pos=pos_1, status=StripeAccountStatus.VERIFIED)

    poses = [pos_1, pos_2, pos_3]
    for pos in poses:
        assert not pos.payment_types.filter(code=PaymentTypeEnum.BLIK).exists()

    call_command('add_blik_payment_type')

    assert pos_1.payment_types.filter(code=PaymentTypeEnum.BLIK).exists()
    assert not pos_2.payment_types.filter(code=PaymentTypeEnum.BLIK).exists()
    assert not pos_3.payment_types.filter(code=PaymentTypeEnum.BLIK).exists()


@pytest.mark.django_db
def test_create_blik_payment_type_and_move_other_payment_types():
    business_1 = business_recipe.make()
    business_2 = business_recipe.make()
    business_3 = business_recipe.make()
    pos_1 = pos_recipe.make(business=business_1)
    pos_2 = pos_recipe.make(business=business_2)
    pos_3 = pos_recipe.make(business=business_3)

    stripe_account_recipe.make(pos=pos_3, status=StripeAccountStatus.VERIFIED)

    cash = payment_type_recipe.make(pos=pos_1, code=PaymentTypeEnum.CASH, order=3)
    blik = payment_type_recipe.make(pos=pos_2, code=PaymentTypeEnum.BLIK, order=0)

    call_command('add_blik_payment_type', '--move-others-payment-types')

    cash.refresh_from_db()
    assert cash.order == 4  # moved forward
    blik.refresh_from_db()
    assert blik.order == 0  # no changes

    assert pos_3.payment_types.filter(code=PaymentTypeEnum.BLIK).exists()
