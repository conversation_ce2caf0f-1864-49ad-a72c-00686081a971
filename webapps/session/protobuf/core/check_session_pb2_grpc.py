# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from webapps.session.protobuf.core import check_session_pb2 as webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2


class CheckSessionExistsStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CheckSessionExists = channel.unary_unary(
                '/check_session.CheckSessionExists/CheckSessionExists',
                request_serializer=webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsRequest.SerializeToString,
                response_deserializer=webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsResponse.FromString,
                )


class CheckSessionExistsServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CheckSessionExists(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CheckSessionExistsServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CheckSessionExists': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckSessionExists,
                    request_deserializer=webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsRequest.FromString,
                    response_serializer=webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'check_session.CheckSessionExists', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CheckSessionExists(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CheckSessionExists(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/check_session.CheckSessionExists/CheckSessionExists',
            webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsRequest.SerializeToString,
            webapps_dot_booksy__session_dot_protobuf_dot_core_dot_check__session__pb2.CheckSessionExistsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
