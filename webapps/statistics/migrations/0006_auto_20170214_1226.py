# Generated by Django 1.10.5 on 2017-02-14 12:26
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('statistics', '0005_auto_20170213_1501'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='businessstatistics',
            name='count',
        ),
        migrations.RemoveField(
            model_name='businessstatistics',
            name='value',
        ),
        migrations.RemoveField(
            model_name='businessstatistics',
            name='value_type',
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='bookings_canceled',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='bookings_finished',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='bookings_no_show',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='deposits_revenue',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='products_count',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='products_revenue',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='services_count',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='services_revenue',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='businessstatistics',
            name='tips_revenue',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
    ]
