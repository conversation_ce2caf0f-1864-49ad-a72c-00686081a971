from dataclasses import dataclass

from webapps.google_business_profile.shared import WebsiteUri, LocationId


@dataclass(frozen=True)
class LocationAddress:
    region_code: str
    language_code: str
    postal_code: str
    administrative_area: str
    locality: str
    address_lines: list[str]


@dataclass(frozen=True)
class LocationCategory:
    name: str
    display_name: str | None = None
    category_id: str | None = None


@dataclass(frozen=True)
class LocationCategories:
    primary_category: LocationCategory


@dataclass(frozen=True)
class LocationPhoneNumbers:
    primary_phone: str
    additional_phones: list[str] | None = None


@dataclass(frozen=True)
class LocationOpenInfo:
    status: str


@dataclass(frozen=True)
class LocationDTO:
    name: str | None
    location_id: LocationId
    title: str
    phone_numbers: LocationPhoneNumbers
    categories: LocationCategories
    storefront_address: LocationAddress
    website_uri: WebsiteUri | None = None
    open_info: LocationOpenInfo | None = None


@dataclass(frozen=True)
class SimpleLocationDTO:
    location_id: LocationId
    title: str
    address: str
