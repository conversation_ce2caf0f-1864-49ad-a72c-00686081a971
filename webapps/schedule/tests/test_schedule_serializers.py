from datetime import time

from webapps.schedule.base_serializers import (
    LegacyHoursSerializer,
    WeekHoursSerializer,
)


def test_week_hours_representation(business_opening_hours):
    serializer = WeekHoursSerializer(
        many=True,
        instance=business_opening_hours.hours,
    )
    assert serializer.data == [
        dict(day_of_week=0, hours=[]),
        dict(day_of_week=1, hours=[dict(hour_from='10:00', hour_till='18:00')]),
        dict(day_of_week=2, hours=[dict(hour_from='10:00', hour_till='18:00')]),
        dict(day_of_week=3, hours=[dict(hour_from='10:00', hour_till='18:00')]),
        dict(day_of_week=4, hours=[dict(hour_from='10:00', hour_till='18:00')]),
        dict(day_of_week=5, hours=[dict(hour_from='10:00', hour_till='18:00')]),
        dict(day_of_week=6, hours=[]),
    ], 'Wrong BusinessOpeningHours representation'


def test_week_hours_validation():
    data = [
        dict(day_of_week=1, hours=[dict(hour_from='09:00', hour_till='20:00')]),
        dict(
            day_of_week=3,
            hours=[
                dict(hour_from='09:00', hour_till='12:00'),
                dict(hour_from='13:00', hour_till='15:00'),
            ],
        ),
    ]
    serializer = WeekHoursSerializer(many=True, data=data)

    assert serializer.is_valid(), serializer.errors
    assert serializer.validated_data[1] == [(time(9), time(20))]
    assert serializer.validated_data[3] == [(time(9), time(12)), (time(13), time(15))]


def test_legacy_hours_representation(business_opening_hours):
    serializer = LegacyHoursSerializer(
        many=True,
        instance=business_opening_hours.hours,
    )
    assert serializer.data == [
        dict(day_of_week=1, hour_from='10:00', hour_till='18:00'),
        dict(day_of_week=2, hour_from='10:00', hour_till='18:00'),
        dict(day_of_week=3, hour_from='10:00', hour_till='18:00'),
        dict(day_of_week=4, hour_from='10:00', hour_till='18:00'),
        dict(day_of_week=5, hour_from='10:00', hour_till='18:00'),
    ], 'Wrong BusinessOpeningHours representation'


def test_legacy_hours_validation():
    data = [
        dict(day_of_week=1, hour_from='09:00', hour_till='20:00'),
        dict(day_of_week=3, hour_from='09:00', hour_till='12:00'),
        dict(day_of_week=3, hour_from='13:00', hour_till='15:00'),
    ]
    serializer = LegacyHoursSerializer(many=True, data=data)

    assert serializer.is_valid()
    assert serializer.validated_data[1] == [(time(9), time(20))]
    assert serializer.validated_data[3] == [(time(9), time(12)), (time(13), time(15))]
