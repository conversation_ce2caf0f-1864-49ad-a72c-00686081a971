from tqdm import tqdm
from django.core.management.base import BaseCommand

from lib.tools import chunker
from webapps.schedule.models import ResourceHours, BusinessHours

CHUNK_SIZE = 100


class Command(BaseCommand):
    help = 'Update schedule_data column for BusinessHours and ResourceHours models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Print the number of records to be updated without making changes',
        )
        parser.add_argument(
            '--chunk-size',
            type=int,
            default=CHUNK_SIZE,
            help='Number of records to process in each chunk',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        chunk_size = options['chunk_size']

        for model in [BusinessHours, ResourceHours]:
            qs = model.objects.filter(schedule_data__isnull=True)
            total_count = qs.count()

            self.stdout.write(
                self.style.SUCCESS(
                    f"Begin update of schedule_data column in table "
                    f"{model._meta.db_table}: {total_count} records"
                )
            )

            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Dry run: {total_count} records would be updated in {model._meta.db_table}"
                    )
                )
                continue

            for schedules in tqdm(
                chunker(qs.iterator(), chunk_size),
                total=total_count // chunk_size,
            ):
                for schedule in schedules:
                    schedule.schedule_data = {}  # Placeholder value to fire PG trigger
                model.objects.bulk_update(schedules, ['schedule_data', 'updated'])

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully updated {total_count} records in {model._meta.db_table}"
                )
            )
