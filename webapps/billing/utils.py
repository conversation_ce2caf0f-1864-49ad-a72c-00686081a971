import json
import typing as t
from datetime import date, datetime, timedelta
from decimal import Decimal

from dateutil import relativedelta
from django.conf import settings
from moneyed import Money

from country_config import Country
from lib.tools import format_money, tznow
from webapps.billing.models import (
    BillingSubscription,
    SubscriptionPurchaseRequest,
)


class DecimalJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Decimal):
            return str(o)
        return o.__dict__


def dates_range_with_delta(
    start: t.Union[date, datetime],
    end: t.Union[date, datetime],
    delta: relativedelta,
) -> t.Iterable[t.Union[date, datetime]]:
    """
    dates_range_with_delta(
        date(2021, 1, 1),
        date(2021, 3, 1),
        relativedelta(months=+1),
    )
    [date(2021, 1, 1), date(2021, 2, 1), date(2021, 3, 1)]
    """

    current = start
    while current <= end:
        yield current
        current += delta


def relativedelta_by_step(
    start: t.Union[date, datetime],
    delta: relativedelta,
    steps: int,
) -> t.Union[date, datetime]:
    """
    relativedelta_by_step(
        date(2021, 8, 31),
        relativedelta(months=+1),
        steps=2,
    )
    date(2021, 10, 30)
    """

    for _ in range(steps):
        start += delta
    return start


def get_current_sms_usage(business_id):
    """
    Get sms usage for current billing subscription.
    Returns None if business has no latest billing cycle.
    """

    subscription = BillingSubscription.get_current_subscription(business_id)
    if subscription and (billing_cycle := subscription.latest_cycle):
        return {
            'current_sms_usage': billing_cycle.current_sms_usage,
            'current_sms_usage_paid': billing_cycle.current_sms_usage_paid,
        }


def billing_sms_summary_by_billing_cycles(
    subscription: BillingSubscription, billing_cycles_amount=2
):
    """
    Calculates sms summary by billing cycles for Billing 3.0 subscription.
    Entries are in line with NotificationSMSStatistics.sms_summary_by_billing_cycles
    """
    from webapps.braintree_app.payment_processor import BraintreePaymentProcessor

    if not (billing_cycles := subscription.billing_cycles.order_by('-date_end')):
        return []

    statistics = []
    for billing_cycle in billing_cycles[:billing_cycles_amount]:
        statistics.append(
            {
                'period': billing_cycle.date_start.strftime('%Y-%m-%d'),
                'period_end': billing_cycle.date_end.strftime('%Y-%m-%d'),
                'limit_free': billing_cycle.sms_allowance,
                'limit_paid': settings.BILLING_SMS_HARD_LIMIT,
                'count_free': billing_cycle.current_sms_usage
                - billing_cycle.current_sms_usage_paid,
                'count_paid': billing_cycle.current_sms_usage_paid,
                'parts_count': billing_cycle.current_sms_usage,
                'current_limit_free': billing_cycle.current_limit_free,
                'current_sms_price': float(billing_cycle.current_sms_price),
                'payable_cost': float(
                    BraintreePaymentProcessor.get_transaction_amount(
                        billing_cycle.current_total_sms_price
                    )
                ),
                'payable_cost_formatted': format_money(
                    Money(billing_cycle.current_total_sms_price, subscription.currency)
                ),
            }
        )
    return statistics


def payment_processor_response_to_dict(response: t.Any) -> dict | list | str | None:
    """
    Tries to convert an object to a dict to save it as json field
    """

    try:
        data = json.loads(json.dumps(response, cls=DecimalJSONEncoder))
    except Exception:  # pylint: disable=broad-except
        data = {}
    return data


def can_restore_manually(purchase_request: SubscriptionPurchaseRequest) -> bool:
    if (
        purchase_request.used_at
        or purchase_request.canceled_at
        or purchase_request.created > tznow() - timedelta(minutes=15)
        or BillingSubscription.objects.active_and_pending_subscriptions()
        .filter(business_id=purchase_request.business_id)
        .exists()
    ):
        return False
    return True


def get_cancellation_date_by_country(subscription: BillingSubscription) -> datetime:
    if settings.API_COUNTRY == Country.PL:
        return subscription.current_cycle_end + relativedelta.relativedelta(months=1)
    return subscription.current_cycle_end
