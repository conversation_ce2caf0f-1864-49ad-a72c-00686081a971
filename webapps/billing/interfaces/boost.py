from lib.cache import lru_booksy_cache
from webapps.boost.apis.next_payment import BusinessBoostNextPaymentAPI
from webapps.boost.apis.overdue_transactions import (
    BillingBulkPaymentResponse,
    BoostOverdueInfo,
    BoostOverdueTransactionAPI,
    BoostTransaction,
)
from webapps.boost.apis.status import BusinessBoostStatusAPI
from webapps.boost.services.next_payment import BoostNextPaymentInfo
from webapps.boost.services.status import BusinessBoostStatus

BOOST_TOTAL_OVERDUE_INFO_CACHE_EXPIRY = 300  # secs


@lru_booksy_cache(
    timeout=BOOST_TOTAL_OVERDUE_INFO_CACHE_EXPIRY,
    skip_in_pytest=True,
)
def total_overdue_info(*, business_id: int) -> BoostOverdueInfo:
    return BoostOverdueTransactionAPI.get_overdue_info(business_id=business_id)


def business_boost_info(*, business_id: int) -> BusinessBoostStatus:
    return BusinessBoostStatusAPI.get_status(business_id=business_id)


def business_next_payment_info(*, business_id: int) -> BoostNextPaymentInfo:
    return BusinessBoostNextPaymentAPI.get_total(business_id=business_id)


def overdue_transactions(
    *,
    business_id: int,
) -> list[BoostTransaction]:
    return BoostOverdueTransactionAPI.get_transactions(business_id=business_id) or []


def set_as_paid(
    *,
    business_id: int,
    transactions: list[BoostTransaction],
    payment_response: BillingBulkPaymentResponse,
) -> None:
    return BoostOverdueTransactionAPI.set_as_paid(
        business_id=business_id,
        transactions=transactions,
        payment_response=payment_response,
    )
