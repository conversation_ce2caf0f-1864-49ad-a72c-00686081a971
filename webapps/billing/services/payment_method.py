from dataclasses import dataclass, field

from dataclasses_json import DataClassJsonMixin
from django.conf import settings
from django.db import transaction

from lib.enums import PaymentMethodType
from webapps.billing.enums import (
    PaymentProcessorType,
    get_card_type,
    get_payment_type,
    CreditNetworkType,
)
from webapps.billing.error_handling.errors import BillingErrorGroupEnum
from webapps.billing.interfaces import stripe as stripe_interface
from webapps.billing.models import (
    BillingCreditCardInfo,
    BillingCreditCardInfoObject,
    BillingCreditCardVerification,
    BillingPaymentMethod,
    ExternalCustomer,
)
from webapps.billing.services.external_customer import get_or_create_customer_with_stripe
from webapps.business.models import Business
from webapps.stripe_app.apis.payment_method import PaymentMethodApi
from webapps.stripe_app.errors import ErrorObject
from webapps.stripe_app.services.payment_method import (
    PaymentMethodResult as StripePaymentMethodResult,
)


@dataclass(frozen=True)
class InitializePaymentMethodResult(DataClassJsonMixin):
    is_success: bool
    client_secret: str | None = field(default=None)
    error: ErrorObject | None = field(default=None)


@dataclass(frozen=True)
class PaymentMethodResult(DataClassJsonMixin):
    is_success: bool
    payment_method_id: int | None = field(default=None)
    error: ErrorObject | None = field(default=None)


@dataclass(frozen=True)
class PaymentMethodsResult(DataClassJsonMixin):
    is_success: bool
    payment_methods_ids: list[int] = field(default_factory=list)
    error: ErrorObject | None = field(default=None)


class PaymentMethodService:
    @staticmethod
    def get_default_stripe_method(*, business_id: int) -> BillingPaymentMethod | None:
        qs = BillingPaymentMethod.objects.filter(
            business_id=business_id,
            payment_processor=PaymentProcessorType.STRIPE,
        )
        # Returns the default value or any existing value
        # (in case someone does not have a default one)
        return qs.filter(default=True).first() or qs.first()

    @staticmethod
    def create(  # pylint: disable=too-many-arguments
        *,
        business_id: int,
        token: str,
        payment_processor: PaymentProcessorType,
        default: bool = True,
        payment_method_type: str,
        card_info: BillingCreditCardInfoObject | None = None,
    ) -> BillingPaymentMethod:
        with transaction.atomic():
            payment_method, created = BillingPaymentMethod.all_objects.get_or_create(
                business_id=business_id,
                token=token,
                payment_processor=payment_processor,
                defaults={
                    'payment_method_type': payment_method_type,
                    'default': default,
                },
            )

            if created:
                if default:
                    BillingPaymentMethod.objects.filter(
                        business_id=business_id,
                        default=True,
                        payment_processor=payment_method.payment_processor,
                    ).exclude(id=payment_method.id).update(default=False)

                if payment_method.is_card():
                    credit_card = BillingCreditCardInfo.objects.create(
                        card_type=card_info.card_type,
                        first_6_digits=card_info.first_6_digits,
                        last_4_digits=card_info.last_4_digits,
                        country_of_issuance=card_info.country_of_issuance,
                        cardholder_name=card_info.cardholder_name,
                        expiration_date=card_info.expiration_date,
                        address_line_1=card_info.address_line_1,
                        address_line_2=card_info.address_line_2,
                        city=card_info.city,
                        country=card_info.country,
                        zipcode=card_info.zipcode,
                        preferred_network=card_info.preferred_network,
                        available_networks=card_info.available_networks,
                    )

                    payment_method.credit_card = credit_card
                    payment_method.save(update_fields=['credit_card'])
                    BillingCreditCardVerification.objects.create(
                        business_id=business_id,
                        credit_card=credit_card,
                    )
        return payment_method

    @classmethod
    def set_as_default(cls, instance: BillingPaymentMethod) -> BillingPaymentMethod:
        with transaction.atomic():
            instance.default = True
            instance.save()
            BillingPaymentMethod.objects.filter(
                business_id=instance.business_id, payment_processor=instance.payment_processor
            ).exclude(id=instance.id).update(default=False)
        return instance


class StripePaymentMethodService:
    @staticmethod
    def initialize_setup(*, business: Business) -> InitializePaymentMethodResult:
        customer = get_or_create_customer_with_stripe(business_id=business.id)
        if not customer.is_success:
            return InitializePaymentMethodResult(is_success=False, error=customer.error)
        stripe_result = stripe_interface.initialize_payment_method(business_id=business.id)
        return InitializePaymentMethodResult(
            is_success=stripe_result.is_success,
            client_secret=(
                stripe_result.setup_intent.client_secret if stripe_result.is_success else None
            ),
            error=stripe_result.error,
        )

    @staticmethod
    def finalize_setup(
        *,
        business: Business,
        stripe_id: str,
        default: bool = True,
    ) -> PaymentMethodResult:
        stripe_result = stripe_interface.finalize_payment_method(
            business_id=business.id,
            stripe_id=stripe_id,
        )

        if not stripe_result.is_success:
            return PaymentMethodResult(
                is_success=False,
                error=stripe_result.error,
            )

        stripe_payment_method = stripe_result.payment_method

        payment_method = PaymentMethodService.create(
            business_id=business.id,
            token=stripe_payment_method.stripe_id,
            payment_processor=PaymentProcessorType.STRIPE,
            payment_method_type=get_payment_type(stripe_payment_method.type),
            default=default,
            card_info=BillingCreditCardInfoObject(
                card_type=get_card_type(stripe_payment_method.card.brand),
                last_4_digits=stripe_payment_method.card.last4,
                preferred_network=stripe_payment_method.card.preferred_network,
                available_networks=stripe_payment_method.card.available_networks,
                country_of_issuance=stripe_payment_method.card.country,
                cardholder_name=stripe_payment_method.billing_details.name,
                expiration_date=stripe_payment_method.card.exp_iso_date,
                address_line_1=stripe_payment_method.billing_details.address.line1,
                address_line_2=stripe_payment_method.billing_details.address.line2,
                city=stripe_payment_method.billing_details.address.city,
                country=stripe_payment_method.billing_details.address.country,
                zipcode=stripe_payment_method.billing_details.address.postal_code,
            ),
        )

        return PaymentMethodResult(
            is_success=True,
            payment_method_id=payment_method.id,
        )

    @staticmethod
    def from_webhook(
        *,
        raw_payment_method_result: dict,
    ) -> PaymentMethodResult:
        stripe_result = StripePaymentMethodResult.from_dict(raw_payment_method_result)

        if not stripe_result.is_success:
            return PaymentMethodResult(
                is_success=False,
                error=stripe_result.error,
            )

        stripe_payment_method = stripe_result.payment_method

        external_customer = ExternalCustomer.objects.get(
            business_id=ExternalCustomer.from_booksy_id(stripe_payment_method.customer.booksy_id),
            payment_processor=PaymentProcessorType.STRIPE,
        )

        payment_method = PaymentMethodService.create(
            business_id=external_customer.business_id,
            token=stripe_payment_method.stripe_id,
            payment_processor=PaymentProcessorType.STRIPE,
            payment_method_type=get_payment_type(stripe_payment_method.type),
            default=True,
            card_info=BillingCreditCardInfoObject(
                card_type=get_card_type(stripe_payment_method.card.brand),
                last_4_digits=stripe_payment_method.card.last4,
                preferred_network=stripe_payment_method.card.preferred_network,
                available_networks=stripe_payment_method.card.available_networks,
                country_of_issuance=stripe_payment_method.card.country,
                cardholder_name=stripe_payment_method.billing_details.name,
                expiration_date=stripe_payment_method.card.exp_iso_date,
                address_line_1=stripe_payment_method.billing_details.address.line1,
                address_line_2=stripe_payment_method.billing_details.address.line2,
                city=stripe_payment_method.billing_details.address.city,
                country=stripe_payment_method.billing_details.address.country,
                zipcode=stripe_payment_method.billing_details.address.postal_code,
            ),
        )

        StripePaymentMethodService.set_default_if_not_exists(
            business_id=external_customer.business_id, payment_processor=PaymentProcessorType.STRIPE
        )

        return PaymentMethodResult(
            is_success=True,
            payment_method_id=payment_method.id,
        )

    @staticmethod
    def migrate_payment_methods(*, business: Business) -> PaymentMethodsResult:
        result = get_or_create_customer_with_stripe(business_id=business.id)
        if not result.is_success:
            return PaymentMethodsResult(is_success=False, error=result.error)

        external_customer_id = ExternalCustomer.booksy_id(business.id)
        billing_payment_methods = BillingPaymentMethod.objects.filter(
            business_id=business.id, payment_processor=PaymentProcessorType.STRIPE
        )
        billing_payment_methods_ids = billing_payment_methods.values_list('token', flat=True)
        stripe_payment_methods_result = PaymentMethodApi.get_list_for_customer(
            customer_booksy_id=external_customer_id,
            stripe_config=settings.BILLING_STRIPE_APP_CONFIG,
        )

        if stripe_payment_methods_result.is_success:
            payment_methods = []
            for stripe_payment_method in stripe_payment_methods_result.payment_methods:
                if stripe_payment_method.stripe_id not in billing_payment_methods_ids:
                    result = PaymentMethodService.create(
                        business_id=business.id,
                        payment_processor=PaymentProcessorType.STRIPE,
                        default=False,
                        payment_method_type=PaymentMethodType.CREDIT_CARD,
                        token=stripe_payment_method.stripe_id,
                        card_info=BillingCreditCardInfoObject(
                            card_type=get_card_type(stripe_payment_method.card.brand),
                            last_4_digits=stripe_payment_method.card.last4,
                            preferred_network=stripe_payment_method.card.preferred_network,
                            available_networks=stripe_payment_method.card.available_networks,
                            country_of_issuance=stripe_payment_method.card.country,
                            cardholder_name=stripe_payment_method.billing_details.name,
                            expiration_date=stripe_payment_method.card.exp_iso_date,
                            address_line_1=stripe_payment_method.billing_details.address.line1,
                            address_line_2=stripe_payment_method.billing_details.address.line2,
                            city=stripe_payment_method.billing_details.address.city,
                            country=stripe_payment_method.billing_details.address.country,
                            zipcode=stripe_payment_method.billing_details.address.postal_code,
                        ),
                    )
                    payment_methods.append(result.id)

            StripePaymentMethodService.set_default_if_not_exists(
                business_id=business.id, payment_processor=PaymentProcessorType.STRIPE
            )

            return PaymentMethodsResult(is_success=True, payment_methods_ids=payment_methods)
        return PaymentMethodsResult(is_success=False, error=stripe_payment_methods_result.error)

    @staticmethod
    def parse_error_response(payment_result: PaymentMethodResult) -> dict:
        fallback_error = BillingErrorGroupEnum.CONTACT_WITH_US

        if error := payment_result.error:
            error_code = error.decline_code or error.code
            error_group = BillingErrorGroupEnum.get_group(error_code)
            return {
                'message': str(error_group.label),
                'error_code': error_group.value,
                'internal_errors': error.to_dict(),
            }

        return {
            'internal_errors': payment_result.to_dict(),
            'internal_message': None,
            'message': str(fallback_error.label),
            'error_code': fallback_error.value,
        }

    @staticmethod
    def set_default_if_not_exists(business_id: int, payment_processor: PaymentProcessorType):
        if not (
            result := BillingPaymentMethod.objects.filter(
                business_id=business_id, payment_processor=payment_processor
            )
        ):
            return

        payment_method = result.filter(default=True)
        if not payment_method.exists():
            payment_method = result.latest('created')
            payment_method.default = True
            payment_method.save()

    @staticmethod
    def payment_method_data_update_from_webhook(payment_method_result: dict) -> None:
        payment_method_result = StripePaymentMethodResult.from_dict(payment_method_result)
        stripe_payment_method = payment_method_result.payment_method
        if (
            payment_method := BillingPaymentMethod.objects.filter(
                token=stripe_payment_method.stripe_id, payment_processor=PaymentProcessorType.STRIPE
            )
            .select_related('credit_card')
            .first()
        ) and (credit_card := payment_method.credit_card):
            credit_card.card_type = get_card_type(stripe_payment_method.card.brand)
            credit_card.last_4_digits = stripe_payment_method.card.last4
            credit_card.country_of_issuance = stripe_payment_method.card.country
            credit_card.cardholder_name = stripe_payment_method.billing_details.name
            credit_card.expiration_date = stripe_payment_method.card.exp_iso_date
            credit_card.preferred_network = stripe_payment_method.card.preferred_network
            credit_card.available_networks = stripe_payment_method.card.available_networks
            credit_card.address_line_1 = stripe_payment_method.billing_details.address.line1
            credit_card.address_line_2 = stripe_payment_method.billing_details.address.line2
            credit_card.city = stripe_payment_method.billing_details.address.city
            credit_card.country = stripe_payment_method.billing_details.address.country
            credit_card.zipcode = stripe_payment_method.billing_details.address.postal_code
            credit_card.save(_history={'metadata': {'endpoint': 'data_update_from_webhook'}})


def modify_stripe_payment_method(
    stripe_id: str, preferred_network: CreditNetworkType
) -> PaymentMethodResult:
    return PaymentMethodApi.modify(
        stripe_id=stripe_id,
        stripe_config=settings.BILLING_STRIPE_APP_CONFIG,
        card_networks_preferred=preferred_network,
    )
