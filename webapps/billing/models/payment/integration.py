import typing as t

from django.db import models

from lib.models import ArchiveModel
from lib.tools import extract_internal_id, id_to_external_api
from webapps.billing.enums import BillingErrorEventType, PaymentProcessorType


__all__ = [
    'ExternalCustomer',
    'PaymentProcessorError',
]


class ExternalCustomer(ArchiveModel):
    class Meta:
        unique_together = ['payment_processor', 'business']

    payment_processor = models.CharField(
        max_length=1,
        choices=PaymentProcessorType.choices(),
        default=PaymentProcessorType.STRIPE.value,
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
    )

    @classmethod
    def booksy_id(cls, business_id: int) -> str:
        return f'business_{id_to_external_api(business_id)}'

    @classmethod
    def from_booksy_id(cls, booksy_id) -> int:
        return int(extract_internal_id(booksy_id.replace('business_', '')))


class PaymentProcessorError(ArchiveModel):
    """
    Stores (weird) errors that do not match a transaction or other object to help debugging.
    """

    class Meta:
        verbose_name = 'Payment processor error'
        verbose_name_plural = 'Payment processor errors'

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.DO_NOTHING,
    )
    response = models.JSONField(null=True, blank=True)
    event_type = models.CharField(
        max_length=1,
        choices=BillingErrorEventType.choices(),
        blank=True,
        null=True,
    )

    @staticmethod
    def create_from_any_response(
        *,
        business_id: int,
        response: t.Any,
        event_type: BillingErrorEventType | None = None,
    ):
        from webapps.billing.utils import payment_processor_response_to_dict

        return PaymentProcessorError.objects.create(
            business_id=business_id,
            response=payment_processor_response_to_dict(response),
            event_type=event_type,
        )
