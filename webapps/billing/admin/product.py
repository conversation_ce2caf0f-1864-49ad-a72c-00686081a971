from django.http import Http404
from django.shortcuts import redirect
from django.templatetags.static import static
from django.urls.base import reverse

from lib.admin_helpers import BaseModelAdmin
from webapps.billing.admin.base import BillingAdminHistoryMixin
from webapps.billing.forms import ProductForm, SaasProductForm, StafferProductForm
from webapps.billing.models import (
    PRODUCT_TYPE_TO_MODEL_MAP,
    BillingBusyProduct,
    BillingPostpaidSMSProduct,
    BillingProduct,
    BillingSaaSProduct,
    BillingStafferBusyProduct,
    BillingStafferSaaSProduct,
)
from webapps.billing.permissions import (
    BillingAdvancedAdminPermissionsNoDelMixin,
    BillingAdvancedPermissionsNoDelMixin,
    BillingDevPermissionsMixin,
)


class ProductAdminMixin(BillingAdvancedPermissionsNoDelMixin, BillingAdminHistoryMixin):
    list_display = (
        'id',
        'name',
        'active',
        'price_formatted',
    )
    search_fields = (
        '=id',
        '=name',
    )
    list_filter = ('active',)
    _all_products_fields = (
        'name',
        'unit_price',
        'active',
        'currency',
    )
    _technical_fields = (
        'created',
        'updated',
        'deleted',
    )
    # pylint: disable=protected-access
    fields = _all_products_fields + _technical_fields + BillingAdminHistoryMixin._history_fields

    readonly_fields = (
        'created',
        'updated',
        'deleted',
        *BillingAdminHistoryMixin._history_fields,
    )
    form = ProductForm

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.filter(product_type=self.model.valid_product_type)
        return queryset

    @staticmethod
    def price_formatted(obj):
        return obj.get_price_display()

    price_formatted.short_description = 'Price'


class SaaSProductAdminMixin(ProductAdminMixin):
    fields = ProductAdminMixin._all_products_fields + (
        'sms_amount',
        'staff_add_on',
        'sms_add_on',
        # pylint: disable=protected-access
        *ProductAdminMixin._technical_fields,
        *BillingAdminHistoryMixin._history_fields,
    )


class StafferProductAdminMixin(ProductAdminMixin):
    fields = (
        *ProductAdminMixin._all_products_fields,
        'sms_amount',
        'free_staff_qty',
        'max_qty',
        # pylint: disable=protected-access
        *ProductAdminMixin._technical_fields,
        *BillingAdminHistoryMixin._history_fields,
    )


class AllProductsAdmin(BillingAdvancedPermissionsNoDelMixin, BaseModelAdmin):
    """Show all products on one changelist."""

    model = BillingProduct
    list_display = (
        'id',
        'product_type',
        'name',
        'active',
        'price_formatted',
        'updated',
    )
    search_fields = (
        '=id',
        '=name',
    )
    list_filter = ('product_type',)
    readonly_fields = ('created', 'updated', 'deleted')

    def has_add_permission(self, request):
        return False

    @staticmethod
    def price_formatted(obj):
        return obj.get_price_display()

    price_formatted.short_description = 'Price'

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Redirect to relevant Product changeview based on mapping."""
        try:
            product = BillingProduct.objects.only('product_type').get(
                id=object_id,
            )

        except BillingProduct.DoesNotExist as err:
            raise Http404 from err
        target_class = PRODUCT_TYPE_TO_MODEL_MAP[product.product_type]
        target_url = reverse(
            f'admin:billing_{target_class.__name__.lower()}_change', args=(object_id,)
        )
        return redirect(target_url)


class SaaSProductAdmin(
    SaaSProductAdminMixin,
    BillingAdvancedAdminPermissionsNoDelMixin,
    BaseModelAdmin,
):
    model = BillingSaaSProduct
    form = SaasProductForm


class StafferSaaSProductAdmin(
    StafferProductAdminMixin,
    BillingAdvancedAdminPermissionsNoDelMixin,
    BaseModelAdmin,
):
    model = BillingStafferSaaSProduct
    form = StafferProductForm

    class Media:
        js = (static('admin/js/billing.js'),)


class BusyProductAdmin(SaaSProductAdminMixin, BillingDevPermissionsMixin, BaseModelAdmin):
    model = BillingBusyProduct


class StafferBusyProductAdmin(StafferProductAdminMixin, BillingDevPermissionsMixin, BaseModelAdmin):
    model = BillingStafferBusyProduct
    form = StafferProductForm
    # No free staffers included


class PostpaidSMSProductAdmin(
    ProductAdminMixin,
    BillingAdvancedAdminPermissionsNoDelMixin,
    BaseModelAdmin,
):
    model = BillingPostpaidSMSProduct
