import uuid

from webapps.billing.tasks import batch_boost_overdue_charge_task


class BillingBoostAPI:
    @staticmethod
    def async_charge_overdues(
        *,
        business_id: int,
        selected_boost_ids: list | None = None,
        operator_id: int | None = None,
    ) -> uuid.uuid4:
        task = batch_boost_overdue_charge_task.delay(
            business_id=business_id,
            selected_boost_ids=selected_boost_ids,
            operator_id=operator_id,
        )
        return task.id
