import elasticsearch_dsl as dsl
from rest_framework import serializers

from webapps.business_related.models import Amenities

PROPERTIES = dict(
    parking=dsl.<PERSON><PERSON><PERSON>(index=True),
    credit_cards=dsl.<PERSON><PERSON><PERSON>(index=True),
    wheelchair_access=dsl.<PERSON><PERSON><PERSON>(index=True),
    kids=dsl.<PERSON><PERSON><PERSON>(index=True),
    animals=dsl.<PERSON><PERSON><PERSON>(index=True),
    wifi=dsl.<PERSON><PERSON><PERSON>(index=True),
    loyalty=dsl.<PERSON><PERSON><PERSON>(index=True),
)


class AmenitiesSerializer(serializers.ModelSerializer):
    # pylint disable=duplicate-code

    class Meta:
        model = Amenities
        fields = (
            'parking',
            'credit_cards',
            'wheelchair_access',
            'kids',
            'animals',
            'wifi',
            'loyalty',
        )
