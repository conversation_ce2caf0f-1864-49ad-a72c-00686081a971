import unittest

import pytest

from webapps.business.baker_recipes import service_recipe, treatment_recipe
from webapps.business.business_categories.treatment_assignment import TreatmentAssignmentUTT1


@pytest.mark.django_db
class TestUpdateServiceTreatment(unittest.TestCase):
    def test_treatment_assigned_to_service(self):
        service = service_recipe.make(
            name='Test name',
            treatment=treatment_recipe.make(),
            is_treatment_selected_by_user=True,
        )
        new_treatment = treatment_recipe.make(name='Name')
        TreatmentAssignmentUTT1.assign_treatments_for_services([service.id], only_null=False)

        service.refresh_from_db()
        self.assertEqual(service.treatment, new_treatment)
        self.assertFalse(service.is_treatment_selected_by_user)

    def test_treatment_not_assigned_to_service(self):
        current_treatment = treatment_recipe.make()
        service = service_recipe.make(
            name='Test name',
            treatment=current_treatment,
            is_treatment_selected_by_user=True,
        )
        treatment_recipe.make(name='No match')
        TreatmentAssignmentUTT1.assign_treatments_for_services([service.id], only_null=False)

        service.refresh_from_db()
        self.assertEqual(service.treatment, current_treatment)
        self.assertTrue(service.is_treatment_selected_by_user)

    def test_treatment_not_assigned_to_service_clear_no_match(self):
        current_treatment = treatment_recipe.make()
        service = service_recipe.make(
            name='Test name',
            treatment=current_treatment,
            is_treatment_selected_by_user=True,
        )
        treatment_recipe.make(name='No match')
        TreatmentAssignmentUTT1.assign_treatments_for_services(
            [service.id],
            only_null=False,
            clear_no_match=True,
        )
        service.refresh_from_db()
        self.assertIsNone(service.treatment)
        self.assertFalse(service.is_treatment_selected_by_user)
