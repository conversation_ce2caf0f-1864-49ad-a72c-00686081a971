# https://github.com/PyCQA/pylint/issues/4987
# pylint: disable=no-name-in-module

import json

import grpc
from django.db.models import F
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from lib.geocoding.here_maps import _ADDRESS_FIELD_TO_REGION_TYPE_MAPPING
from webapps.business.models.models import Business
from webapps.business.protobuf.core import ecommerce_business_details_pb2_grpc
from webapps.business.protobuf.core.ecommerce_business_details_pb2 import (
    GetEcommerceBusinessDetailsRequest,
    EcommerceBusinessDetailsResponse,
)
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.warehouse.models import Warehouse


def find_region_name(zip_region: Region) -> str | None:
    for state_equivalent_field_type in _ADDRESS_FIELD_TO_REGION_TYPE_MAPPING['state']:
        if region := zip_region.get_parent_by_type(state_equivalent_field_type):
            return region.name
    return None


class GetEcommerceBusinessDetailsRequestSerializer(ProtoSerializer):
    class Meta:
        proto_class = GetEcommerceBusinessDetailsRequest

    id = serializers.IntegerField()


class BusinessAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = ['address', 'address2', 'city', 'zipcode', 'country_code', 'region']

    region = serializers.SerializerMethodField()

    def get_region(self, instance) -> str | None:
        return find_region_name(instance.region) if instance.region else None


class WarehouseAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Warehouse
        fields = ['address', 'city', 'zipcode', 'country_code', 'region']

    zipcode = serializers.CharField(source='zip_code')
    country_code = serializers.CharField(source='country')
    region = serializers.SerializerMethodField()

    def get_region(self, instance) -> str | None:
        try:
            zip_region = Region.objects.get(type=RegionType.ZIP, name=instance.zip_code)
        except Region.DoesNotExist:
            return None
        return find_region_name(zip_region)


class EcommerceBusinessDetailsResponseSerializer(ProtoSerializer):
    class Meta:
        proto_class = EcommerceBusinessDetailsResponse

    name = serializers.CharField()
    owner_first_name = serializers.CharField(allow_blank=True)
    owner_last_name = serializers.CharField(allow_blank=True)
    owner_email = serializers.EmailField()
    tax_id = serializers.CharField(required=False)
    phone = serializers.CharField(required=False)
    business_address = BusinessAddressSerializer()
    warehouse_addresses = WarehouseAddressSerializer(many=True, required=False)


class EcommerceBusinessDetailsServicer(
    ecommerce_business_details_pb2_grpc.EcommerceBusinessDetailsServicer
):
    def GetEcommerceBusinessDetails(self, request, context):
        context.set_compression(grpc.Compression.Gzip)
        serializer = GetEcommerceBusinessDetailsRequestSerializer(message=request)
        if not serializer.is_valid():
            context.abort(code=grpc.StatusCode.NOT_FOUND, details=json.dumps(serializer.errors))
        business_id = serializer.validated_data['id']

        try:
            business = (
                Business.objects.annotate(tax_id=F('buyer__tax_id'))
                .select_related('owner', 'region')
                .prefetch_related('warehouses')
                .get(pk=business_id)
            )
        except Business.DoesNotExist:
            context.abort(code=grpc.StatusCode.NOT_FOUND, details=json.dumps(serializer.errors))
        serialized_response = EcommerceBusinessDetailsResponseSerializer(
            {
                'name': business.name,
                'owner_first_name': business.owner.first_name,
                'owner_last_name': business.owner.last_name,
                'owner_email': business.owner.email,
                'tax_id': business.tax_id,
                'phone': business.phone,
                'business_address': business,
                'warehouse_addresses': business.warehouses.all(),
            }
        )
        return serialized_response.message
