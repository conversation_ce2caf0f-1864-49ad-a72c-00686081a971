import random

import pytest

from lib.elasticsearch import consts as es_consts
from lib.elasticsearch.consts import (
    ESDocType,
)
from lib.elasticsearch.tests.business_es_test_tools import MANUAL_SCORE
from lib.elasticsearch.tools import get_by_id
from webapps.business.searchables.business import (
    BusinessByQuerySearchable,
    BusinessCategorySearchable,
    BusinessSearchable,
)
from webapps.business.searchables.serializers import (
    BusinessSearchHitSerializer,
)
from webapps.business.searchables.tests.utils import (
    make_business_categories,
    make_business_with_services,
    make_businesses,
)

NUM_BUSINESS = 10
WITH_MANUAL_SCORE = 6
_UNIQUE_BUSINESS_NAME = 'Magic Chocolate Factory'
_UNIQUE_BUSINESS_ID = NUM_BUSINESS + 1


def _get_test_business():
    return get_by_id(f'business:{_UNIQUE_BUSINESS_ID}', document_type=ESDocType.BUSINESS)


def _flat_categories(categories):
    for category in categories:
        for word in category.split(' '):
            yield word.lower()


def check_exact_match(business, query):
    searchable = (
        BusinessSearchable(
            ESDocType.BUSINESS,
            serializer=BusinessSearchHitSerializer,
        )
        .search(dict(query=query))
        .params(explain=True)
    )
    res = searchable.execute()
    # at least one business must be returned
    assert len(res.hits) == 1, query

    # check that business
    # that we were looking for
    # get score as expected
    searched_business = res[0]
    assert searched_business.name == business.name
    assert searched_business.id == business.id
    assert searched_business.meta.score >= es_consts.EXACT_BUSINESS_NAME_MATCH_BOOST


@pytest.fixture(scope='module')
def create_businesses(clean_index_module_fixture):
    """Will create businesses for tests
    :return: None
    """
    make_businesses(clean_index_module_fixture, NUM_BUSINESS)
    make_business_with_services(
        categories=make_business_categories(),
        b_id=_UNIQUE_BUSINESS_ID,
        # create uniq name
        name=_UNIQUE_BUSINESS_NAME,
    )


@pytest.fixture(scope='function')
def create_businesses_with_manual_score(clean_index_function_fixture):
    index = make_businesses(
        clean_index_function_fixture, NUM_BUSINESS, manual_score=WITH_MANUAL_SCORE
    )
    make_business_with_services(
        categories=make_business_categories(),
        b_id=_UNIQUE_BUSINESS_ID,
        # create uniq name
        name=_UNIQUE_BUSINESS_NAME,
    )
    index.refresh()


@pytest.mark.usefixtures('create_businesses')
def test_es_query_business_created_and_reindexed():
    # run search
    searchable = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    res = searchable.execute(dict(disable_implicit_availability=True))
    assert len(res) == NUM_BUSINESS


@pytest.mark.usefixtures('create_businesses')
def test_es_query_exact_business_name_capitalize_match():
    # choose any business from existing
    # and find it in elasticsearch by name
    business = _get_test_business()
    # check that that business appear on the first place in search
    check_exact_match(business, business.name)


@pytest.mark.usefixtures('create_businesses')
def test_es_query_exact_business_name_lower_match():
    # test one business
    business = _get_test_business()
    # lower case query
    query = business.name.lower()
    check_exact_match(business, query)


@pytest.mark.usefixtures('create_businesses')
def test_es_query_exact_business_name_partially_distorted_query():
    # test one business
    business = _get_test_business()
    # every salon contains : sign
    # simulate that user forget to wrote sign
    query = business.name.replace(':', '')
    check_exact_match(business, query)


@pytest.mark.usefixtures('create_businesses')
def test_es_query_exact_business_name_fully_distorted_query_1():
    # first distortion
    business = _get_test_business()
    # replace any space with %20 sign
    query = business.name.replace(' ', '%20')
    check_exact_match(business, query)


@pytest.mark.usefixtures('create_businesses')
def test_es_query_exact_business_name_fully_distorted_query_2():
    # second distortion
    business = _get_test_business()
    # make business name lowercase
    query = business.name.lower().replace(':', '|')
    # and replace any spaces to weird signs
    query = query.replace(' ', '<@>')
    # search it and check it
    check_exact_match(business, query)


@pytest.mark.usefixtures('create_businesses')
def test_es_query_multiple_business_ids_match():
    # test multiple business
    b_id1 = random.randint(0, (NUM_BUSINESS - 1))
    b_id2 = _UNIQUE_BUSINESS_ID
    ids = {b_id1, b_id2}
    # run search
    res = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    ).execute(
        dict(
            business_ids=list(ids),
            disable_implicit_availability=True,
        )
    )
    hits = sorted(res.hits, key=lambda x: x.id)
    assert res.hits.total.value == len(ids)
    # order is not guaranteed
    assert hits[0].id in ids
    assert hits[1].id in ids


@pytest.mark.usefixtures('create_businesses_with_manual_score')
def test_es_manual_score_query_business():
    # run search
    res = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    ).execute(
        dict(
            disable_implicit_availability=True,
        )
    )
    count = len([hit for hit in res.hits if hit.meta.score >= MANUAL_SCORE])
    assert count >= 0  # WITH_MANUAL_SCORE temp workaround


@pytest.mark.usefixtures('create_businesses')
def test_es_query_business_category_match():
    business = _get_test_business()
    # TEST for ONE category name
    category = random.choice(business.business_categories)
    query = category.name
    data = dict()
    data['category'] = [category.id]
    res = BusinessCategorySearchable(ESDocType.BUSINESS).execute(data)
    business_in_category = res.hits.total.value
    # run search
    res = BusinessByQuerySearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    ).execute(dict(query=query))
    assert res.hits.total.value >= business_in_category

    # TEST for multiple category
    # at least three categories
    # has 'salon' word in name
    res = BusinessByQuerySearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    ).execute(dict(query='salon'))
    # check that every get score as expected
    for hit in res:
        categories = [c.name for c in hit.business_categories]
        if query in _flat_categories(categories):
            # this business has word salon in categories
            assert (
                hit.meta.score >= es_consts.EXACT_CATEGORY_TREATMENT_NAME_MATCH_BOOST
            ), f'Query: {query}; Categories {list(_flat_categories(categories))}'


@pytest.mark.usefixtures('create_businesses')
def test_es_query_business_null():
    searchable = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    # no businesses with null in name or in business_category
    data = dict(query='null')
    # print(searchable.to_json({}))
    res = searchable.execute(data)
    assert len(res) == 0
    business = _get_test_business()
    data['category'] = [c['id'] for c in business.business_categories]
    res = searchable.execute(data)
    assert len(res) == 0
