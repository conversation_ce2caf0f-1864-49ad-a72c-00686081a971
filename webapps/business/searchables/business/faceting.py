import elasticsearch_dsl as dsl

from lib.abc import abstractclassattribute
from lib.searchables.searchables import (
    MissingValueException,
    Searchable,
    SensiFilter,
    V,
)
from webapps.business.searchables.business.response import BusinessSearchableResponse
from webapps.business.searchables.business.search_engine import (
    BListingSearchable,
    BusinessByWebVisibility,
    ExcludeVenueBusinessSearchable,
    VisibleBusinessSearchable,
)
from webapps.business.searchables.business.sub_searchables import (
    BusinessByQuerySearchable,
    BusinessLocationSearchable,
    BusinessTreatmentsSearchable,
)


class FacetingBusinessSearchable(Searchable):
    """Do not create instance of this class
    this is base class to override: 'faceting' attribute
    """

    class Meta:
        response_class = BusinessSearchableResponse

    visible = VisibleBusinessSearchable()
    venue = ExcludeVenueBusinessSearchable()
    b_listing = BListingSearchable()
    category = dsl.query.Terms(business_categories__id=V('get_category'))
    location = BusinessLocationSearchable()
    treatments = BusinessTreatmentsSearchable()
    # possible remove query if no used
    query = BusinessByQuerySearchable()
    hidden_on_web = BusinessByWebVisibility()

    # must be child to be fired if Bucket in children condition
    # for details see Searchable._append_searchable
    faceting = abstractclassattribute()

    @staticmethod
    def get_category(data):
        # any falsy value
        if not data.get('category'):
            raise MissingValueException()
        return data['category']


class RegionFacetingSearchable(Searchable):
    regions = SensiFilter(
        aggs={
            'regions': dsl.aggs.Terms(
                field='regions.id',
                include=V('facet_regions'),
                size=V('get_facet_regions_size'),
            ),
        }
    )

    @staticmethod
    def get_facet_regions_size(data):
        facet_regions = data.get('facet_regions')
        if not facet_regions:
            raise MissingValueException()
        return len(facet_regions)


class CategoryFacetingSearchable(Searchable):
    categories = SensiFilter(
        aggs={
            'categories': dsl.aggs.Terms(
                field='business_categories.id',
                size=30,
            ),
        },
    )


class TreatmentFacetingSearchable(Searchable):
    class Meta:
        bool_param = 'filter'

    treatments = SensiFilter(
        aggs={
            'treatments': dsl.aggs.Terms(
                field='treatments.id',
                include=V('facet_treatments'),
                size=30,
            ),
        }
    )


class BusinessRegionFacetingSearchable(FacetingBusinessSearchable):
    class Meta:
        bool_param = 'should'

    faceting = RegionFacetingSearchable()


class BusinessCategoryFacetingSearchable(FacetingBusinessSearchable):
    class Meta:
        bool_param = 'filter'

    faceting = CategoryFacetingSearchable()


class BusinessTreatmentFacetingSearchable(FacetingBusinessSearchable):
    class Meta:
        bool_param = 'filter'

    faceting = TreatmentFacetingSearchable()
