import elasticsearch_dsl as dsl

from lib.searchables.common import IdsSearchable
from lib.searchables.searchables import Searchable, V
from webapps.business.searchables.business.sub_searchables import BusinessInnerHits
from webapps.business.searchables.business.search_engine import BusinessByWebVisibility


class BusinessOpenHoursCustomizations(Searchable):
    open_hours = dsl.query.Bool(
        should=[
            dsl.query.MatchAll(),
            dsl.query.HasChild(
                type='open_hours',
                query=dsl.query.Bool(
                    must=[
                        dsl.query.Term(customized=True),
                        dsl.query.Range(date=dict(gte='now/d')),
                    ]
                ),
                inner_hits=dict(
                    name='open_hours_customizations',
                    size=7,
                    sort=[{'date': 'asc'}],
                    _source=['date', 'time_range'],
                ),
            ),
        ]
    )


class BListingSourceIdSearchable(Searchable):
    source_id = dsl.query.Term(b_listing_source_id=V('b_listing_source_id'))


class BusinessByIdSearchable(Searchable):
    id = IdsSearchable()
    inner_hits = BusinessInnerHits()
    insert_open_hours_customizations = BusinessOpenHoursCustomizations()
    b_listing_source_id = BListingSourceIdSearchable()
    hidden_on_web = BusinessByWebVisibility()

    @staticmethod
    def compute_data(data):
        data['include_details'] = True
        return data
