from elasticsearch_dsl import AttrList

from webapps.business.enums import PriceType
from webapps.premium_services.public import PeakHoursAdapter


class VariantsAdapter:
    def __init__(self, business_id: int):
        self._peak_hour_adapter = PeakHoursAdapter()
        self.business_id = business_id
        self._service_variant_ids = None

    def has_active_peak_hours(self) -> bool:
        return bool(self.service_variant_ids)

    @property
    def service_variant_ids(self) -> set[int]:
        if self._service_variant_ids is None:
            active_peak_hours = self._peak_hour_adapter.get_active_services(
                business_id=self.business_id
            )
            self._service_variant_ids = {
                sv.service_variant_id for ph in active_peak_hours for sv in ph.service_variants
            }

        return self._service_variant_ids

    def apply_peak_hours_services(self, categories: AttrList) -> AttrList:
        for category in categories:
            for service in category.services:
                for variant in service.variants:
                    if variant.type == PriceType.FIXED and self.is_peak_hour(variant.id):
                        variant.type = PriceType.STARTS_AT

        return categories

    def is_peak_hour(self, service_variant_id: int) -> bool:
        return service_variant_id in self.service_variant_ids
