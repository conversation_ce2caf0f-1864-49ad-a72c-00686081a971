# Generated by Django 1.11.7 on 2018-02-23 13:52
from django.db import migrations, models
import django.db.models.deletion
import webapps.schedule.fields


MIGRATE_HOURS_SQL = '''
    INSERT INTO business_businessopeninghours 
        (created, updated, business_id, day_of_the_week, hours)
    SELECT
        now(),
        now(),
        business_id, 
        day_of_the_week,
        array_agg(ARRAY[hour_from, hour_till] ORDER BY hour_from)
    FROM business_businesshours
    GROUP BY day_of_the_week, business_id;
'''


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0122_merge_20180214_1445'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessOpeningHours',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.AutoField(
                        db_column='business_opening_hours_id', primary_key=True, serialize=False
                    ),
                ),
                (
                    'day_of_the_week',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'Monday'),
                            (2, 'Tuesday'),
                            (3, 'Wednesday'),
                            (4, 'Thursday'),
                            (5, 'Friday'),
                            (6, 'Saturday'),
                            (0, 'Sunday'),
                        ]
                    ),
                ),
                (
                    'hours',
                    webapps.schedule.fields.HoursField(
                        base_field=models.TimeField(), default=list, size=2
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='business_opening_hours',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Business Opening Hours',
                'verbose_name_plural': 'Business Opening Hours',
            },
        ),
        migrations.AlterUniqueTogether(
            name='businessopeninghours',
            unique_together=set([('business', 'day_of_the_week')]),
        ),
        migrations.RunSQL(MIGRATE_HOURS_SQL, reverse_sql=migrations.RunSQL.noop),
    ]
