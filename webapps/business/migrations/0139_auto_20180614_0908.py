# Generated by Django 1.11.11 on 2018-06-14 09:08
from django.db import migrations

from webapps.business.enums import CustomData


def business_changes(apps, schema_editor):
    def _is_test_business(b):
        return b.owner.email.endswith('@booksy.com') or b.owner.email.endswith('@booksy.net')

    Business = apps.get_model('business', 'Business')
    BusinessCategory = apps.get_model('business', 'BusinessCategory')
    try:
        other_category_id = (
            BusinessCategory.objects.filter(
                internal_name='Other',
            )
            .first()
            .id
        )
    except AttributeError:
        return
    for business in Business.objects.only('id', 'custom_data').iterator():
        primary_category_id = business.primary_category_id

        if primary_category_id != other_category_id:
            business.categories.remove(other_category_id)

        if _is_test_business(business):
            business.custom_data[CustomData.HIDDEN_IN_SEARCH] = True
            business.save(update_fields=['custom_data'])


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0138_auto_20180524_1346'),
    ]

    operations = []
