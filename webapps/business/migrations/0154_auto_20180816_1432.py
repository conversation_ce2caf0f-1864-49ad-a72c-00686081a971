# Generated by Django 1.11.11 on 2018-08-16 14:32
from django.db import migrations, models


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BusinessCategory = apps.get_model("business", "BusinessCategory")
    for category in BusinessCategory.objects.using(db_alias).all():
        category.plural_name = category.name
        category.save()


def no_op(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0153_merge_20180803_0938'),
    ]

    operations = [
        migrations.AddField(
            model_name='businesscategory',
            name='plural_name',
            field=models.CharField(default=' ', max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='servicevariantpayment',
            name='saving_type',
            field=models.CharField(
                choices=[('A', 'Amount'), ('P', 'Percentage')], default='P', max_length=1
            ),
        ),
        migrations.RunPython(forwards_func, reverse_code=no_op),
    ]
