# Generated by Django 2.2.13 on 2020-10-31 00:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0043_new_agreements'),
        ('business', '0299_merge_20201019_1025'),
    ]

    operations = [
        migrations.CreateModel(
            name='Booksy30SwitchBackFeedback',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'not_enough_features',
                    models.BooleanField(
                        blank=True,
                        default=False,
                        help_text="Current feature set doesn't meet my needs",
                    ),
                ),
                (
                    'technical_issues',
                    models.Bo<PERSON>anField(
                        blank=True, default=False, help_text="I'm expecting technical issues"
                    ),
                ),
                (
                    'too_unfamiliar',
                    models.BooleanField(
                        blank=True,
                        default=False,
                        help_text="It's too unfamiliar, I'd like to stick with what I know for now",
                    ),
                ),
                ('other_reason', models.BooleanField(blank=True, default=False, help_text='Other')),
                ('feedback_content', models.TextField(blank=True)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='business.Business'
                    ),
                ),
                (
                    'staff_user',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.User'),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Booksy30Feedback',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('is_enjoying', models.BooleanField(help_text='Are you enjoying Booksy Business?')),
                ('feedback_content', models.TextField()),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='business.Business'
                    ),
                ),
                (
                    'staff_user',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.User'),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
    ]
