# Generated by Django 4.1.10 on 2023-10-03 07:29

from django.db import migrations, models
import webapps.business.enums


class Migration(migrations.Migration):
    dependencies = [
        ("business", "0418_alter_businesspolicyagreement_partner_marketing_agreement"),
    ]

    operations = [
        migrations.AddField(
            model_name="cancellationreason",
            name="business_cancellation_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("BC", "Change in my business"),
                    ("TI", "Technical issues"),
                    ("F", "Lacking features"),
                    ("VC", "Not enough new clients"),
                    ("PC", "Too expensive"),
                    ("CU", "Clients are unhappy with Booksy"),
                    ("PS", "Lack of support and guidance"),
                    ("TC", "Need to cancel temporarily"),
                    ("D", "Difficult to use"),
                    ("O", "Other"),
                ],
                max_length=2,
                null=True,
                verbose_name="Business reason of cancel",
            ),
        ),
        migrations.AddField(
            model_name="cancellationreason",
            name="churn_type",
            field=models.Char<PERSON>ield(
                choices=[("A", "System cancellation"), ("B", "Made by Business")],
                default=webapps.business.enums.CancellationType["AUTOMATIC"],
                editable=False,
                max_length=1,
            ),
        ),
        migrations.AddField(
            model_name="cancellationreason",
            name="reason_additional_info",
            field=models.TextField(
                blank=True,
                help_text="Additional reason info provide by business",
                max_length=500,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="cancellationreason",
            name="cancellation_info",
            field=models.TextField(
                blank=True,
                help_text="Additional information typed by CS",
                null=True,
                verbose_name="Cancellation comment",
            ),
        ),
        migrations.AlterField(
            model_name="cancellationreason",
            name="cancellation_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("BC", "Business change"),
                    ("PO", "Did not speak to merchant"),
                    ("FA", "Fake"),
                    ("F", "Functionality"),
                    ("U", "Merchant declined to provide info"),
                    ("O", "Other"),
                    ("P", "Pricing"),
                    ("BS", "Product different than expected"),
                    ("S", "Tech issues / bugs"),
                    ("MP", "Very few CBs"),
                ],
                max_length=2,
                null=True,
                verbose_name="Reason of cancel",
            ),
        ),
    ]
