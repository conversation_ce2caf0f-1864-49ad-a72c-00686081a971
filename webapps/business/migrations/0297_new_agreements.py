# Generated by Django 2.2.13 on 2020-09-29 12:51

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0296_merge_20200921_1527'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='businesscustomerinfo',
            name='disclosure_obligation_agreement',
        ),
        migrations.RemoveField(
            model_name='businesscustomerinfo',
            name='marketing_agreement',
        ),
        migrations.RemoveField(
            model_name='businesscustomerinfo',
            name='privacy_policy_agreement',
        ),
        migrations.RemoveField(
            model_name='businesscustomerinfo',
            name='profiling_agreement',
        ),
        migrations.RemoveField(
            model_name='businesspolicyagreement',
            name='disclosure_obligation_agreement',
        ),
        migrations.RemoveField(
            model_name='businesspolicyagreement',
            name='profiling_agreement',
        ),
        migrations.RemoveField(
            model_name='businesspolicyagreement',
            name='web_communication_agreement',
        ),
        migrations.AddField(
            model_name='businesscustomerinfo',
            name='processing_consent',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='NIP',
            field=models.CharField(
                blank=True,
                max_length=10,
                validators=[
                    django.core.validators.RegexValidator(
                        message='NIP must consist of only numbers', regex='^[0-9]*$'
                    ),
                    django.core.validators.RegexValidator(
                        message='NIP length has to be exactly 10 characters', regex='^.{10}$'
                    ),
                ],
            ),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='business_address',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='business_name',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='inspector_email',
            field=models.EmailField(blank=True, max_length=75),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='inspector_first_name',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='inspector_last_name',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='businesspolicyagreement',
            name='new_gdpr_flow',
            field=models.BooleanField(default=True),
        ),
    ]
