# Generated by Django 1.11.17 on 2019-01-02 10:18
import datetime
from django.db import migrations, models
import django.db.models.deletion
import lib.interval.fields


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0193_merge_20190102_1017'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='blisting',
            options={
                'ordering': ['created'],
                'verbose_name': 'B Listing',
                'verbose_name_plural': 'B Listing',
            },
        ),
        migrations.AddField(
            model_name='business',
            name='b_listing',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='source',
                to='business.Business',
            ),
        ),
    ]
