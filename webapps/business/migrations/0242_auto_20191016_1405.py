# Generated by Django 2.0.13 on 2019-10-16 14:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0034_auto_20191004_1512'),
        ('business', '0241_auto_20191007_1102'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChurnButtonHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'edit_date',
                    models.DateTimeField(blank=True, null=True, verbose_name='Date (UTC)'),
                ),
                (
                    'action',
                    models.CharField(
                        choices=[('I', 'Initiate'), ('E', 'Edit'), ('C', 'Churn'), ('U', 'Undo')],
                        max_length=1,
                    ),
                ),
                (
                    'cancellation_date',
                    models.DateTimeField(blank=True, null=True, verbose_name='Churn date (UTC)'),
                ),
                (
                    'churn_reason',
                    models.CharField(
                        choices=[
                            ('P', 'Pricing'),
                            ('F', 'Functionality'),
                            ('S', 'Tech issues / bugs'),
                            ('BS', 'Product different than expected'),
                            ('BC', 'Business change'),
                            ('PO', 'Did not speak to merchant'),
                            ('U', 'Merchant declined to provide info'),
                            ('O', 'Other'),
                            ('MP', 'Very few CBs'),
                            ('FA', 'Fake'),
                        ],
                        max_length=2,
                        verbose_name='Reason of cancel',
                    ),
                ),
                (
                    'cancellation_info',
                    models.TextField(blank=True, null=True, verbose_name='Cancellation comment'),
                ),
                ('competitor_name', models.CharField(blank=True, max_length=150, null=True)),
                ('churn_done', models.NullBooleanField()),
                (
                    'business',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='churn_history',
                        to='business.Business',
                    ),
                ),
                (
                    'cancellation_reason',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='churn_history',
                        to='business.CancellationReason',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.User',
                        verbose_name='Operator',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.AlterModelOptions(
            name='businesscustomerinfo',
            options={
                'permissions': (('edit_business_customer_info', 'Can edit Client Cards'),),
                'verbose_name': 'Business Customer Card',
                'verbose_name_plural': 'Business Customer Cards',
            },
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='blacklisted',
            field=models.BooleanField(
                default=False,
                help_text='If checked, no more bookings by this customer will be allowed in this business',
                verbose_name='blacklisted',
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='bookmarked',
            field=models.BooleanField(default=False, help_text='Added to the favourite businesses'),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='business_secret_note',
            field=models.CharField(
                blank=True,
                help_text='Secret note made by business about the customer.',
                max_length=61440,
                verbose_name='business secret note',
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='visible_in_business',
            field=models.BooleanField(
                default=True,
                help_text="If checked, customer will appear in business' customers list",
                verbose_name='visible in business',
            ),
        ),
    ]
