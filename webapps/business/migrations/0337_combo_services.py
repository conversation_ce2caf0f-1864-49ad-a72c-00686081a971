# Generated by Django 3.1.12 on 2021-07-01 13:30

import dateutil.relativedelta
from django.db import migrations, models
import django.db.models.deletion
import lib.interval.fields
import webapps.business.enums


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0336_merge_20210622_0701'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='combo_type',
            field=models.CharField(
                choices=[('P', 'Booked in parallel'), ('S', 'Booked in sequence')],
                max_length=1,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='servicevariant',
            name='type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('X', 'Fixed price'),
                    ('V', 'Varies'),
                    ('D', "Don't show"),
                    ('F', 'Free'),
                    ('S', 'Price starts at'),
                    ('C', 'Combo Price'),
                ],
                max_length=1,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='travelingtoclients',
            name='price_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('X', 'Fixed price'),
                    ('V', 'Varies'),
                    ('D', "Don't show"),
                    ('F', 'Free'),
                    ('S', 'Price starts at'),
                    ('C', 'Combo Price'),
                ],
                default=webapps.business.enums.PriceType['FREE'],
                max_length=1,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name='ComboMembership',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'gap_time',
                    lib.interval.fields.IntervalField(default=dateutil.relativedelta.relativedelta),
                ),
                (
                    'child',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='combo_parents_through',
                        to='business.servicevariant',
                    ),
                ),
                (
                    'combo',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='combo_children_through',
                        to='business.servicevariant',
                    ),
                ),
            ],
            options={
                'unique_together': {('combo', 'child')},
            },
        ),
        migrations.AddField(
            model_name='servicevariant',
            name='combo_children',
            field=models.ManyToManyField(
                related_name='combo_parents',
                through='business.ComboMembership',
                to='business.ServiceVariant',
            ),
        ),
    ]
