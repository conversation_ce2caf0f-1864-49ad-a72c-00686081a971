from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Resource


class TestBusinessFizjoCheckAccess(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.user = cls.business.owner
        cls.url = reverse('fizjo_check_access')

    def test_appointment_id_is_required(self):
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

    def test_should_return_403_if_not_authenticated(self):
        self.client.credentials(
            HTTP_X_API_KEY=getattr(self, self.AUTH_BOOKING_SOURCE).api_key,
            HTTP_X_ACCESS_TOKEN='not-valid-access-token',
        )
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, status.HTTP_403_FORBIDDEN)

    def test_should_return_404_if_no_access_to_appointment(self):
        appointment = create_appointment(business=business_recipe.make())
        resp = self.client.get(self.url, {'subbooking_id': appointment.subbookings[0].id})
        self.assertEqual(resp.status_code, status.HTTP_404_NOT_FOUND)

    def test_should_return_404_if_appointment_does_not_exist(self):
        resp = self.client.get(self.url, {'subbooking_id': 9999})
        self.assertEqual(resp.status_code, status.HTTP_404_NOT_FOUND)

    def test_should_return_200_with_minimum_access_level(self):
        self._create_session_for_user(Resource.STAFF_ACCESS_LEVEL_STAFF)

        appointment = create_appointment(business=self.business)
        resp = self.client.get(self.url, {'subbooking_id': appointment.subbookings[0].id})
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

    def test_should_return_200_with_owner_level(self):
        appointment = create_appointment(business=self.business)
        resp = self.client.get(self.url, {'subbooking_id': appointment.subbookings[0].id})
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
