import unittest
from datetime import datetime

import pytest
from django.conf import settings
from freezegun import freeze_time
from mock import patch
from parameterized import parameterized

from lib.db import PAYMENTS_DB
from lib.feature_flag.killswitch import HintsAndWalkThroughEventsPublishingFlag
from lib.protobuf.fields import ProtobufDate<PERSON>imeField
from lib.tests.utils import override_feature_flag
from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import BookingMode
from webapps.business.baker_recipes import business_recipe, category_recipe, service_recipe
from webapps.business.messages.business import (
    BusinessCustomersImportedMessage,
    BusinessPrimaryDataChangedMessage,
)
from webapps.business.models import Business
from webapps.experiment_v3.exp import HintAndWalkthroughExperiment
from webapps.pubsub.config import GC_PROJECT_ID

FROZEN_DATE_TIME = datetime(2023, 9, 1)


def test_message_name_customers_imported():
    expected_topic_name = (
        f'projects/{GC_PROJECT_ID}/topics/{settings.DEPLOYMENT_LEVEL}.'
        f'{settings.API_COUNTRY}.core.business_customers_imported'
    )

    assert BusinessCustomersImportedMessage.topic_name == expected_topic_name


@pytest.mark.django_db
@freeze_time(FROZEN_DATE_TIME)
def test_message_payload_customers_imported():
    business = business_recipe.make()

    message = BusinessCustomersImportedMessage(
        business, context={'user_id': business.owner.id, 'imported_count': 5}
    )
    payload = message.message

    assert payload.event_timestamp == ProtobufDateTimeField.datetime_to_int(FROZEN_DATE_TIME)
    assert payload.business_id == business.id
    assert payload.user_id == business.owner.id
    assert payload.imported_count == 5


@pytest.mark.django_db
class TestMessageSendCustomersImported(BaseAsyncHTTPTest):
    """Based on service.business.tests.test_business_customer_import_handler_json.
    BusinessCustomerImportHandlerJSONTestCase"""

    def setUp(self):
        super().setUp()

        self.url = f'/business_api/me/businesses/{self.business.id}/customers/import/json/?'
        self.body = {
            'customers': [
                {
                    'last_name': 'Ryan',
                    'phone': '+***********',
                },
            ],
            'import': True,
            'invite': False,
        }

    @override_feature_flag({HintsAndWalkThroughEventsPublishingFlag: True})
    @patch('service.business.customer_info.BusinessCustomersImportedMessage.publish')
    def test_grpc(self, mocked):
        assert mocked.called is False

        resp = self.fetch(self.url, method='POST', body=self.body)

        assert resp.code == 200
        assert mocked.called is True


def test_message_name_business_data():
    expected_topic_name = (
        f'projects/{GC_PROJECT_ID}/topics/{settings.DEPLOYMENT_LEVEL}.'
        f'{settings.API_COUNTRY}.core.business_primary_data_changed'
    )

    assert BusinessPrimaryDataChangedMessage.topic_name == expected_topic_name


@pytest.mark.django_db
@freeze_time(FROZEN_DATE_TIME)
def test_message_payload_business_data():
    business = business_recipe.make()
    business.active_from = tznow()
    business.address = 'Test Address'
    business.zipcode = '12345'

    message = BusinessPrimaryDataChangedMessage(business)
    payload = message.message

    assert payload.event_timestamp == ProtobufDateTimeField.datetime_to_int(FROZEN_DATE_TIME)
    assert payload.business_id == business.id
    assert payload.primary_category == str(business.primary_category)
    assert payload.time_zone_name == business.time_zone_name
    assert payload.active_from == ProtobufDateTimeField.datetime_to_int(business.active_from)
    assert payload.name == business.name
    assert payload.status == business.status
    assert payload.owner.email == business.owner.email
    assert payload.country_code == business.country_code
    assert payload.address_details.address == 'Test Address'
    assert payload.address_details.city == 'Test City'
    assert payload.address_details.zipcode == '12345'


@pytest.mark.django_db()
class TestMessageSendBusinessDataActivated(BaseAsyncHTTPTest):
    """Based on service.business.tests.test_business_activate.BusinessActivationHandlerTestCase"""

    def setUp(self):
        super().setUp()
        self.business.primary_category = category_recipe.make()
        self.business.categories.add(self.business.primary_category)
        service_recipe.make(business=self.business)
        self.business.active = False
        self.business.save()
        HintAndWalkthroughExperiment.initialize()
        self.url = f'/business_api/me/businesses/{self.business.id}/activate/?'

    @override_feature_flag({HintsAndWalkThroughEventsPublishingFlag: True})
    @patch('service.business.create.BusinessPrimaryDataChangedMessage.publish')
    def test_grpc(self, mocked):
        assert mocked.called is False

        resp = self.fetch(self.url, body={'booking_mode': BookingMode.AUTO}, method='POST')

        assert resp.code == 200
        assert mocked.called is True


@pytest.mark.django_db(transaction=True, databases=['default', PAYMENTS_DB])
@override_feature_flag({HintsAndWalkThroughEventsPublishingFlag: True})
class TestMessageSendBusinessDataUpdated(unittest.TestCase):
    """Checks if message was published after object save, so it requires transaction test."""

    @patch('webapps.business.models.BusinessPrimaryDataChangedMessage.publish')
    @patch("webapps.payment_gateway.ports.PaymentGatewayPort.get_or_create_business_wallet")
    def test_grpc(self, _wallet_mock, mocked):
        business = business_recipe.make()
        assert mocked.called is False

        business.active_from = tznow()
        business.save()

        assert mocked.called is True

    @parameterized.expand(
        [
            ('time_zone_name', 'Europe/Warsaw'),
            ('primary_category', None),
            ('active_from', tznow()),
            ('name', 'new name 123'),
            ('status', Business.Status.TRIAL),
            ('address', 'new address details'),
            ('zipcode', 'new zip - code'),
            ('city', 'new city'),
        ]
    )
    @patch('webapps.business.models.BusinessPrimaryDataChangedMessage.publish')
    @patch("webapps.payment_gateway.ports.PaymentGatewayPort.get_or_create_business_wallet")
    def test_changing_field_calls_message_publish(
        self, business_field, new_value, _wallet_mock, mocked
    ):
        business = business_recipe.make()
        assert mocked.called is False
        setattr(business, business_field, new_value)
        business.save()
        assert mocked.called is True

    @patch('webapps.business.models.BusinessPrimaryDataChangedMessage.publish')
    @patch("webapps.payment_gateway.ports.PaymentGatewayPort.get_or_create_business_wallet")
    def test_changing_owner_email_calls_message_publish_with_flag(self, _wallet_mock, mocked):
        business = business_recipe.make()
        assert mocked.called is False
        business.owner.email = '<EMAIL>'
        business.owner.save()

        assert mocked.called is True
