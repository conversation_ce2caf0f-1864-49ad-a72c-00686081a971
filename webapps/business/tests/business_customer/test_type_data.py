import pytest

from service.exceptions import ServiceError
from webapps.business.baker_recipes import bci_recipe
from webapps.business.enums import CustomerCardType
from webapps.business.models.bci import BCITypeData
from webapps.business_customer_info.serializers.bci_type_data import BCITypeDataSerializer


@pytest.mark.django_db
def test_additional_data_default():
    bci = bci_recipe.make()
    assert bci.type_data is None
    data = BCITypeData.objects.create()
    bci.type_data = data
    bci.save()
    assert bci.type_data.card_type == CustomerCardType.PERSON
    assert bci.type_data.additional_data == {}


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('data'),
    [
        pytest.param(
            {'card_type': CustomerCardType.PERSON, 'additional_data': {}}, id='Person empty card'
        ),
        pytest.param(
            {'card_type': CustomerCardType.PET, 'additional_data': {}}, id='Pet empty card'
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {'registration_number': 'XXXYYY21'},
            },
            id='Vehicle minimal card',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {'pet_type': 'good', 'breed': 'unknown'},
            },
            id='Pet additional params',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {'weight': '49.3', 'additional_info': 'unknown'},
            },
            id='Pet additional params 2',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'manufacturer': 'Rolvo',
                    'model': 'S+',
                    'registration_number': 'SUPS',
                    'vin_number': 'AXDA903X',
                    'year': '1989',
                    'additional_info': 'unknown',
                },
            },
            id='Vehicle additional params',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'manufacturer': '',
                    'model': '',
                    'registration_number': 'AAAA',
                    'vin_number': '',
                    'year': None,
                    'additional_info': '',
                },
            },
            id='Vehicle empty not required fields',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'manufacturer': None,
                    'model': None,
                    'registration_number': 'AAAA',
                    'vin_number': None,
                    'year': None,
                    'additional_info': None,
                },
            },
            id='Vehicle null not required fields',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': '',
                    'breed': '',
                    'weight': None,
                    'additional_info': '',
                },
            },
            id='Pet empty not required fields',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': None,
                    'breed': None,
                    'weight': None,
                    'additional_info': None,
                },
            },
            id='Pet null not required fields',
        ),
    ],
)
def test_additional_data_valid(data):
    serializer = BCITypeDataSerializer(data=data)
    assert serializer.is_valid()
    assert serializer.data == data


def test_invalid_card_type():
    data = {'card_type': 'X', 'additional_data': {}}
    serializer = BCITypeDataSerializer(data=data)
    assert not serializer.is_valid()
    assert serializer.errors['card_type'][0].code == 'invalid_choice'


@pytest.mark.parametrize(
    ('data', 'error_messages', 'error_code'),
    [
        pytest.param(
            {'card_type': CustomerCardType.PERSON, 'additional_data': {'height': 172}},
            ['Unknown parameter(s): height'],
            'unknown_options',
            id='Person additional data',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PERSON,
                'additional_data': {'pet_type': 'good', 'breed': 'unknown'},
            },
            ['Unknown parameter(s): pet_type, breed', 'Unknown parameter(s): breed, pet_type'],
            'unknown_options',
            id='Person 2 values',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {'additional_info': '3 wheels', 'breed': 'unknown'},
            },
            ['Unknown parameter(s): breed'],
            'unknown_options',
            id='Vehicle additional data',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'manufacturer': 'Pusio',
                    'additional_info': '3 wheels',
                    'breed': 'unknown',
                    'first_name': 'Pusio',
                },
            },
            [
                'Unknown parameter(s): first_name, manufacturer',
                'Unknown parameter(s): manufacturer, first_name',
            ],
            'unknown_options',
            id='Vehicle additional data from bci',
        ),
        pytest.param(
            {'card_type': CustomerCardType.VEHICLE, 'additional_data': {}},
            [
                'Required parameter(s) missing: registration_number',
            ],
            'required_option_missing',
            id='Vehicle empty card',
        ),
    ],
)
def test_invalid_options(data, error_messages, error_code):
    serializer = BCITypeDataSerializer(data=data)
    assert not serializer.is_valid()
    assert serializer.errors['non_field_errors'][0].code == error_code
    assert str(serializer.errors['non_field_errors'][0]) in error_messages


@pytest.mark.parametrize(
    ('data', 'field', 'error_code'),
    [
        pytest.param(
            {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'manufacturer': 'Rolvo',
                    'model': 'S+',
                    'registration_number': 'SUPS',
                    'vin_number': 'AXDA903X',
                    'year': 'NOTAYEAR',
                    'additional_info': 'unknown',
                },
            },
            'year',
            'invalid',
            id='Vehicle wrong year param',
        ),
        pytest.param(
            {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'a' * 150,
                    'breed': 'Labrador',
                    'weight': '22',
                    'additional_info': 'good boy',
                },
            },
            'pet_type',
            'max_length',
            id='Pet too long pet_type',
        ),
    ],
)
def test_invalid_fields(data, field, error_code):
    serializer = BCITypeDataSerializer(data=data)
    with pytest.raises(ServiceError) as exc:
        serializer.is_valid()
    assert exc.value.errors[0]['code'] == error_code
    assert exc.value.errors[0]['field'] == field


@pytest.mark.django_db
def test_representation():
    type_data = BCITypeData.objects.create(
        card_type=CustomerCardType.PET, additional_data={'breed': 'Sphinx', 'skill': 'fetch'}
    )
    serializer = BCITypeDataSerializer(instance=type_data)
    data = serializer.data
    assert data['card_type'] == CustomerCardType.PET
    assert data['additional_data']['breed'] == "Sphinx"
