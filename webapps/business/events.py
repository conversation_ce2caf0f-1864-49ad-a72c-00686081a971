from lib.events import EventSignal


business_activated_event = EventSignal('business_activated_event')

business_bookmarked_event = EventSignal('business_bookmarked_event')

business_ended_promotion_event = EventSignal('business_ended_promotion')

service_created_event = EventSignal(event_type='service_created_event')

service_changed_event = EventSignal(event_type='service_changed_event')

switch_to_pro_event = EventSignal(event_type='switch_to_pro_event')

business_user_logged_in = EventSignal(event_type='business_user_logged_in')

business_status_changed_event = EventSignal(event_type='business_status_changed_event')

test_business_marked_event = EventSignal(event_type='test_business_marked_event')

business_primary_category_saved_event = EventSignal(
    event_type='business_primary_category_saved',
)

business_churn_scheduled = EventSignal(event_type="business_churn_scheduled")
