# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import webapps.booksy_gift_cards.notifications.api.grpc.notifications_pb2 as notifications__pb2


class NotificationsStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.send_gift_card_purchase_notification = channel.unary_unary(
            '/Notifications/send_gift_card_purchase_notification',
            request_serializer=notifications__pb2.GiftCardPurchaseNotificationRequest.SerializeToString,
            response_deserializer=notifications__pb2.NotificationRequestResponse.FromString,
        )
        self.send_dropped_gift_card_notification = channel.unary_unary(
            '/Notifications/send_dropped_gift_card_notification',
            request_serializer=notifications__pb2.DroppedGiftCardNotificationRequest.SerializeToString,
            response_deserializer=notifications__pb2.NotificationRequestResponse.FromString,
        )


class NotificationsServicer(object):
    """Missing associated documentation comment in .proto file."""

    def send_gift_card_purchase_notification(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def send_dropped_gift_card_notification(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_NotificationsServicer_to_server(servicer, server):
    rpc_method_handlers = {
        'send_gift_card_purchase_notification': grpc.unary_unary_rpc_method_handler(
            servicer.send_gift_card_purchase_notification,
            request_deserializer=notifications__pb2.GiftCardPurchaseNotificationRequest.FromString,
            response_serializer=notifications__pb2.NotificationRequestResponse.SerializeToString,
        ),
        'send_dropped_gift_card_notification': grpc.unary_unary_rpc_method_handler(
            servicer.send_dropped_gift_card_notification,
            request_deserializer=notifications__pb2.DroppedGiftCardNotificationRequest.FromString,
            response_serializer=notifications__pb2.NotificationRequestResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler('Notifications', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class Notifications(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def send_gift_card_purchase_notification(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/Notifications/send_gift_card_purchase_notification',
            notifications__pb2.GiftCardPurchaseNotificationRequest.SerializeToString,
            notifications__pb2.NotificationRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def send_dropped_gift_card_notification(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/Notifications/send_dropped_gift_card_notification',
            notifications__pb2.DroppedGiftCardNotificationRequest.SerializeToString,
            notifications__pb2.NotificationRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
