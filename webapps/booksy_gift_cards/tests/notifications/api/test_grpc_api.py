# pylint: disable=no-name-in-module,redefined-outer-name
import datetime
import random
import time
from typing import Generator, Type
from uuid import uuid4

import pytest
from django.db import connection
from django.test.utils import CaptureQueriesContext, override_settings
from django.utils.crypto import get_random_string
from django_socio_grpc.tests.grpc_test_utils.fake_grpc import FakeChannel, FakeRpcError
from mock import patch
from model_bakery import baker

from webapps.booksy_gift_cards.notifications.api.grpc.notifications_pb2 import (
    DroppedGiftCardNotificationRequest,
    GiftCardPurchaseNotificationRequest,
)
from webapps.booksy_gift_cards.notifications.api.grpc.notifications_pb2_grpc import (
    NotificationsStub,
)
from webapps.booksy_gift_cards.notifications.api.grpc.notifications_servicer import (
    BooksyGiftCardPurchasedNotification,
    NotificationsServicer,
)
from webapps.booksy_gift_cards.notifications.enums import GiftCardImageEnum
from webapps.booksy_gift_cards.notifications.utils import get_images_by_image_type
from webapps.user.models import User


class AsyncSendSpy:
    async_send_counter = 0

    def async_send(self):
        self.__class__.async_send_counter += 1


class NotificationSpy(AsyncSendSpy, BooksyGiftCardPurchasedNotification):
    pass


@pytest.fixture
def notification_class() -> Generator[Type[NotificationSpy], None, None]:

    yield NotificationSpy

    NotificationSpy.async_send_counter = 0


@pytest.fixture
def buyer() -> Generator[User, None, None]:
    yield baker.make(
        User,
        email=f"{get_random_string(8)}@gmail.com",
        cell_phone="".join([str(random.randint(0, 9)) for _ in range(9)]),
        first_name=get_random_string(5),
        last_name=get_random_string(5),
    )


@pytest.mark.grpc_api
@pytest.mark.django_db
@override_settings(API_GRPC=True)
def test_notification_is_scheduled(
    test_channel: FakeChannel, buyer: User, notification_class: Type[NotificationSpy]
):
    # given
    request = GiftCardPurchaseNotificationRequest(
        request_id="1",
        buyer_id=buyer.id,
        card_code="xxxxxxxxxxxx",
        card_external_id=str(uuid4()),
        card_value=100,
        card_expiration_date=int(time.time()),
    )
    # when
    with patch(
        "webapps.booksy_gift_cards.notifications.api.grpc.notifications_servicer.BooksyGiftCardPurchasedNotification",  # pylint: disable=line-too-long
        notification_class,
    ):
        with CaptureQueriesContext(connection):
            NotificationsStub(test_channel).send_gift_card_purchase_notification(request)
    # then
    assert notification_class.async_send_counter == 1


@pytest.mark.grpc_api
@pytest.mark.django_db
@override_settings(API_GRPC=True)
def test_not_found_error_is_returned_if_buyer_does_not_exist(test_channel):
    # given
    request = GiftCardPurchaseNotificationRequest(
        request_id="1",
        buyer_id=101,
        card_code="xxxxxxxxxxxx",
        card_external_id=str(uuid4()),
        card_value=100,
        card_expiration_date=int(time.time()),
    )
    # then
    with pytest.raises(FakeRpcError):
        # when
        with CaptureQueriesContext(connection):
            NotificationsStub(test_channel).send_gift_card_purchase_notification(request)


@pytest.mark.django_db
def test_build_notification_details(buyer: User):
    # given
    request = GiftCardPurchaseNotificationRequest(
        request_id="1",
        buyer_id=buyer.id,
        card_code="xxxxxxxxxxxx",
        card_external_id=str(uuid4()),
        card_value=10000,
        card_expiration_date=int(time.time()),
    )
    # when
    notification = NotificationsServicer().build_gift_card_purchase_notification(request)
    # then
    assert notification.details.notification_id == request.request_id
    assert notification.details.card_code == "xxxxxxxxxxxx"
    assert notification.details.card_value == 100
    assert notification.details.card_expiration_date == datetime.date.today()
    assert notification.details.email_image == 'booksy-gift-card-rewers-decorated.png'
    assert notification.details.pdf_image == 'booksy-gift-card-rewers.png'


@pytest.mark.django_db
def test_build_christmas_notification_details(buyer: User):
    # given
    request = GiftCardPurchaseNotificationRequest(
        request_id="1",
        buyer_id=buyer.id,
        card_code="xxxxxxxxxxxx",
        card_external_id=str(uuid4()),
        card_value=10000,
        card_expiration_date=int(time.time()),
        background_image=GiftCardImageEnum.CHRISTMAS_1,
    )
    # when
    notification = NotificationsServicer().build_gift_card_purchase_notification(request)
    # then
    email_image, pdf_image = get_images_by_image_type(image_type=GiftCardImageEnum.CHRISTMAS_1)
    assert notification.details.email_image == email_image
    assert notification.details.pdf_image == pdf_image


@pytest.mark.grpc_api
@pytest.mark.django_db
@patch(
    'webapps.booksy_gift_cards.notifications.notifications.DroppedBooksyGiftCardNotification.send'
)
def test_send_dropped_card_notification(
    notification_mock,
    test_channel: FakeChannel,
    buyer: User,
):
    request = DroppedGiftCardNotificationRequest(
        request_id='1',
        buyer_id=buyer.id,
        card_id=str(uuid4()),
    )
    NotificationsStub(test_channel).send_dropped_gift_card_notification(request)

    assert notification_mock.called


@pytest.mark.grpc_api
@pytest.mark.django_db
@patch(
    'webapps.booksy_gift_cards.notifications.api.grpc.notifications_servicer.DroppedBooksyGiftCardNotification'  # pylint: disable=line-too-long
)
@patch(
    'webapps.booksy_gift_cards.notifications.notifications.DroppedBooksyGiftCardNotification.send'
)
def test_dropped_card_notification_params(
    send_notification_mock,
    notification_mock,
    test_channel: FakeChannel,
    buyer: User,
):
    card_id = uuid4()
    request = DroppedGiftCardNotificationRequest(
        request_id='1',
        buyer_id=buyer.id,
        card_id=str(card_id),
    )
    NotificationsStub(test_channel).send_dropped_gift_card_notification(request)

    notification_mock.assert_called_with(
        buyer,
        notification_id='1',
        card_id=card_id,
    )
