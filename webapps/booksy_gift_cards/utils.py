import attr
from more_itertools import sliced
from weasyprint import H<PERSON><PERSON>

from lib.pdf_render import PDF<PERSON><PERSON><PERSON>, build_pdf_font_config

from webapps.booksy_gift_cards.notifications.details import PurchasedCardNotificationDetails
from webapps.booksy_gift_cards.notifications.exceptions import UnexpectedPDFGenerationError


def format_card_code(gift_card_number: str) -> str:
    return "-".join(sliced("".join(filter(str.isalnum, gift_card_number)), 4))


def generate_gift_card_pdf(
    template_name: str,
    notification_details: PurchasedCardNotificationDetails,
    html_file_path: str | None = None,
) -> bytes:
    pdf_body = PDFRenderer.render_pdf_body(
        template_name,
        data=attr.asdict(notification_details) | {"format_card_code": format_card_code},
        escape_html=False,
    )
    pdf_file = HTML(string=pdf_body).write_pdf(font_config=build_pdf_font_config())

    if pdf_file is None:
        raise UnexpectedPDFGenerationError("weasyprint for unknown reason returned 'None'")

    if html_file_path is not None:
        with open(html_file_path, "w", encoding="utf-8") as f:
            f.writelines(pdf_body)

    return pdf_file
