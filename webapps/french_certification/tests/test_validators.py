import unittest
import uuid
from contextlib import contextmanager
from decimal import Decimal
from typing import TypedDict, NamedTuple
from unittest.mock import patch, Mock

from rest_framework import serializers

from lib.point_of_sale.entities import RelatedBasketItemEntity, BasketItemEntity
from lib.point_of_sale.enums import RelatedBasketItemType, BasketItemType
from webapps.french_certification.validators import validate_not_changing_price_or_discount


class MockedSubbooking(NamedTuple):
    id: int


class MockedRow(TypedDict):
    subbooking: MockedSubbooking
    item_price: Decimal
    discount_rate: int
    addon_use: bool  # for these tests this suffices


class TestEditingPaidRows(unittest.TestCase):
    def setUp(self):
        basket_item_1_id_service_variant = uuid.uuid4()
        basket_item_2_id_addon1 = uuid.uuid4()
        basket_item_3_id_addon2 = uuid.uuid4()
        subbooking_id = 1
        basket_id = uuid.uuid4()
        basket_item_1_price = 10000
        basket_item_2_price = 500
        basket_item_3_price = 1000

        basket_item_1_discount_rate = 10
        basket_item_2_discount_rate = 5
        basket_item_3_discount_rate = 0

        self.rows = [
            MockedRow(
                subbooking=MockedSubbooking(id=subbooking_id),
                item_price=Decimal(basket_item_1_price / 100),
                discount_rate=basket_item_1_discount_rate,
                addon_use=False,
            ),
            MockedRow(
                subbooking=MockedSubbooking(id=subbooking_id),
                item_price=Decimal(basket_item_2_price / 100),
                discount_rate=basket_item_2_discount_rate,
                addon_use=True,
            ),
            MockedRow(
                subbooking=MockedSubbooking(id=subbooking_id),
                item_price=Decimal(basket_item_3_price / 100),
                discount_rate=basket_item_3_discount_rate,
                addon_use=True,
            ),
        ]
        self.related_basket_items = [
            RelatedBasketItemEntity(
                id=uuid.uuid4(),
                basket_item_id=basket_item_1_id_service_variant,
                external_id=subbooking_id,
                type=RelatedBasketItemType.SUBBOOKING,
            ),
            RelatedBasketItemEntity(
                id=uuid.uuid4(),
                basket_item_id=basket_item_2_id_addon1,
                external_id=subbooking_id,
                type=RelatedBasketItemType.SUBBOOKING,
            ),
            RelatedBasketItemEntity(
                id=uuid.uuid4(),
                basket_item_id=basket_item_3_id_addon2,
                external_id=subbooking_id,
                type=RelatedBasketItemType.SUBBOOKING,
            ),
        ]
        self.basket_items = [
            BasketItemEntity(
                id=basket_item_1_id_service_variant,
                basket_id=basket_id,
                item_price=basket_item_1_price,
                discount_rate=basket_item_1_discount_rate,
                gross_total=0,
                type=BasketItemType.SERVICE,
            ),
            BasketItemEntity(
                id=basket_item_2_id_addon1,
                basket_id=basket_id,
                item_price=basket_item_2_price,
                discount_rate=basket_item_2_discount_rate,
                gross_total=0,
                type=BasketItemType.ADDON,
            ),
            BasketItemEntity(
                id=basket_item_3_id_addon2,
                basket_id=basket_id,
                item_price=basket_item_3_price,
                discount_rate=basket_item_3_discount_rate,
                gross_total=0,
                type=BasketItemType.ADDON,
            ),
        ]

    @property
    def mock_adapters(self):
        @contextmanager
        def _mock_adapters():
            with (
                patch(
                    'webapps.french_certification.validators.get_related_basket_items_with_external_ids_adapter',  # pylint: disable=line-too-long
                    Mock(
                        return_value=self.related_basket_items,
                    ),
                ),
                patch(
                    'webapps.french_certification.validators.get_basket_items_adapter',
                    Mock(
                        return_value=self.basket_items,
                    ),
                ),
                patch(
                    'webapps.french_certification.validators.get_basket_items_for_receipt_adapter',
                    Mock(
                        return_value=self.basket_items,
                    ),
                ),
            ):
                yield

        return _mock_adapters

    def test_no_changes_to_paid_rows(self):
        with self.mock_adapters():
            validate_not_changing_price_or_discount(
                prepayment_fiscal_receipt=Mock(),
                rows=self.rows,
            )

    def test_paid_rows_item_price_changed(self):
        self.rows[0]['item_price'] += 5
        with (
            self.mock_adapters(),
            self.assertRaises(serializers.ValidationError),
        ):
            validate_not_changing_price_or_discount(
                prepayment_fiscal_receipt=Mock(),
                rows=self.rows,
            )

    def test_paid_rows_discount_rate_changed(self):
        self.rows[0]['discount_rate'] += 5
        with (
            self.mock_adapters(),
            self.assertRaises(serializers.ValidationError),
        ):
            validate_not_changing_price_or_discount(
                prepayment_fiscal_receipt=Mock(),
                rows=self.rows,
            )
