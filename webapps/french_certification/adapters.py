import uuid
from decimal import Decimal

from lib.business.entities import ServiceVariantPaymentEntity
from lib.point_of_sale.enums import RelatedBasketItemType

from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    BasketItemEntity,
    RelatedBasketItemEntity,
    BasketEntity,
)
from webapps.booking.ports import AppointmentPort
from webapps.business.enums import NoShowProtectionType
from webapps.business.ports.business import get_service_variant_payment
from webapps.french_certification.entities import BasketPaymentRefundStatus
from webapps.point_of_sale.ports import BasketItemPort


def get_email_from_basket_id_adapter(basket_id: uuid.UUID):
    from webapps.pos.ports import get_customer_info_data
    from webapps.business.models.bci import BusinessCustomerInfo
    from webapps.point_of_sale.ports import BasketPort

    basket = BasketPort.get_basket(basket_id=basket_id)

    customer_card_id = basket.customer_card_id
    if customer_card_id:
        business_customer_info = BusinessCustomerInfo.objects.get(id=customer_card_id)
        return get_customer_info_data(bci_instance=business_customer_info)['email']
    return None


def is_refund_possible_adapter(
    basket_payment_id,
    check_requested,
    check_balance,
    refresh_transaction,
):
    from webapps.pos.ports import RefundPort

    return RefundPort.is_refund_possible(
        basket_payment_id,
        check_requested,
        check_balance,
        refresh_transaction,
    )


def payment_row_id_from_basket_payment_adapter(basket_payment_id):
    from webapps.pos.ports import RefundPort

    return RefundPort.payment_row_id_from_basket_payment(basket_payment_id)


def basket_payment_refund_status_adapter(basket_payment_id) -> BasketPaymentRefundStatus | None:
    from webapps.pos.ports import RefundPort

    if refund_status := RefundPort.basket_payment_refund_status(
        basket_payment_id,
    ):
        return BasketPaymentRefundStatus(**refund_status)


def get_basket_adapter(basket_id: uuid.UUID) -> BasketEntity:
    from webapps.point_of_sale.ports import BasketPort

    return BasketPort.get_basket(basket_id=basket_id)


def get_basket_payments_adapter(ids: list[uuid.UUID]) -> list[BasketPaymentEntity]:
    from webapps.point_of_sale.ports import BasketPaymentPort

    return BasketPaymentPort.get_basket_payments(ids=ids)


def get_latest_refund_child_adapter(payment_id: uuid.UUID) -> BasketPaymentEntity | None:
    from webapps.point_of_sale.ports import BasketPaymentPort

    return BasketPaymentPort.get_latest_refund_child(payment_id)


def get_prepay_rate_adapter(
    price: int,
    service_variant_payment: ServiceVariantPaymentEntity,
) -> Decimal:
    from webapps.booking.ports import get_prepay_rate_port

    return get_prepay_rate_port(price, service_variant_payment)


def get_service_variant_payment_adapter(
    service_variant_id: int,
) -> ServiceVariantPaymentEntity | None:
    return get_service_variant_payment(
        service_variant_id,
        payment_type=NoShowProtectionType.PREPAYMENT.value,
    )


def get_basket_items_for_receipt_adapter(receipt: 'FiscalReceipt') -> list[BasketItemEntity]:
    return BasketItemPort.get_basket_items(receipt.basket_item_ids)


def get_basket_items_adapter(ids: list[uuid.UUID]) -> list[BasketItemEntity]:
    return BasketItemPort.get_basket_items(ids)


def get_related_basket_items_with_basket_items_adapter(
    basket_items_ids: list[uuid.UUID], relation_types: list[RelatedBasketItemType]
) -> list[RelatedBasketItemEntity]:
    from webapps.point_of_sale.ports import RelatedBasketItemPort

    return RelatedBasketItemPort.get_related_basket_items_by_basket_items(
        basket_items_ids, relation_types
    )


def get_related_basket_items_with_external_ids_adapter(
    external_ids: list[int], relation_type: RelatedBasketItemType
) -> list[RelatedBasketItemEntity]:
    from webapps.point_of_sale.ports import RelatedBasketItemPort

    return RelatedBasketItemPort.get_related_basket_items_by_external_ids(
        external_ids, relation_type
    )


def get_basket_id_with_related_basket_item_adapter(
    *,
    external_id: int,
    relation_type: RelatedBasketItemType,
) -> uuid.UUID | None:
    from webapps.point_of_sale.ports import BasketPort

    basket_ids = BasketPort.get_basket_ids_with_related_basket_item(
        external_ids=[external_id],
        relation_type=relation_type,
    )
    return basket_ids[0] if basket_ids else None


def get_basket_ids_for_appointments_adapter(
    appointment_ids: list[int],
) -> list[uuid.UUID]:
    from webapps.point_of_sale.ports import BasketPort

    return BasketPort.get_basket_ids_with_related_basket_item(
        relation_type=RelatedBasketItemType.APPOINTMENT,
        external_ids=appointment_ids,
    )


def get_successful_payments_adapter(ids: list[uuid.UUID]) -> list[BasketPaymentEntity]:
    from webapps.point_of_sale.ports import BasketPaymentPort

    return BasketPaymentPort.get_successful_payments(ids)


def get_payments_for_basket_adapter(basket_id: uuid.UUID):
    from webapps.point_of_sale.ports import BasketPaymentPort

    return BasketPaymentPort.get_payments_for_basket(basket_id)


def get_customer_appointments_ids_adapter() -> list[int]:
    return AppointmentPort.get_customer_appointments_ids()
