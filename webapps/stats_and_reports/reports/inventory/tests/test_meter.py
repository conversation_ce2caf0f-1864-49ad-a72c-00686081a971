import datetime
from decimal import Decimal

from django.test import TestCase

from webapps.stats_and_reports.reports.base import Horizontal<PERSON><PERSON>, MeterValue
from webapps.stats_and_reports.reports.inventory.meter import (
    InventoryMeterSection,
)
from webapps.stats_and_reports.reports.inventory.tests.test_high_rotating_products import (  # pylint: disable=line-too-long
    HighRotatingProductsPrepareDataMixin,
)
from webapps.stats_and_reports.reports.time_data import (
    TimeDataScope,
    TimeScopeType,
)


class InventoryMeterSectionTests(
    HighRotatingProductsPrepareDataMixin,
    TestCase,
):
    @classmethod
    def setUpTestData(cls):
        cls.prepare_high_rotating_products_data()
        cls.time_scope = TimeDataScope(
            cls.business,
            'en',
            date_from=datetime.date(2020, 12, 1),
            date_till=datetime.date(2020, 12, 31),
            time_span=TimeScopeType.MONTH,
        )
        cls.meter_section = InventoryMeterSection(cls.time_scope)

    def test_calculate_stats(self):
        res = self.meter_section.calculate_stats()
        expected_res = {
            'high_rotating_count': 2,
            'high_rotating_value': Decimal('20.00'),
            'low_rotating_count': 2,
            'low_rotating_value': Decimal('20.00'),
        }
        self.assertDictEqual(res, expected_res)

    @staticmethod
    def test_products_count_label():
        res = InventoryMeterSection.products_count_label(1)
        assert res == '1 product'
        res = InventoryMeterSection.products_count_label(2)
        assert res == '2 products'

    def test_get_meters(self):
        res = self.meter_section.get_meters()
        expected = [
            HorizontalMeter(
                left=MeterValue(
                    name='High rotating',
                    value_label='2 products',
                    percentage_value=50,
                ),
                right=MeterValue(
                    name='Low rotating',
                    value_label='2 products',
                    percentage_value=50,
                ),
            ),
            HorizontalMeter(
                left=MeterValue(
                    name='High rotating value',
                    value_label='$20.00',
                    percentage_value=50,
                ),
                right=MeterValue(
                    name='Low rotating value',
                    value_label='$20.00',
                    percentage_value=50,
                ),
            ),
        ]
        self.assertListEqual(res, expected)
