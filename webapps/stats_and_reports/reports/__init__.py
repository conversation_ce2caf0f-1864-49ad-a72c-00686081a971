from .appointment_reports.appointments_by_days import (
    AppointmentsByDaysAndHours,
    AppointmentsByDaysAndHoursSection,
)
from .appointment_reports.appointments_by_services import (
    AppointmentsListByServices,
    ServiceAppointmentsListSection,
)
from .appointment_reports.appointments_by_staffer import (
    AppointmentsListByStaffer,
    StafferAppointmentsListSection,
)
from .appointment_reports.appointments_cancellations import (
    AppointmentsCancellations,
    AppointmentsCancellationsSection,
    AppointmentsNoShows,
    AppointmentsNoShowsSection,
)
from .appointment_reports.appointments_charts import (
    AppointmentsChart,
)
from .appointment_reports.appointments_list import (
    AppointmentsList,
    AppointmentsListSection,
)
from .appointment_reports.appointments_summary import (
    AppointmentsSummary,
    AppointmentsSummarySection,
    BestDaysSection,
    BestHoursSection,
    HighlightsSection,
    TimeOccupancySection,
    TopServicesSection,
    TopAddonsSection,
)
from .appointment_reports.categories_services_summary import (
    CategoriesServicesSummary,
    CategoriesServicesSummarySection,
    TopCategoriesSubcategoriesSection,
    TopServicesCategoriesSection,
)
from .cash_flow.cancellation_fees import (
    CancellationFeesReport,
    CancellationFeesSection,
)
from .cash_flow.cash_registers_summary import (
    CashRegistersSummary,
    CashRegistersSummarySection,
)
from .cash_flow.cash_registers_transactions import (
    CashRegistersTransactions,
    CashRegistersTransactionsSection,
)
from .cash_flow.mobile_payments_log import (
    MobilePaymentsLogReport,
    MobilePaymentsLogSection,
)
from .cash_flow.mobile_payments_transactions_summary import (
    MobilePaymentsTransactionsSummaryReport,
    MobilePaymentsTransactionsSummarySection,
    MobilePaymentsTransactionsSummaryTotalSection,
)
from .cash_flow.payment_types_summary import (
    PaymentTypesSummary,
    PaymentTypesSummarySection,
)
from .cash_flow.payout_batches import (
    PayoutBatchesReport,
    PayoutBatchesSection,
)
from .cash_flow.prepayments import (
    PrepaymentsReport,
    PrepaymentsSection,
)
from .cash_flow.refunds import (
    RefundsReport,
    RefundsSection,
)
from .cash_flow.stripe_payout_batches import (
    StripePayoutBatchesReport,
    StripePayoutBatchesSection,
    StripeSinglePayoutReport,
    StripeSinglePayoutSection,
)

from .cash_flow.square_payments import (
    SquarePaymentsReport,
    SquarePaymentsSection,
)
from .cash_flow.bcr_transactions_summary import (
    BCRTransactionsSummaryReport,
    BCRTransactionsSummarySection,
    BCRTransactionsSummaryTotalSection,
)
from .cash_flow.bcr_log import (
    BCRLogReport,
    BCRLogSection,
)
from .cash_flow.bcr_refunds import (
    BCRRefundsReport,
    BCRRefundsSection,
)
from .cash_flow.bcr_payout_batches import (
    BCRSinglePayoutReport,
    BCRSinglePayoutSection,
)
from .cash_flow.ttp_transactions_summary import (
    TTPTransactionsSummaryReport,
    TTPTransactionsSummarySection,
    TTPTransactionsSummaryTotalSection,
)
from .cash_flow.ttp_log import (
    TTPLogReport,
    TTPLogSection,
)
from .cash_flow.ttp_refunds import (
    TTPRefundsReport,
    TTPRefundsSection,
)
from .client_reports.clients_by_group import (
    ClientsByGroupSection,
)
from .client_reports.clients_by_type import (
    ClientsByTypesSection,
)
from .client_reports.clients_charts import (
    ClientsChart,
    NewAndReturningClientsChart,
)
from .client_reports.clients_list import (
    ClientsList,
    ClientsListSection,
)
from .client_reports.clients_no_shows import (
    ClientsNoShows,
    ClientsNoShowsSection,
)
from .client_reports.clients_summary import (
    ClientsByHashTagsSection,
    ClientsSummaryReport,
    ClientsSummarySection,
)
from .client_reports.new_clients import (
    NewClients,
    NewClientsSection,
)
from .client_reports.potential_clients import (
    PotentialClientsReport,
    PotentialClientsSection,
)
from .client_reports.returning_clients import (
    ReturningClients,
    ReturningClientsSection,
)
from .client_reports.sleeping_away_clients import (
    SlippingAwayClients,
    SlippingAwayClientsSection,
)
from .client_reports.top_clients import (
    TopClientsSection,
)
from .inventory.dashboard_box import (
    InventoryDashboardBox,
)
from .inventory.high_rotating_products import (
    HighRotatingProductsDashboardSection,
)
from .inventory.low_stock import (
    LowStock,
    LowStockDashboardSection,
    WarehouseLowStockSection,
)
from .inventory.stock_consumption_summary import (
    InternalExpendituresSummarySection,
    InternalStockConsumptionSummary,
    RwReasonExpendituresSection,
)
from .inventory.stock_movement_summary import (
    StockMovementSummary,
    WarehouseStockMovementSection,
)
from .inventory.stock_on_hand import (
    StockOnHand,
    WarehouseStockOnHandSection,
)
from .inventory.stock_rotation import (
    StockRotation,
    WarehouseStockRotationSection,
)
from .inventory.stock_value import (
    StockValue,
    WarehouseStockValueSection,
)
from .promotion.boost_charts import (
    BoostNewAndReturningClientsChart,
)
from .promotion.boost_claims import (
    BoostClaimsReport,
    BoostClaimsSection,
)
from .promotion.dashboard_tables import (
    TopBoostClientsSection,
)
from .promotion.dashboard_box import (
    BoostDashboardBox,
)
from .promotion.new_clients_from_boost import (
    CompletedAppointmentsFromBoostSection,
    FutureAppointmentsFromBoostSection,
    NewClientsFromBoostReport,
    NewClientsFromBoostSummarySection,
)
from .promotion.special_offers_summary import (
    SpecialOffersSummaryReport,
    SpecialOffersSummarySection,
)
from .revenue.appointments_profitability import (
    AppointmentsProfitability,
    AppointmentsProfitabilitySection,
)
from .revenue.discount import (
    DiscountSection,
    DiscountsReport,
)
from .revenue.membership_summary import (
    MembershipsSummary,
    MembershipsActiveSection,
    MembershipsCloseToExpirationSection,
    MembershipsExpiredSection,
)
from .revenue.memberships_redemptions import (
    MembershipsRedemptions,
    MembershipsRedemptionsSection,
)
from .revenue.outstanding_invoices import (
    OutstandingInvoices,
    OutstandingInvoicesSection,
)
from .revenue.packages_redemptions import (
    PackagesRedemptions,
    PackagesRedemptionsSection,
)
from .revenue.packages_summary import (
    PackagesSummary,
    PackagesSummaryActiveSection,
    PackagesSummaryExpiredCloseSection,
    PackagesSummaryExpiredSection,
)
from .revenue.revenue_charts import (
    RevenueChart,
)
from .revenue.revenue_forecast import (
    RevenueForecast,
    RevenueForecastSection,
)
from .revenue.staff_revenue_forecast import (
    StaffRevenueForecast,
    StaffRevenueForecastSection,
)
from .revenue.total_revenue import (
    TotalRevenueSection,
)
from .sales_reports.payment_methods_archive import (
    PaymentMethodsArchive,
    PaymentMethodsArchiveSection,
)
from .sales_reports.sales_by_giftcards import (
    SalesByGiftCards,
    SalesByGiftLogSection,
    SalesByGiftRedemptionsLogSection,
    SalesByGiftSection,
)
from .sales_reports.sales_by_memberships import (
    MembershipsSalesLogSection,
    SalesByMemberships,
    SalesByMembershipsSection,
)
from .sales_reports.sales_by_packages import (
    PackagesSalesLogSection,
    SalesByPackages,
    SalesByPackagesSection,
)
from .sales_reports.sales_by_product import (
    SalesByBrandsSection,
    SalesByProducts,
    SalesByProductsCategoriesSection,
    SalesByProductsSection,
)
from .sales_reports.sales_by_services import (
    SalesByServices,
    SalesByComboServicesSection,
    SalesByServicesCategoriesSection,
    SalesByServicesSection,
)
from .sales_reports.sales_log import (
    SalesLog,
    SalesLogSection,
    SalesLogBeforeFrenchMigration,
    SalesLogBeforeFrenchMigrationSection,
)
from .sales_reports.sales_summary import (
    SalesSummary,
    SalesSummarySection,
    SalesSummaryBeforeFrenchMigration,
    SalesSummaryBeforeFrenchMigrationSection,
)
from .sales_reports.sales_trends import (
    SalesByDaysSection,
    SalesByHoursSection,
    SalesByMonthsSection,
    SalesTrends,
)
from .sales_reports.taxes_summary import (
    TaxesSummary,
    TaxesSummaryProductsSection,
    TaxesSummaryServicesSection,
)
from .staff.dashboard_tables import (
    ShiftWorksSection,
    StaffMembersCommissionSection,
    Top10StaffMembersRevenueSection,
    StaffMemberOccupancySection,
)
from .staff.staff_commision_details import (
    StaffCommissionDetails,
    StaffCommissionDetailsSection,
)
from .staff.staff_performance_per_staffer.report import StaffPerformancePerStaffer
from .staff.staff_time_offs import (
    StaffTimeOffsReport,
    StaffTimeOffsSection,
)
from .staff.staff_working_hours import (
    StaffWorkingHoursReport,
    StaffWorkingHoursSection,
)
from .staff.staffer_comission_summary import (
    StaffCommissionSummary,
    StaffCommissionSummarySection,
)
from .staff.staffer_gratuity import (
    StaffGratuity,
    StaffGratuitySection,
)
from .staff.staffer_perfomance_summary import (
    StaffPerformanceSummary,
    StaffPerformanceSummarySection,
)
from .staff.staff_revenue_payment_methods import (
    StaffRevenuePaymentMethods,
    StaffRevenuePaymentMethodsSection,
    StaffRevenuePaymentMethodsComplexTransactionsSection,
)
from .other.business_performance_report import (
    BusinessPerformanceReport,
)
