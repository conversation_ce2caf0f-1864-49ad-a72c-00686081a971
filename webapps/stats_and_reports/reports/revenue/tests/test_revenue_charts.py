import datetime
from decimal import Decimal

import pytest
import pytz
from freezegun import freeze_time

from webapps.stats_and_reports.reports.revenue.revenue_charts import (
    RevenueChart,
    RevenueChartStaffFiltered,
)
from webapps.stats_and_reports.reports.tests.base import PrepareData
from webapps.stats_and_reports.reports.time_data import TimeDataScope, TimeScopeType


@pytest.mark.django_db
@freeze_time(datetime.datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
class RevenueChartTests(PrepareData):
    def setUp(self):
        super().setUp()
        self.chart = RevenueChart(self.time_scope)
        self.chart_staffer_a = RevenueChartStaffFiltered(
            self.time_scope, self.staffer_a, self.staffer_a.staff_access_level
        )
        self.chart_staffer_b = RevenueChartStaffFiltered(
            self.time_scope, self.staffer_b, self.staffer_b.staff_access_level
        )

    def test_get_tips(self):
        """There are:
        -) TransactionTip 700.00 for Transaction txn_1 with rows txnr_5, txnr_7, txnr_8
           all for Staffer B
        -) TransactionTip 400.00 for Transaction txn_05 (without rows, should not be counted)
        -) TransactionTip 600.00 for Transaction txn_0 with rows txnr_1, txnr_2, txnr_3, txnr_4
           all for Staffer B and split tips:
               - TransactionTipRow 200.00 for Staffer A
               - TransactionTipRow 400.00 for Staffer B
        """

        res = self.chart.get_tips()
        self.assertDictEqual(
            res[0],
            {
                'date': datetime.datetime(2020, 12, 1, tzinfo=pytz.utc),
                'sum': Decimal('1300.00'),
            },
        )

        staffer_a_res = self.chart_staffer_a.get_tips()
        self.assertDictEqual(
            staffer_a_res[0],
            {
                'date': datetime.datetime(2020, 12, 1, tzinfo=pytz.utc),
                'normal_tips': Decimal('0.00'),
                'split_tips': Decimal('200.00'),
                'sum': Decimal('200.00'),
            },
        )

        staffer_b_res = self.chart_staffer_b.get_tips()
        self.assertDictEqual(
            staffer_b_res[0],
            {
                'date': datetime.datetime(2020, 12, 1, tzinfo=pytz.utc),
                'normal_tips': Decimal('700.00'),
                'split_tips': Decimal('400.00'),
                'sum': Decimal('1100.00'),
            },
        )

    def test_coerce_data_to_table_sales_log(self):
        result = self.chart.get_data()
        result_rows_list = [
            [{point.date: point.value} for point in serie.data_points if point.value != 0]
            for serie in result.series
        ]
        expected_rows_list = [{datetime.date(2020, 12, 1): 1973}]
        self.assertCountEqual(result_rows_list[0], expected_rows_list)

    def test_chart_has_any_data(self):
        # 2020 has data
        assert self.chart.has_any_data

        # 2018 does not have data
        chart = RevenueChart(
            scope=TimeDataScope(
                self.business,
                'en',
                date_from=datetime.date(2018, 12, 1),
                date_till=None,
                time_span=TimeScopeType.MONTH,
            )
        )
        assert not chart.has_any_data
