import builtins
import dataclasses
from dataclasses import dataclass
from decimal import Decimal

from datetime import date
from operator import attrgetter
from typing import Optional

import rest_framework.fields
from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework_dataclasses.fields import DefaultDecimalField
from rest_framework_dataclasses.serializers import DataclassSerializer
from rest_framework import serializers

from webapps.stats_and_reports.reports.base import (
    ReportTable,
    ReportTableRow,
    SectionTooltips,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_number,
    ws_range,
    NumberFormat,
    format_font,
    format_alignment,
)

from webapps.stats_and_reports.reports.staff.staff_performance_per_staffer.constants import (
    ReviewRating,
    LongText,
)
from webapps.stats_and_reports.reports.styling import format_table_total_row
from webapps.stats_and_reports.reports import fields as report_fields


class DataclassRowSerializer(ReportTableRow):
    def get_serializer_class(self):
        class RowSerializer(DataclassSerializer):
            class Meta:
                dataclass = type(self)

        return RowSerializer


@dataclass
class BaseTooltipTableRow(DataclassRowSerializer):
    _tooltip: dataclasses.InitVar[dict[str, str]] = dataclasses.field(init=False)

    def add_tooltip(self, tooltip: dict[str, str]):
        header_names = {h.name for h in dataclasses.fields(self)}
        if diff := tooltip.keys() - header_names:
            raise KeyError(
                f"Class {type(self)} doesn't contain fields: {diff}."
                f"None of those can be used as tooltip."
            )
        self._tooltip = tooltip
        return self


class ToolTipReportTable(ReportTable):
    header_tooltips: dict[str, str] | None = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        row_tooltips = []
        for pos, row in enumerate(self.rows):
            if getattr(row, '_tooltip', None) is not None:
                for tooltip_key, tooltip_value in row._tooltip.items():
                    row_tooltips.append({"index": pos, tooltip_key: tooltip_value})
        self.tooltips = SectionTooltips(headers=self.header_tooltips, rows=row_tooltips)


def add_header_tooltip(**tooltip_kwargs):
    def inner(cls: type[ReportTable]):
        if not issubclass(cls, ToolTipReportTable):
            raise builtins.RuntimeError(
                f'Subclasses of {ReportTable} should inherit '
                f'{ToolTipReportTable} in order to support tooltips.'
            )
        if tooltip_kwargs:
            header_names = {h.name for h in dataclasses.fields(cls.Row)}
            if diff := tooltip_kwargs.keys() - header_names:
                raise KeyError(
                    f"Class {cls} doesn't contain fields: {diff}."
                    f"None of those can be used as tooltip."
                )
            cls.header_tooltips = tooltip_kwargs

        return cls

    return inner


class StafferPerformanceOverview(ToolTipReportTable):
    header = (
        _('Metric'),
        _('Value'),
    )

    @dataclass
    class Row(BaseTooltipTableRow):  # pylint: disable=too-many-instance-attributes
        field: str
        value: str

    @dataclass
    class DecimalRow(Row):
        value: Decimal

    @dataclass
    class CoutnerRow(Row):
        value: int

    @dataclass
    class CurrencyRow(DecimalRow):
        xlsx_format = NumberFormat.CURRENCY

        def get_serializer_class(self):
            return CurrencyFieldRowSerializer

    @dataclass
    class PercentageRow(DecimalRow):
        xlsx_format = NumberFormat.PERCENTAGE

        def get_serializer_class(self):
            return PercentageFieldRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        if isinstance(
            row, (StafferPerformanceOverview.CurrencyRow, StafferPerformanceOverview.PercentageRow)
        ):
            format_number(ws_range(ws, 'C:C'), row.xlsx_format)
        elif isinstance(row, StafferPerformanceOverview.Row):
            format_alignment(ws_range(ws, 'C:C'), Alignment(horizontal='right'))


class CurrencyFieldRowSerializer(DataclassSerializer):
    value = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = StafferPerformanceOverview.CurrencyRow
        fields = [
            *map(attrgetter('name'), dataclasses.fields(StafferPerformanceOverview.CurrencyRow))
        ]


class PercentageFieldRowSerializer(DataclassSerializer):
    value = report_fields.ReportsPercentageField()

    class Meta:
        dataclass = StafferPerformanceOverview.PercentageRow
        fields = [
            *map(attrgetter('name'), dataclasses.fields(StafferPerformanceOverview.PercentageRow))
        ]


class BoldLastRowMixin:
    def render_spreadsheet_content(
        self,
        ws: Worksheet,
        enumerated: bool = True,
    ):
        # pylint: disable=protected-access
        super().render_spreadsheet_content(ws, enumerated)
        last_row = ws[ws._current_row]
        format_table_total_row(ws, n_cols=len(last_row))


@add_header_tooltip(
    discount=_('Total value of all applied discounts (individual discounts and promotions).')
)
class AppointmentsOverviewTable(BoldLastRowMixin, ToolTipReportTable):
    header = (
        _('Appointment type'),
        _('Quantity'),
        _('%'),
        _('Hours'),
        _('Service value'),
        _('Net revenue'),
        _('Discount'),
        _('Tax'),
        _('Tips'),
        _('Commissions'),
        _('Total revenue'),
        _('Total revenue without tips'),
    )

    @dataclass
    class Row(BaseTooltipTableRow):  # pylint: disable=too-many-instance-attributes
        appo_status: str
        quantity: Optional[int] = None
        quantity_rel: Optional[Decimal] = None
        appo_duration: Optional[relativedelta] = None
        appo_value: Optional[Decimal] = None
        revenue_net: Optional[Decimal] = None
        discount: Optional[Decimal] = None
        tax: Optional[Decimal] = None
        tips: Optional[Decimal] = None
        commission: Optional[Decimal] = None
        total_revenue: Optional[Decimal] = None
        total_revenue_no_tips: Optional[Decimal] = None

        def get_serializer_class(self):
            return AppointmentOverviewRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'F:N'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'D:D'), '0.0%')


class AppointmentOverviewRowSerializer(DataclassSerializer):
    quantity_rel = report_fields.ReportsPercentageField()
    appo_duration = report_fields.ReportsRelativeDeltaField()
    appo_value = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    tips = report_fields.ReportsCurrencyField()
    commission = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    total_revenue_no_tips = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = AppointmentsOverviewTable.Row
        fields = [*map(attrgetter('name'), dataclasses.fields(AppointmentsOverviewTable.Row))]


@add_header_tooltip(
    discount=_('Total value of all applied discounts (individual discounts and promotions).'),
    revenue_net=_('Total revenue without taxes.'),
)
class CompletedAppointmentsByCategoryNService(BoldLastRowMixin, ToolTipReportTable):
    header = (
        _('Category'),
        _('Service name'),
        _('Total number of services'),
        # xgettext:no-python-format
        _('Total duration of services (HH:MM)'),
        _('Total service value'),
        _('Net revenue'),
        _('Discount'),
        _('Tax'),
        _('Tips'),
        _('Commissions'),
        _('Total revenue'),
        _('Total revenue without tips'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        booking_service_category: str
        booking_service_name: str
        number_of_services: int
        booking_duration: relativedelta
        booking_value: Decimal = 0.0
        revenue_net: Decimal = 0.0
        discount: Decimal = 0.0
        tax: Decimal = 0.0
        tips: Decimal = 0.0
        commission: Decimal = 0.0
        total_revenue: Decimal = 0.0
        total_revenue_no_tips: Decimal = 0.0

        def get_serializer_class(self):
            return CompletedAppointmentsRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'F:N'), NumberFormat.CURRENCY)
        if row.booking_service_category:
            format_font(ws_range(ws, 'A:N'), bold=True)


class CompletedAppointmentsRowSerializer(DataclassSerializer):
    booking_duration = report_fields.ReportsRelativeDeltaField()
    booking_value = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    tips = report_fields.ReportsCurrencyField()
    commission = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    total_revenue_no_tips = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = CompletedAppointmentsByCategoryNService.Row
        fields = [
            *map(
                attrgetter('name'), dataclasses.fields(CompletedAppointmentsByCategoryNService.Row)
            )
        ]


@add_header_tooltip(
    number_of_services=_(
        'Total number of canceled and missed appointments '
        'for which No-Show Protection fees have been charged.'
    ),
    no_show_fee=_(
        'Total revenue from No-Show Protection charges (if No-Show Protection is enabled).'
    ),
)
class NoShowChargesByCategoryNService(BoldLastRowMixin, ToolTipReportTable):
    header = (
        _('Category'),
        _('Service name'),
        _('Number of No-Shows'),
        # xgettext:no-python-format
        _('Revenue from No-Shows'),
    )

    @dataclass
    class Row(DataclassRowSerializer):
        booking_service_category: str
        booking_service_name: str
        number_of_services: int
        no_show_fee: Decimal = 0.0

        def get_serializer_class(self):
            return NoShowAppointmentsRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'E:E'), NumberFormat.CURRENCY)
        if row.booking_service_category:
            format_font(ws_range(ws, 'A:N'), bold=True)


class NoShowAppointmentsRowSerializer(DataclassSerializer):
    no_show_fee = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = NoShowChargesByCategoryNService.Row
        fields = [*map(attrgetter('name'), dataclasses.fields(NoShowChargesByCategoryNService.Row))]


@add_header_tooltip(
    discount=_('Total value of all applied discounts (individual discounts and promotions).'),
    revenue_net=_('Total revenue without taxes.'),
)
class AppointmentsLogTable(ToolTipReportTable):
    enum_col_title = '#'
    header = (
        _('Appointment date'),
        _('Category'),
        _('Service(s)'),
        _('Client name'),
        _('Client type'),
        _('Appointment status'),
        _('Service value'),
        _('Service duration'),
        _('Discount'),
        _('Net revenue'),
        _('Traveling fee'),
        _('Tax $$'),
        _('Tip %'),  # xgettext:no-python-format
        _('Tip $$'),
        _('Commission %'),  # xgettext:no-python-format
        _('Commission $$'),
        _('Total revenue'),
        _('Total revenue without tips'),
        _('Payment method(s)'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        appointment_date: date
        service_category_name: str
        service_name: str
        customer_name: str
        client_type: str
        appointment_status: str
        service_value: Decimal
        service_length: relativedelta
        discount: Decimal
        revenue_net: Decimal
        traveling_fee: Decimal
        tax_amount: Decimal
        tip_rate: int
        tip_amount: Decimal
        commission_rate: int
        commission_amount: Decimal
        total_revenue: Decimal
        total_revenue_wo_tips: Decimal
        payment_methods: str

        def get_serializer_class(self):
            return AppointmentLogRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # Takes into account shift by one (we insert one empty column later)
        format_number(ws_range(ws, 'H:H'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'J:M'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'O:O'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'Q:S'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'N:N'), NumberFormat.PERCENTAGE)
        format_number(ws_range(ws, 'P:P'), NumberFormat.PERCENTAGE)


class AppointmentLogRowSerializer(DataclassSerializer):
    service_length = report_fields.ReportsRelativeDeltaField()
    service_value = report_fields.ReportsCurrencyField()
    commission_rate = report_fields.ReportsPercentageField()
    tip_rate = report_fields.ReportsPercentageField()
    discount = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    traveling_fee = report_fields.ReportsCurrencyField()
    tax_amount = report_fields.ReportsCurrencyField()
    tip_amount = report_fields.ReportsCurrencyField()
    commission_amount = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    total_revenue_wo_tips = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = AppointmentsLogTable.Row
        fields = [*map(attrgetter('name'), dataclasses.fields(AppointmentsLogTable.Row))]


class StafferTimeTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    duration = report_fields.ReportsMinutesField()
    percentage = report_fields.ReportsPercentageField()


class StafferTimeTable(ReportTable):
    header = (
        _('Type'),
        _('Hours'),
        # xgettext:no-python-format
        _('% of working hours'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        name: str
        duration: relativedelta
        percentage: int

        def get_serializer_class(self):
            return StafferTimeTableRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)


class StafferTimeOffTable(ReportTable):
    header = (
        _('Date'),
        _('Reason'),
        # xgettext:no-python-format
        _('Approved'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        date: str
        reason: str
        approved: str


class StafferPlannedTimeOffTable(ReportTable):
    header = (
        _('Date'),
        _('Time'),
        _('Reason'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        date: str
        time: str
        reason: str


@add_header_tooltip(
    revenue=_('Revenue with tips'),
)
class StafferSalesNCommisionsByTypeTable(ToolTipReportTable):
    header = (
        _('Type'),
        _('Revenue'),
        _('Commission'),
        _('Tips'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        type: str
        revenue: Decimal
        commission: Decimal
        tips: Decimal

        def get_serializer_class(self):
            return StafferSalesNCommisionsByTypeRowSerializer

    @staticmethod
    def set_currency(ws: Worksheet):
        format_number(ws_range(ws, 'C:E'), NumberFormat.CURRENCY)

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        self.set_currency(ws)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        self.set_currency(ws)


class StafferSalesNCommisionsByTypeRowSerializer(DataclassSerializer):
    revenue = report_fields.ReportsCurrencyField()
    commission = report_fields.ReportsCurrencyField()
    tips = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = StafferSalesNCommisionsByTypeTable.Row
        fields = [
            *map(attrgetter('name'), dataclasses.fields(StafferSalesNCommisionsByTypeTable.Row))
        ]


@add_header_tooltip(
    revenue_net=_('Total cost of product(s) before tax.'),
    discount=_('Total amount of applied discounts.'),
)
class StafferSalesByProductsTable(ToolTipReportTable):
    header = (
        _('Product category'),
        _('Product name'),
        _('Quantity'),
        _('Price'),
        _('Discount'),
        _('Tax'),
        _('Total Revenue'),
        _('Commission'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        product_category: str
        product_name: str
        quantity: int
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        revenue_gross: Decimal
        commission: Decimal

        def get_serializer_class(self):
            return StafferSalesByProductsRowSerializer

    @staticmethod
    def set_currency(ws: Worksheet):
        format_number(ws_range(ws, 'E:I'), NumberFormat.CURRENCY)

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        self.set_currency(ws)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        self.set_currency(ws)


class StafferSalesByProductsRowSerializer(DataclassSerializer):
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    revenue_gross = report_fields.ReportsCurrencyField()
    commission = report_fields.ReportsCurrencyField()

    class Meta:
        dataclass = StafferSalesByProductsTable.Row
        fields = [*map(attrgetter('name'), dataclasses.fields(StafferSalesByProductsTable.Row))]


@add_header_tooltip(
    overall_clients=_('Total number of unique clients and walk-ins.'),
    new_clients=_('Clients who have scheduled their first appointment with this business.'),
    new_clients_rate=_(
        'The ratio of first-time clients to all clients who have scheduled an appointment.'
    ),
    returning_clients=_(
        'Total number of clients who have had at least one '
        'appointment with this business (also with other staff members).'
    ),
    returning_clients_rate=_(
        'The ratio of returning clients to all clients who have scheduled an appointment.'
    ),
    reviews=_(
        'Total number of reviews added for appointments '
        'which took place over the selected time period.'
    ),
    avg_rating=_(
        'Average rating of reviews added for appointments '
        'which took place over the selected time period.'
    ),
)
class StafferPerformanceClientsNReviewsTable(ToolTipReportTable):
    header = (
        _('Completed appointments'),
        _('Total number of clients'),
        _('Number of new clients'),
        _('% of new clients'),
        _('Number of returning clients'),
        _('% of returning clients'),
        _('Number of walk-in clients'),
        _('% of walk-in clients'),
        _('Number of Clients Who Requested the Staff Member'),
        _('% of clients who requested the staff member'),
        _('Number of reviews'),
        _('Average rating'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        completed_apps: int
        overall_clients: int
        new_clients: int
        new_clients_rate: Decimal
        returning_clients: int
        returning_clients_rate: Decimal
        walk_in_clients: int
        walk_in_clients_rate: Decimal
        clients_requested_staffer: int
        clients_requested_staffer_rate: Decimal
        reviews: int
        avg_rating: Decimal

        def get_serializer_class(self):
            return StafferClientsNReviewsRowSerializer

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'E:E'), NumberFormat.PERCENTAGE)
        format_number(ws_range(ws, 'G:G'), NumberFormat.PERCENTAGE)
        format_number(ws_range(ws, 'I:I'), NumberFormat.PERCENTAGE)
        format_number(ws_range(ws, 'K:K'), NumberFormat.PERCENTAGE)


class StafferClientsNReviewsRowSerializer(DataclassSerializer):
    new_clients_rate = report_fields.ReportsPercentageField()
    returning_clients_rate = report_fields.ReportsPercentageField()
    clients_requested_staffer_rate = report_fields.ReportsPercentageField()
    walk_in_clients_rate = report_fields.ReportsPercentageField()

    class Meta:
        dataclass = StafferPerformanceClientsNReviewsTable.Row
        fields = [
            *map(attrgetter('name'), dataclasses.fields(StafferPerformanceClientsNReviewsTable.Row))
        ]


@add_header_tooltip(
    rate=_('Review rate out of the maximum of 5 stars.'),
)
class StafferPerformanceDetailedReviewsTable(ToolTipReportTable):
    header = (
        _('Client name'),
        _('Service(s)'),
        _('Rating'),
        _('Review'),
    )

    @dataclass
    class Row(DataclassRowSerializer):  # pylint: disable=too-many-instance-attributes
        client_name: str
        service_name: str
        rate: ReviewRating
        review_text: LongText

        def get_serializer_class(self):
            return StafferPerformanceDetailedReviewsRowSerializer


class StafferPerformanceDetailedReviewsRowSerializer(DataclassSerializer):
    rate = DefaultDecimalField()
    review_text = rest_framework.fields.CharField()

    class Meta:
        dataclass = StafferPerformanceDetailedReviewsTable.Row
        fields = [
            *map(attrgetter('name'), dataclasses.fields(StafferPerformanceDetailedReviewsTable.Row))
        ]
