# pylint: disable=duplicate-code

import typing
from collections import defaultdict
from dataclasses import dataclass
from decimal import Decimal

from babel.dates import get_day_names
from dateutil.relativedelta import relativedelta
import pandas as pd
from django.contrib.postgres.fields.ranges import DateTimeTZRange
from django.db.models import (
    Count,
    DecimalField,
    F,
    OuterRef,
    QuerySet,
    Sum,
    Value,
)
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import (
    Cast,
    Coalesce,
    ExtractHour,
    ExtractWeekDay,
)
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from lib.db import using_report_db
from lib.ranges import merge_ranges
from lib.tools import get_locale_from_language
from webapps.booking.enums import AppointmentStatus, AppointmentType
from webapps.booking.models import Appointment
from webapps.pos.enums import receipt_status
from webapps.pos.models import PaymentType
from webapps.schedule.ports import get_working_hours_work_durations
from webapps.stats_and_reports.consts import DjangoExtractDOW
from webapps.stats_and_reports.models import (
    AppointmentReplica,
    PaymentRowReplica,
    ResourceReplica,
    ServiceAddOnUseReplica,
    SubBookingReplica,
)
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_alignment,
    format_number,
    NumberFormat,
    ws_range,
)
from webapps.stats_and_reports.reports.utils import (
    generate_time_slots,
    get_business_tzinfo,
    percentage,
    relativedelta_to_minutes,
    TimeSlot,
)


class AppointmentsSummaryTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    appointments_count = serializers.IntegerField()
    percentage = report_fields.ReportsPercentageField()
    appointments_duration = report_fields.ReportsRelativeDeltaField()
    appointments_value = report_fields.ReportsCurrencyField()
    addons_value = report_fields.ReportsCurrencyField()
    revenue = report_fields.ReportsCurrencyField()
    _meta = serializers.DictField()


class AppointmentsSummaryTable(ReportTable):
    header = (
        '',
        _('Quantity'),
        '%',
        _('Duration'),
        _('Service Value'),
        _('Add-ons Value'),
        _('Revenue'),
    )

    @dataclass
    class Row(ReportTableRow):
        name: str
        appointments_count: int
        percentage: int
        appointments_duration: relativedelta
        appointments_value: Decimal  # "Service Value"
        addons_value: Decimal
        revenue: Decimal
        _meta: typing.Dict

        def get_serializer_class(self) -> typing.Optional[typing.Callable]:
            return AppointmentsSummaryTableRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'C:H'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_number(ws_range(ws, 'C:C'), NumberFormat.GENERAL)
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)
        format_alignment(ws_range(ws, 'E:E'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'F:G'), NumberFormat.CURRENCY)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'C:C'), NumberFormat.GENERAL)
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)
        format_alignment(ws_range(ws, 'E:E'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'F:H'), NumberFormat.CURRENCY)


class AppointmentsSummarySection(ReportSection):
    key = 'appointments_summary_section'

    def appointments_qs(self) -> QuerySet:
        return AppointmentReplica.objects.filter(
            business_id=self.scope.business.id,
            booked_from__gte=self.scope.date_from_datetime_utc,
            booked_till__lte=self.scope.date_till_by_span,
            type__in=AppointmentReplica.TYPES_BOOKABLE,
            status__in=[
                AppointmentStatus.ACCEPTED,  # "Incomplete appointments"
                AppointmentStatus.CANCELED,
                AppointmentStatus.FINISHED,
                AppointmentStatus.NOSHOW,
            ],
        )

    def get_queryset(self) -> QuerySet:
        return (
            self.appointments_qs()
            .values(
                'status',
            )
            .annotate(
                appointments_count=Count('id'),
                appointments_duration=Sum(F('booked_till') - F('booked_from')),
                appointments_value=Sum(
                    SubBookingReplica.objects.filter(
                        appointment_id=OuterRef('id'),
                        service_variant__service__combo_type__isnull=True,
                    )
                    .values('appointment_id')
                    .annotate(
                        booking_values=Sum(
                            Cast(
                                KeyTextTransform('service_variant_price', 'service_data_internal'),
                                DecimalField(max_digits=10, decimal_places=2),
                            )
                        ),
                    )
                    .values('booking_values')[:1],
                    default=Decimal(0),
                    output_field=DecimalField(max_digits=10, decimal_places=2),
                ),
                addons_value=Sum(
                    ServiceAddOnUseReplica.objects.filter(
                        subbooking__appointment_id=OuterRef('id'),
                        subbooking__deleted__isnull=True,
                    )
                    .values('subbooking__appointment_id')
                    .annotate(
                        addon_values=Sum(F('price') * F('quantity')),
                    )
                    .values('addon_values')[:1],
                    default=Decimal(0),
                    output_field=DecimalField(max_digits=10, decimal_places=2),
                ),
            )
        )

    def get_appointments_revenue_stats(self) -> dict:
        appointment_ids = list(self.appointments_qs().values_list('id', flat=True))

        # We use PaymentRow as source of truth as we need to filter out payments
        # made with vouchers (it's impossible to distinguish them at Transaction
        # level). We can use PaymentRow as this report does not distinguish net,
        # gross and tax amounts.
        codes = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        payment_rows_qs = (
            PaymentRowReplica.objects.filter(
                receipt__transaction__appointment_id__in=appointment_ids,
                receipt__transaction__children__isnull=True,
                receipt__transaction__latest_receipt__status_code__in=codes,
            )
            .exclude(
                payment_type__code__in=PaymentType.VOUCHER_TYPES,
            )
            .annotate(
                appointment_status=F('receipt__transaction__appointment__status'),
            )
        )
        revenue_stats = (
            payment_rows_qs.values(
                'appointment_status',
            )
            .annotate(
                total_revenue=Coalesce(Sum('amount'), Decimal(0)),
            )
            .order_by()
        )

        return {entry['appointment_status']: entry for entry in revenue_stats}

    def coerce_data_to_table(self, results) -> AppointmentsSummaryTable:
        stats = defaultdict(dict)
        revenue_stats = self.get_appointments_revenue_stats()
        for row in results:
            appointment_status = row['status']
            stats[appointment_status] = row
            stats[appointment_status]['revenue'] = revenue_stats.get(appointment_status, {}).get(
                'total_revenue'
            ) or Decimal(0)

        total_appointments_count = sum(entry['appointments_count'] for entry in stats.values())

        # We can calculate percentage when we know total appointments number
        for entry in stats.values():
            entry['percentage'] = percentage(entry['appointments_count'], total_appointments_count)

        rows = []
        statuses = {
            AppointmentStatus.ACCEPTED: gettext('Incomplete'),
            AppointmentStatus.FINISHED: gettext('Completed'),
            AppointmentStatus.NOSHOW: gettext('No-Shows'),
            AppointmentStatus.CANCELED: gettext('Canceled'),
        }
        for status, label in statuses.items():
            status_stats = stats[status]
            rows.append(
                AppointmentsSummaryTable.Row(
                    name=label,
                    appointments_count=status_stats.get('appointments_count') or 0,
                    percentage=status_stats.get('percentage') or 0,
                    appointments_duration=status_stats.get(
                        'appointments_duration', relativedelta()
                    ),
                    appointments_value=status_stats.get('appointments_value') or Decimal(0),
                    addons_value=status_stats.get('addons_value') or Decimal(0),
                    revenue=status_stats.get('revenue') or Decimal(0),
                    _meta={
                        'key': status,
                        '_key_choices': list(statuses.keys()),
                    },
                )
            )
        return AppointmentsSummaryTable(rows=rows)


class HighlightsTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    appointments_count = serializers.IntegerField()
    percentage = report_fields.ReportsPercentageField()
    appointments_value = report_fields.ReportsCurrencyField()
    _meta = serializers.DictField()


class HighlightsTable(ReportTable):
    header = (
        _('Appointment status'),
        _('Quantity'),
        _('Percent'),
        _('Value'),
    )

    @dataclass
    class Row(ReportTableRow):
        name: str
        appointments_count: int
        percentage: int
        appointments_value: Decimal
        _meta: typing.Dict

        def get_serializer_class(self) -> typing.Optional[typing.Callable]:
            return HighlightsTableRowSerializer


class HighlightsSection(AppointmentsSummarySection):
    """In appointments dashboard this section is named differently"""

    title = _('Highlights')

    def coerce_data_to_table(self, results) -> HighlightsTable:
        stats = defaultdict(dict)
        for row in results:
            appointment_status = row['status']
            stats[appointment_status] = row

        total_appointments_count = 0
        for entry in stats.values():
            total_appointments_count += entry['appointments_count']

        # We can calculate percentage when we know total appointments number
        for entry in stats.values():
            entry['percentage'] = percentage(entry['appointments_count'], total_appointments_count)

        rows = []
        statuses = {
            AppointmentStatus.ACCEPTED: gettext('Incomplete'),
            AppointmentStatus.FINISHED: gettext('Completed / Finished'),
            AppointmentStatus.NOSHOW: gettext('No-shows'),
            AppointmentStatus.CANCELED: gettext('Canceled'),
        }
        for status, label in statuses.items():
            status_stats = stats[status]
            rows.append(
                HighlightsTable.Row(
                    name=label,
                    appointments_count=status_stats.get('appointments_count', 0),
                    percentage=status_stats.get('percentage', 0),
                    appointments_value=status_stats.get('appointments_value', Decimal(0)),
                    _meta={
                        'key': status,
                        '_key_choices': list(statuses.keys()),
                    },
                )
            )
        return HighlightsTable(
            rows=rows,
        )


class BestDaysTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    duration = report_fields.ReportsRelativeDeltaField()
    percentage = report_fields.ReportsPercentageField()


class BestDaysTable(ReportTable):
    header = ('', _('Duration'), '%')

    @dataclass
    class Row(ReportTableRow):
        name: str
        duration: relativedelta
        percentage: int

        def get_serializer_class(self):
            return BestDaysTableRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'C:D'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'C:C'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_alignment(ws_range(ws, 'C:C'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)


class BestDaysSection(ReportSection):
    key = 'best_days'
    title = _('Best Days')
    description = _('Best performing days based on your completed appointments')
    append_total_row = True

    def get_queryset(self):
        business_tz = get_business_tzinfo(self.scope.business, self.scope.date_from_datetime_utc)
        return (
            AppointmentReplica.objects.filter(
                business_id=self.scope.business.id,
                booked_from__gte=self.scope.date_from_datetime_utc,
                booked_till__lte=self.scope.date_till_by_span,
                type__in=AppointmentReplica.TYPES_BOOKABLE,
                status=AppointmentStatus.FINISHED,
            )
            .annotate(
                weekday=ExtractWeekDay('booked_from', tzinfo=business_tz),
            )
            .values(
                'weekday',
            )
            .annotate(
                appointments_duration=Sum(F('booked_till') - F('booked_from')),
            )
            .order_by('weekday')
        )

    def _dow_name(self, django_dow: int) -> str:
        """Beware: Django ExtractWeekDay uses different day numbering than
        Babel Dates library.
        Django: 1 - Sunday ... 7 - Saturday
        Babel:  0 - Monday ... 6 - Sunday
        See:
            http://babel.pocoo.org/en/latest/api/dates.html#data-access
            https://docs.djangoproject.com/en/3.1/ref/models/database-functions/#extract  # pylint: disable=line-too-long
        """
        babel_dow = (django_dow - 2) % 7
        locale = get_locale_from_language(self.scope.language)
        return get_day_names(locale=locale)[babel_dow].capitalize()

    def coerce_data_to_table(self, results) -> BestDaysTable:
        total_duration = relativedelta()
        weekday_duration = {}
        for row in results:
            total_duration += row['appointments_duration']
            weekday_duration[row['weekday']] = row['appointments_duration']

        rows = [
            BestDaysTable.Row(
                name=self._dow_name(wd),
                duration=weekday_duration.get(wd, relativedelta()),
                percentage=percentage(
                    relativedelta_to_minutes(weekday_duration.get(wd, relativedelta())),
                    relativedelta_to_minutes(total_duration),
                ),
            )
            for wd in DjangoExtractDOW.week_days()
        ]
        return BestDaysTable(
            rows=rows,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        aggregated_results = queryset.aggregate(total_duration=Sum('appointments_duration'))

        return BestDaysTable.Row(
            name=gettext('Total'),
            duration=aggregated_results['total_duration'],
            percentage=100,
        )


class BestHoursTableRowSerializer(serializers.Serializer):
    time_slot = report_fields.ReportsTimeSlotField()
    appointments_count = serializers.IntegerField()
    duration = report_fields.ReportsRelativeDeltaField()


class BestHoursTableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    appointments_count = serializers.IntegerField()
    duration = report_fields.ReportsRelativeDeltaField()


class BestHoursTable(ReportTable):
    header = (_('Time'), _('No. of appointments'), _('Total length'))

    @dataclass
    class Row(ReportTableRow):
        time_slot: TimeSlot
        appointments_count: int
        duration: relativedelta

        def get_serializer_class(self) -> typing.Optional[typing.Callable]:
            return BestHoursTableRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'C:D'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_number(ws_range(ws, 'C:C'), NumberFormat.GENERAL)
        format_alignment(ws_range(ws, 'D:D'), Alignment(horizontal='right'))

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'C:C'), NumberFormat.GENERAL)
        format_alignment(ws_range(ws, 'D:D'), Alignment(horizontal='right'))

    @dataclass
    class TotalRow(ReportTableRow):
        appointments_count: int
        duration: relativedelta

        def get_serializer_class(self) -> typing.Optional[typing.Callable]:
            return BestHoursTableTotalRowSerializer


class BestHoursSection(ReportSection):
    key = 'best_hours'
    title = _('Best Hours')
    description = _('Best performing hours based on your completed appointments')
    append_total_row = True

    FIRST_TIME_SLOT = 8  # 8:00 - 9:00
    LAST_TIME_SLOT = 21  # 21:00 - 22:00

    @property
    def time_slots(self) -> typing.List[TimeSlot]:
        return generate_time_slots(self.FIRST_TIME_SLOT, self.LAST_TIME_SLOT)

    def get_queryset(self):
        business_tz = get_business_tzinfo(self.scope.business, self.scope.date_from_datetime_utc)
        return (
            AppointmentReplica.objects.filter(
                business_id=self.scope.business.id,
                booked_from__gte=self.scope.date_from_datetime_utc,
                booked_till__lte=self.scope.date_till_by_span,
                type__in=Appointment.TYPES_BOOKABLE,
                status=Appointment.STATUS.FINISHED,
            )
            .annotate(
                hour=ExtractHour('booked_from', tzinfo=business_tz),
            )
            .values(
                'hour',
            )
            .annotate(
                appointments_count=Count('id', distinct=True),
                appointments_duration=Sum(F('booked_till') - F('booked_from')),
            )
            .order_by('hour')
        )

    def coerce_data_to_table(self, results) -> BestHoursTable:
        total_duration = relativedelta()
        total_appointments_count = 0
        hour_duration = {}
        hour_appointments_count = {}
        for row in results:
            total_duration += row['appointments_duration']
            hour_duration[row['hour']] = row['appointments_duration']
            hour_appointments_count[row['hour']] = row['appointments_count']
            total_appointments_count += row['appointments_count']
        rows = [
            BestHoursTable.Row(
                time_slot=slot,
                appointments_count=hour_appointments_count.get(slot.begin.hour, 0),
                duration=hour_duration.get(slot.begin.hour, relativedelta()),
            )
            for slot in self.time_slots
        ]
        return BestHoursTable(
            rows=rows,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        aggregated_qs = queryset.aggregate(
            total_appointments_count=Sum('appointments_count'),
            total_duration=Sum('appointments_duration'),
        )

        return BestHoursTable.TotalRow(
            appointments_count=aggregated_qs['total_appointments_count'],
            duration=aggregated_qs['total_duration'],
        )


class TimeOccupancyTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    duration = report_fields.ReportsMinutesField()
    percentage = report_fields.ReportsPercentageField()


class TimeOccupancyTable(ReportTable):
    header = (_('Item type'), _('Hours'), '%')

    @dataclass
    class Row(ReportTableRow):
        name: str
        duration: int
        percentage: int

        def get_serializer_class(self) -> typing.Optional[typing.Callable]:
            return TimeOccupancyTableRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'C:D'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'C:C'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'D:D'), NumberFormat.PERCENTAGE)


class TimeOccupancySection(ReportSection):
    key = 'time_occupancy'
    title = _('Time occupancy')
    description = _(
        'Shows the breakdown of used time in your appointments '
        'calendar, breaking down Time reservations, Booked and '
        'unbooked hours'
    )

    WORKING_HOURS = _('Working hours')
    BOOKED_HOURS = _('Booked hours')
    UNBOOKED_HOURS = _('Unbooked hours')
    TIME_BLOCKED = _('Time blocks')

    def get_queryset(self):
        appointment_types = [
            AppointmentType.BUSINESS,
            AppointmentType.CUSTOMER,
            AppointmentType.RESERVATION,
        ]
        return (
            SubBookingReplica.objects.annotate_is_combo()
            .filter(
                appointment__business_id=self.scope.business.id,
                padded_booked_range__overlap=DateTimeTZRange(
                    self.scope.date_from_datetime_utc,
                    self.scope.date_till_by_span,
                    bounds='[]',
                ),
                appointment__type__in=appointment_types,
                appointment__status=AppointmentStatus.FINISHED,
                resources__type=ResourceReplica.STAFF,
                is_combo=False,
            )
            .values_list('appointment__type', 'padded_booked_range', 'resources')
        )

    def _get_resources_qs(self):
        return ResourceReplica.objects.filter(
            business=self.scope.business,
            type=ResourceReplica.STAFF,
            active=True,
        )

    def _get_report_table_class(self):
        return TimeOccupancyTable

    def _extract_data_to_table(self, coerced_data):
        report_table_cls = self._get_report_table_class()
        return report_table_cls(rows=[report_table_cls.Row(**data) for data in coerced_data])

    def coerce_data_to_table(self, results) -> TimeOccupancyTable:
        bookings_df = pd.DataFrame.from_records(
            results,
            columns=['type', 'booked_range', 'resource_id'],
        ).set_index(['resource_id', 'type'])
        booked = bookings_df.booked_range.apply(lambda x: (x.lower, x.upper))
        # we must merge booked_ranges before calculating duration
        # because staffer can have parallel services
        merged = booked.groupby(['type', 'resource_id']).apply(lambda x: merge_ranges(sorted(x)))
        if not merged.empty:
            merged = merged.explode().apply(lambda x: x[1] - x[0]).groupby('type').sum()

        booked_duration = int(
            (
                merged.get(AppointmentType.BUSINESS, pd.Timedelta(0))
                + merged.get(AppointmentType.CUSTOMER, pd.Timedelta(0))
            ).total_seconds()
            / 60
        )

        time_blocks_duration = int(
            merged.get(AppointmentType.RESERVATION, pd.Timedelta(0)).total_seconds() / 60
        )

        resource_ids = list(self._get_resources_qs().values_list('id', flat=True))

        with using_report_db():
            working_durations = get_working_hours_work_durations(
                business_id=self.scope.business.id,
                resource_ids=resource_ids,
                start_date=self.scope.date_from,
                end_date=self.scope.date_till,
            )
        total_duration = sum(working_durations.values())
        unbooked_duration = total_duration - (booked_duration + time_blocks_duration)
        # pylint: disable=use-dict-literal
        rows = [
            dict(
                name=self.WORKING_HOURS,
                duration=total_duration,
                percentage=100,
            ),
            dict(
                name=self.BOOKED_HOURS,
                duration=booked_duration,
                percentage=percentage(booked_duration, total_duration),
            ),
            dict(
                name=self.UNBOOKED_HOURS,
                duration=unbooked_duration,
                percentage=percentage(unbooked_duration, total_duration),
            ),
            dict(
                name=self.TIME_BLOCKED,
                duration=time_blocks_duration,
                percentage=percentage(time_blocks_duration, total_duration),
            ),
        ]
        return self._extract_data_to_table(rows)
        # pylint: enable=use-dict-literal


class TopServicesTableRowSerializer(serializers.Serializer):
    service_name = serializers.CharField()
    appointments_count = serializers.IntegerField()
    appointments_value = report_fields.ReportsCurrencyField()


class TopServicesTable(ReportTable):
    header = (_('Service'), _('Appointments'), _('Value'))

    @dataclass
    class Row(ReportTableRow):
        service_name: str
        appointments_count: int
        appointments_value: Decimal

        def get_serializer_class(self):
            return TopServicesTableRowSerializer


class TopServicesSection(ReportSection):
    key = 'top_services'
    title = _('Top 10 services')
    description = _('Ten most popular services by booking value')

    def get_queryset(self) -> QuerySet:
        return (
            SubBookingReplica.objects.annotate_is_combo()
            .filter(
                appointment__business_id=self.scope.business.id,
                booked_from__gte=self.scope.date_from_datetime_utc,
                booked_till__lte=self.scope.date_till_by_span,
                appointment__type__in=AppointmentReplica.TYPES_BOOKABLE,
                appointment__status=AppointmentStatus.FINISHED,
                service_variant__isnull=False,
                is_combo=False,
            )
            .values(
                'service_variant__service__name',
            )
            .annotate(
                appointments_count=Count('appointment_id', distinct=True),
                appointments_value=Coalesce(
                    Sum(
                        Cast(
                            KeyTextTransform('service_variant_price', 'service_data_internal'),
                            DecimalField(max_digits=10, decimal_places=2),
                        )
                    ),
                    Value(Decimal('0')),
                ),
            )
            .order_by('-appointments_count')[:10]
        )

    def coerce_data_to_table(self, results) -> TopServicesTable:
        rows = [
            TopServicesTable.Row(
                service_name=row['service_variant__service__name'],
                appointments_count=row['appointments_count'],
                appointments_value=row['appointments_value'],
            )
            for row in results
        ]
        return TopServicesTable(rows=rows)


class TopAddonsTableRowSerializer(serializers.Serializer):
    name = serializers.CharField()
    appointments_count = serializers.IntegerField()
    value = report_fields.ReportsCurrencyField()


class TopAddonsTable(ReportTable):
    header = (_('Add-on'), _('Appointments'), _('Value'))

    @dataclass
    class Row(ReportTableRow):
        name: str
        appointments_count: int
        value: Decimal

        def get_serializer_class(self):
            return TopAddonsTableRowSerializer


class TopAddonsSection(ReportSection):
    key = 'top_addons'
    title = _('Top 10 Add-ons')
    description = _('Ten most popular service Add-ons by value')

    def get_queryset(self) -> QuerySet:
        return (
            SubBookingReplica.objects.filter(
                appointment__business_id=self.scope.business.id,
                booked_from__gte=self.scope.date_from_datetime_utc,
                booked_till__lte=self.scope.date_till_by_span,
                appointment__type__in=AppointmentReplica.TYPES_BOOKABLE,
                appointment__status=AppointmentStatus.FINISHED,
                addons_set__isnull=False,
            )
            .values(
                'addons_set__name',
            )
            .annotate(
                appointments_count=Count('appointment_id', distinct=True),
                value=Coalesce(Sum(F('addons_set__price') * F('addons_set__quantity')), Decimal(0)),
            )
            .order_by('-value')[:10]
        )

    def coerce_data_to_table(self, results) -> TopAddonsTable:
        return TopAddonsTable(
            rows=[
                TopAddonsTable.Row(
                    name=row['addons_set__name'],
                    appointments_count=row['appointments_count'],
                    value=row['value'],
                )
                for row in results
            ]
        )


class AppointmentsSummary(BaseReport):
    key = ReportKeys.AppointmentsSummary

    @staticmethod
    def get_title() -> str:
        return gettext('Appointments summary')

    @property
    def subtitle(self) -> str:
        return gettext(
            'Number of appointments, associated details, and revenue by ' 'appointment status'
        )

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(AppointmentsSummarySection)
        self._add_section(TimeOccupancySection)
        self._add_section(BestDaysSection)
        self._add_section(BestHoursSection)

    def get_column_widths(self) -> typing.List[int]:
        return [5, 25, 10, 10, 8, 15, 10]
