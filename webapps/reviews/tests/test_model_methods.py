import secrets
import string

from django.test import TestCase
from model_bakery import baker

from lib.test_utils import (
    create_service_with_variant,
    create_staffer,
    create_subbooking,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Business, Resource
from webapps.reviews.models import Review


class TestReviewModelMethods(TestCase):
    @classmethod
    def setUpTestData(cls):
        alphabet = string.ascii_letters
        cls.service_name = ''.join(secrets.choice(alphabet) for i in range(8))

        cls.business = baker.make(Business)
        service, cls.sv = create_service_with_variant(cls.business)
        cls.staffer = create_staffer(cls.business, service=service)
        cls.appliance = baker.make(
            Resource,
            business=cls.business,
            type=Resource.APPLIANCE,
        )
        cls.appliance.add_services([service.id])
        cls.sbk, *_ = create_subbooking(
            business=cls.business,
        )
        cls.sbk_sv_staff, *_ = create_subbooking(
            business=cls.business,
            booking_kws=dict(
                service_variant=cls.sv,
            ),
        )
        cls.sbk_sv_staff.resources.add(cls.staffer, cls.appliance)
        cls.sbk_service_name, *_ = create_subbooking(
            business=cls.business,
            booking_kws=dict(
                service_name=cls.service_name,
            ),
        )

        cls.mb_sbk_1, cls.mb_sbk_2 = create_appointment(
            [
                dict(service_variant=cls.sv, staffer=cls.staffer),
                dict(service_name=cls.service_name, staffer=cls.staffer),
            ],
            business=cls.business,
        ).subbookings

    def test_get_staff_and_services__service_variant(self):
        expected_services = [
            {
                'id': None,
                'name': None,
                'treatment_id': None,
            }
        ]
        expected_staff = []
        staff, services = Review.get_staff_and_services(self.sbk)
        self.assertListEqual(expected_services, services)
        self.assertListEqual(expected_staff, staff)

    def test_get_staff_and_services__service_variant_and_staffer(self):
        expected_services = [
            {
                'id': self.sv.service.id,
                'name': self.sv.service.name,
                'treatment_id': self.sv.service.treatment_id,
            }
        ]
        expected_staff = [
            {
                'id': self.staffer.id,
                'name': self.staffer.name,
            },
        ]
        staff, services = Review.get_staff_and_services(self.sbk_sv_staff)
        self.assertListEqual(expected_services, services)
        self.assertListEqual(expected_staff, staff)

    def test_get_staff_and_services__service_name(self):
        expected_services = [
            {
                'id': None,
                'name': self.service_name,
                'treatment_id': None,
            },
        ]
        expected_staff = []
        staff, services = Review.get_staff_and_services(self.sbk_service_name)
        self.assertListEqual(expected_services, services)
        self.assertListEqual(expected_staff, staff)

    def test_get_staff_and_services__multibooking(self):
        expected_services = [
            {
                'id': self.sv.service.id,
                'name': self.sv.service.name,
                'treatment_id': self.sv.service.treatment_id,
            },
            {
                'id': None,
                'name': self.service_name,
                'treatment_id': None,
            },
        ]
        expected_staff = [
            {
                'id': self.staffer.id,
                'name': self.staffer.name,
            },
        ]
        staff, services = Review.get_staff_and_services(self.mb_sbk_1)
        self.assertCountEqual(expected_services, services)
        self.assertListEqual(expected_staff, staff)

        staff, services = Review.get_staff_and_services(self.mb_sbk_2)
        self.assertCountEqual(expected_services, services)
        self.assertListEqual(expected_staff, staff)
