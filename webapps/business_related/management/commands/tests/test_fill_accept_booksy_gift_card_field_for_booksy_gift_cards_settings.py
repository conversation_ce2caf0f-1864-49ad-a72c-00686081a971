from itertools import cycle

import mock
import pytest
from model_bakery import baker

from webapps.business.models import Business, BusinessCategory
from webapps.business_related.management.commands.fill_accept_booksy_gift_cards_field import Command
from webapps.business_related.models import BooksyGiftCardsSettings
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import PaymentType, POS


@mock.patch('webapps.business_related.utils._is_kyced_and_active')
@pytest.mark.django_db
def test_test_script__fill_accept_booksy_gift_cards_field(_is_kyced_and_active):
    barbers_category = baker.make(
        BusinessCategory,
        internal_name='Barbers',
        enable_booksy_gift_cards=True,
    )
    car_wash_category = baker.make(
        BusinessCategory,
        internal_name='Car Wash',
        enable_booksy_gift_cards=False,
    )
    businesses = baker.make(
        Business,
        primary_category=cycle((barbers_category, car_wash_category, barbers_category)),
        _quantity=3,
    )
    for business in businesses:
        baker.make(POS, business=business)

    def _is_kyced_and_active_mock(business_id):
        if business_id == businesses[2].id:
            return False
        return True

    _is_kyced_and_active.side_effect = _is_kyced_and_active_mock
    BooksyGiftCardsSettings.objects.update(accept_booksy_gift_cards=False)
    assert not BooksyGiftCardsSettings.objects.filter(accept_booksy_gift_cards=True)
    Command().handle()
    assert BooksyGiftCardsSettings.objects.filter(accept_booksy_gift_cards=True).count() == 1
    assert (
        PaymentType.objects.filter(
            code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
        ).count()
        == 1
    )
