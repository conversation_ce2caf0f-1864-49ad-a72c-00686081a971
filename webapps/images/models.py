import logging

from django.contrib.postgres.fields import <PERSON>rray<PERSON>ield
from django.db import models, transaction
from django.db.models import (
    F,
    Subquery,
)
from django.utils.translation import gettext

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import (
    ESDocMixin,
    delete_document,
    index_document,
    ESDefaultBumpOnSaveDeleteMixin,
    ESDefaultBumpOnCreateUpdateDeleteQuerysetMixin,
)
from lib.fields.image_field import NonUpdatableImageField
from lib.models import (
    ArchiveModel,
    ArchiveManager,
    BaseArchiveManager,
    OptionalSoftDeleteQuerySet,
)
from lib.rivers import River, bump_document
from settings.storage import (
    ImageModelStorage,
    StaticBusinessCategoryImageS3Boto3Storage,
)
from webapps.images import enums
from webapps.images.enums import BusinessCategoryPlaceholderType, ImageTypeEnum
from webapps.images.events import logo_changed_event
from webapps.images.mixins import PhotoHelperMixin
from webapps.images.tools import (
    ImageThumbnailsMixin,
    get_image_business_name,
)
from webapps.user.models import User, UserProfile

log = logging.getLogger('booksy.images')


class ImageQuerySet(
    ESDefaultBumpOnCreateUpdateDeleteQuerysetMixin,
    OptionalSoftDeleteQuerySet,
):
    def cover_photos(self, business_id=None):
        qs = self.filter(is_cover_photo=True)
        if business_id:
            qs = qs.filter(business_id=business_id)
        return qs


class ImageManager(BaseArchiveManager.from_queryset(ImageQuerySet)):
    @transaction.atomic
    def reorder_other_in_favour_of_instance(self, instance):
        if (
            self.select_for_update()
            .filter(business=instance.business, category=instance.category, order=instance.order)
            .exclude(id=instance.id)
            .exists()
        ):
            self.filter(
                business=instance.business, category=instance.category, order__gte=instance.order
            ).exclude(id=instance.id).update(order=F('order') + 1)


class Image(
    ESDefaultBumpOnSaveDeleteMixin,
    ImageThumbnailsMixin,
    ArchiveModel,
    ESDocMixin,
):
    es_doc_type = ESDocType.IMAGE
    river = River.IMAGE

    id = models.AutoField(primary_key=True, db_column='image_id')
    instagram_id = models.BigIntegerField(null=True, blank=True)
    business = models.ForeignKey(
        'business.Business',
        related_name='images',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    cms_content = models.ForeignKey(
        'marketplace.CmsContent',
        related_name='images',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    tags = ArrayField(models.CharField(max_length=50), blank=True)
    staffers = ArrayField(models.CharField(max_length=50), blank=True)
    description = models.TextField(null=True, blank=True, verbose_name='Note')
    category = models.CharField(
        max_length=50,
        null=False,
        blank=False,
        choices=enums.IMAGE_CATEGORY_CHOICES,
        default=ImageTypeEnum.BIZ_PHOTO,
    )
    is_cover_photo = models.BooleanField(default=False)
    inspiration_categories = models.ManyToManyField(
        'business.BusinessCategory',
        related_name='inspirations',
        blank=True,
    )
    order = models.IntegerField(default=0, editable=True)
    image = NonUpdatableImageField(
        blank=True,
        null=True,
        storage=ImageModelStorage(),
        upload_to=get_image_business_name,
        height_field='height',
        width_field='width',
    )
    image_url = models.URLField(blank=True, null=True)
    active = models.BooleanField(default=True, blank=False, editable=False)
    visible = models.BooleanField(default=True, blank=False, editable=True)
    height = models.IntegerField(default=0)
    width = models.IntegerField(default=0)

    import_uid = models.CharField(max_length=64, null=True, blank=True)

    objects = ImageManager()
    all_objects = models.Manager()

    def __str__(self):
        return f'<Image id:{self.id}>'

    def to_es(self, no_thumbs=False):
        doc = {
            '_id': self.id,
            '_parent': self.business_id,
            'routing': self.business_id,
            'tags': self.tags or [],
            'staffers': self.staffers or [],
            'description': self.description,
            'image': self.full_url,
            'height': self.height,
            'width': self.width,
            'category': self.category,
            'is_cover_photo': self.is_cover_photo,
            'inspiration_categories': [x.id for x in self.inspiration_categories.all()],
            'order': self.order,
            'visible': self.visible,
            'location': (
                {'lat': self.business.latitude, 'lon': self.business.longitude}
                if self.business
                and self.business.latitude is not None
                and self.business.longitude is not None
                else None
            ),
            'active': self.active,
            'created': self.created.strftime("%Y-%m-%d %H:%M:%S"),
        }
        if not no_thumbs:
            doc['thumbnails'] = self.get_thumbnails()
        return doc

    def save(self, *args, **kwargs):
        tags = list(set(self.tags or []))
        new_list = []
        for tag in tags:
            new_list.append(self.prepare_tag(tag))
        self.tags = list(set(new_list))
        staffers = list(set(self.staffers or []))
        new_list = []
        for staffer in staffers:
            new_list.append(self.prepare_tag(staffer))
        self.staffers = list(set(new_list))
        super().save(*args, **kwargs)
        if self.business:
            self.business.maybe_check_boost_visibility()
            if self.deleted:
                logo_changed_event.send(self.business)
        if self.category == ImageTypeEnum.LOGO:
            reindex_business_image_comments(self.business_id)

    def delete(self, using=None, keep_parents=False):
        delete_return = super().delete(using, keep_parents)
        if self.category == ImageTypeEnum.LOGO:
            reindex_business_image_comments(self.business_id)
            logo_changed_event.send(self.business)
        return delete_return

    @staticmethod
    def prepare_tag(value):
        val = ''.join(c if c.isalnum() else '' for c in value).replace(' ', '')
        return val[:50]

    def reorder_before(self, reference_image_id: int, refresh_order: bool = False) -> None:
        """Note that we only reorder images from the same category"""
        reference_image_qs = Image.objects.filter(
            id=reference_image_id,
            business=self.business,
            category=self.category,
        )
        if not reference_image_qs.exists():
            raise ValueError(gettext('Invalid reference image for reordering'))

        images_to_be_moved = Image.objects.filter(
            business=self.business,
            category=self.category,
            order__gte=Subquery(reference_image_qs.values('order')[:1]),
        ).exclude(
            id=self.id,  # Do not move the image that has just been moved
        )
        with transaction.atomic():
            # Move the image to the same place as selected reference image
            Image.objects.filter(id=self.id).update(
                order=Subquery(reference_image_qs.values('order')[:1]),
                bump_instances_to_es=False,
            )
            # Move all latter ones (from the same category) by one place
            images_to_be_moved.update(order=F('order') + 1, bump_later=True)
        self.reindex_or_bump_river()
        if refresh_order:
            self.refresh_from_db(fields=['order'])

    def reorder_after(self, reference_image_id: int, refresh_order: bool = False) -> None:
        """Note that we only reorder images from the same category"""
        reference_image_qs = Image.objects.filter(
            id=reference_image_id,
            business=self.business,
            category=self.category,
        )
        if not reference_image_qs.exists():
            raise ValueError(gettext('Invalid reference image for reordering'))

        # Make space for the image that is being moved
        images_to_be_moved = Image.objects.filter(
            business=self.business,
            category=self.category,
            order__gt=Subquery(reference_image_qs.values('order')[:1]),
        ).exclude(id=self.id)
        with transaction.atomic():
            # Move all latter ones by one place
            images_to_be_moved.update(order=F('order') + 1, bump_later=True)
            # Move our image after the selected one
            Image.objects.filter(id=self.id).update(
                order=Subquery(
                    reference_image_qs.annotate(
                        new_order=F('order') + 1,
                    ).values(
                        'new_order'
                    )[:1]
                ),
                bump_instances_to_es=False,
            )
        self.reindex_or_bump_river()
        if refresh_order:
            self.refresh_from_db(fields=['order'])

    @classmethod
    def from_validated_data(
        cls,
        business_id: int,
        validated_data: dict,
    ) -> 'Image':
        validated_image = validated_data.pop('image')
        image = cls(
            business_id=business_id,
            height=validated_image.image.height,
            width=validated_image.image.width,
            **validated_data,
        )

        helper = PhotoHelperMixin(image.category, image)
        image.image_url = helper.upload_file_obj(
            bytes_file=validated_image.file,
            extension=validated_image.image.format.lower(),
            business_id=business_id,
        )
        image.save()

        return image


def reindex_business_image_comments(business_id):
    comment_ids = list(
        ImageComment.objects.filter(
            image__business_id=business_id,
            profile_type=UserProfile.Type.BUSINESS,
        ).values_list('id', flat=True)
    )
    bump_document(River.IMAGE_COMMENT, comment_ids)


class ImageComment(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.IMAGE_COMMENT

    id = models.AutoField(primary_key=True, db_column='comment_id')
    image = models.ForeignKey(
        'images.Image',
        related_name='comments',
        null=False,
        blank=False,
        on_delete=models.CASCADE,
    )
    user = models.ForeignKey(
        'user.User',
        related_name='image_comments',
        null=False,
        blank=False,
        on_delete=models.CASCADE,
    )
    profile_type = models.CharField(
        max_length=1,
        choices=UserProfile.Type.choices(),
        default=UserProfile.Type.CUSTOMER,
    )
    content = models.TextField(null=False, blank=False, verbose_name='Comment')

    objects = ArchiveManager()
    all_objects = models.Manager()

    def get_user_name(self):
        if self.profile_type == UserProfile.Type.BUSINESS:
            return self.image.business.name
        # UserProfile.Type.CUSTOMER
        return self.user.full_name

    def get_user_avatar(self):
        """
        When comment was made from Business Profile we return business logo.
        In other case we return User profile photo.

        First we try to use prefetched objects (when list of comments are being returned and they
        have the data prefetched in queryset).
        """
        if self.profile_type == UserProfile.Type.BUSINESS:
            logo = None
            if hasattr(self.image.business, 'logo_images'):
                if self.image.business.logo_images:
                    logo = self.image.business.logo_images[-1]  # pick last
            else:
                logo = self.image.business.images.filter(category=ImageTypeEnum.LOGO).last()
            if logo:
                return logo.thumbnail(150, 150)
        else:
            if (
                hasattr(self.user, '_prefetched_objects_cache')
                # pylint: disable=protected-access
                and 'profiles' in self.user._prefetched_objects_cache
            ):
                profiles = [
                    # pylint: disable=protected-access
                    p
                    for p in self.user.profiles.all()
                    if p.profile_type == UserProfile.Type.CUSTOMER
                ]
                profile = profiles[-1] if profiles else None
            else:
                profile = self.user.profiles.filter(profile_type=UserProfile.Type.CUSTOMER).last()
            if profile and profile.photo:
                return profile.photo.thumbnail(100, 100)
        return None

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if self.deleted:
            delete_document(ESDocType.IMAGE_COMMENT, [self.id])
            return
        bump_document(River.IMAGE_COMMENT, [self.id])

    def delete(self, using=None, keep_parents=False):
        delete_return = super().delete(using, keep_parents)
        delete_document(ESDocType.IMAGE_COMMENT, [self.id])
        return delete_return


class ImageLike(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.IMAGE_LIKE

    id = models.AutoField(primary_key=True, db_column='imagelike_id')

    # Historicaly both "image" and "user" fields were plain IntegerField which means there was no db
    # constraint that would ensure data integrity. We changed them to ForeignKey, which allows us
    # to use Djago features such us lookups or prefetches but we remain them without db constraint
    # (db_constraint=False). Beware of that when accessing parent instances via: image_like.image or
    # image_like.user
    image = models.ForeignKey(
        Image,
        editable=False,
        on_delete=models.CASCADE,
        related_name='likes',
        db_constraint=False,
        db_index=True,
    )
    user = models.ForeignKey(
        User,
        editable=False,
        on_delete=models.CASCADE,
        related_name='image_likes',
        db_constraint=False,
        db_index=True,
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if self.deleted:
            delete_document(ESDocType.IMAGE_LIKE, [self.id])
            return
        index_document(ESDocType.IMAGE_LIKE, [self.id])

    def delete(self, using=None, keep_parents=False):
        delete_return = super().delete(using, keep_parents)
        delete_document(ESDocType.IMAGE_LIKE, [self.id])
        return delete_return


class BusinessPlaceholderImage(models.Model):
    class Meta:
        verbose_name = 'Business Placeholder Image'
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                fields=['category', 'type'],
                name='only_one_image_per_category_for_each_type',
            ),
        ]

    def get_image_path(self, filename):
        return f"{self.type}s/{filename}"

    image = models.ImageField(
        storage=StaticBusinessCategoryImageS3Boto3Storage(),
        upload_to=get_image_path,
    )
    category = models.ForeignKey(
        'business.BusinessCategory',
        related_name='business_placeholder',
        on_delete=models.CASCADE,
    )

    type = models.CharField(
        choices=BusinessCategoryPlaceholderType.choices(),
        default=BusinessCategoryPlaceholderType.COVER,
        max_length=16,
    )
