import pytest

from lib.elasticsearch.consts import ESDocType
from service.marketplace.marketplace import <PERSON>ms<PERSON><PERSON>nt<PERSON>andler
from webapps.marketplace.baker_recipes import (
    seo_content_data_recipe,
    seo_recommended4u_data_recipe,
)
from webapps.marketplace.cms.searchables.seo_homepage import (
    SeoContentDataSearchable,
    SeoRecommended4USearchable,
)
from webapps.marketplace.cms.searchables.serializers import (
    SeoCategoryContentHitSerializer,
    SeoRecommended4UHitSerializer,
)
from webapps.marketplace.models import SeoContentData


@pytest.mark.django_db
def test_seo_cms_content_document_serializer():
    instance: SeoContentData = seo_content_data_recipe.make()
    doc = instance.get_document()
    expected_doc = {
        'id': instance.id,
        'seo_content_id': instance.seo_content_id,
        'category_id': instance.seo_content.category_id,
        'active': True,
        'content_type': 'G',
        'position': 1,
        'language': 'en',
        'image_url': 'https://img.booksy.pm/test-bucket/cms_content/mocked-image.png',
        'title': 'Some test title',
        'body': '<p>Some body text</p>',
        'target_url': 'https://example.com',
        'image_alt': 'Image alt',
        'image_title': 'Image title',
    }
    assert doc.to_dict() == expected_doc, doc.to_dict()


@pytest.mark.usefixtures('seo_content_docs')
def test_seo_content_only_category():
    request_data = {'seo_content': True, 'category': [4]}
    res = CmsContentHandler.add_seo_content(request_data, 'es')
    assert len(res['seo_content']) == 1
    assert res['seo_content'][0]['body'] == 'Barber category content text...'


@pytest.mark.usefixtures('seo_content_docs')
def test_seo_content_only_treatment():
    request_data = {'seo_content': True, 'treatment': [374]}
    res = CmsContentHandler.add_seo_content(request_data, 'es')
    assert len(res['seo_content']) == 1
    assert res['seo_content'][0]['body'] == 'Mustache trim treatment content text...'


@pytest.mark.usefixtures('seo_content_docs')
def test_seo_content_category_and_treatment():
    request_data = {'seo_content': True, 'category': [4], 'treatment': [374]}
    res = CmsContentHandler.add_seo_content(request_data, 'es')
    assert len(res['seo_content']) == 1
    assert res['seo_content'][0]['body'] == 'Barber category content text...'


@pytest.mark.usefixtures('seo_content_docs')
def test_seo_content_no_content():
    request_data = {}
    res = CmsContentHandler.add_seo_content(request_data, 'es')
    assert res == {}


@pytest.mark.usefixtures('seo_content_docs')
def test_seo_content_invalid_category():
    request_data = {'seo_content': True, 'category': [100]}
    res = CmsContentHandler.add_seo_content(request_data, 'es')
    assert res['seo_content'] == []


@pytest.mark.usefixtures('recommended4u_docs')
def test_seo_cms_content_searchable():
    searchable = SeoContentDataSearchable(
        ESDocType.SEO_CMS_CONTENT_DATA,
        serializer=SeoCategoryContentHitSerializer,
    )

    resp = searchable.search(dict(category=[4], language='es')).execute()
    assert resp.hits.total.value == 3

    doc = resp.hits[0]
    expected_keys = {
        'meta',
        'position',
        'title',
        'body',
        'image_url',
        'image_alt',
        'image_title',
    }
    assert set(doc.to_dict().keys()) == expected_keys, doc.to_dict()


@pytest.mark.usefixtures('recommended4u_docs')
def test_seo_cms_content_aggregations():
    """We use aggregates to filter docs without category (default) or required category."""
    required_category = 4
    required_language = 'es'
    searchable = SeoContentDataSearchable(
        ESDocType.SEO_CMS_CONTENT_DATA,
    ).params(size=0)

    resp = searchable.search(
        dict(
            category=[required_category],
            language=required_language,
        ),
    ).execute()
    buckets = resp.aggregations.categorized_content.buckets
    assert len(buckets) == 2

    default_bucket = min(buckets, key=lambda b: b.key)
    required_bucket = max(buckets, key=lambda b: b.key)

    assert required_bucket == buckets[0]
    assert required_bucket.key == required_category

    assert default_bucket.content.hits.total.value == 2
    assert required_bucket.content.hits.total.value == 1


@pytest.mark.django_db
def test_seo_recommended4u_document_serializer():
    instance: SeoContentData = seo_recommended4u_data_recipe.make()
    doc = instance.get_document()

    expected_doc = {
        'id': instance.id,
        'seo_content_id': instance.seo_content_id,
        'active': True,
        'content_type': 'R',
        'position': 1,
        'language': 'en',
        'image_url': 'https://img.booksy.pm/test-bucket/cms_content/mocked-image.png',
        'image_alt': 'Image alt',
        'image_title': 'Image title',
        'title': 'Some test title',
        'body': '<p>Some body text</p>',
        'target_url': 'https://example.com',
    }
    assert doc.to_dict() == expected_doc, doc.to_dict()


@pytest.mark.usefixtures('recommended4u_docs')
def test_seo_recommended4u_searchable():
    searchable = SeoRecommended4USearchable(
        ESDocType.SEO_CMS_CONTENT_DATA,
        serializer=SeoRecommended4UHitSerializer,
    )
    resp = searchable.search(dict(language='en')).execute()
    assert resp.hits.total.value == 2, searchable.to_dict()

    doc = resp.hits[0]
    expected_keys = {
        'meta',
        'position',
        'title',
        'image_url',
        'image_alt',
        'image_title',
        'target_url',
    }
    assert set(doc.to_dict().keys()) == expected_keys, doc.to_dict()
