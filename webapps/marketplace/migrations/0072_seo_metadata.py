# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2019-06-10 07:53
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0071_merge_20190607_1159'),
    ]

    operations = [
        migrations.AlterField(
            model_name='seometadata',
            name='description_pattern',
            field=models.TextField(max_length=500),
        ),
        migrations.AlterField(
            model_name='seometadata',
            name='sub_type',
            field=models.CharField(
                choices=[
                    ('CC', 'Category + City'),
                    ('CD', 'Category + District'),
                    ('TC', 'Treatment + City'),
                    ('TD', 'Treatment + District'),
                    ('DL', 'Default listing'),
                    ('T', 'Treatment'),
                    ('C', 'Category'),
                ],
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name='seometadata',
            name='title_pattern',
            field=models.TextField(max_length=500),
        ),
    ]
