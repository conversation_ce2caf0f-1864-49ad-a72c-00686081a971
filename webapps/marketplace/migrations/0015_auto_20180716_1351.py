# Generated by Django 1.11.11 on 2018-07-16 13:51
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0014_marketplacetransaction_marketplacetransactionstatus'),
    ]

    operations = [
        migrations.AddField(
            model_name='marketplacetransactionstatus',
            name='errors',
            field=django.contrib.postgres.fields.jsonb.JSONField(null=True),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionstatus',
            name='braintree_id',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionstatus',
            name='status',
            field=models.CharField(
                choices=[
                    ('C', 'Created'),
                    ('A', 'Accepted'),
                    ('R', 'Rejected'),
                    ('X', 'Canceled'),
                    ('N', 'No user'),
                ],
                max_length=1,
            ),
        ),
    ]
