# Generated by Django 2.2.13 on 2020-09-22 11:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0108_merge_20200804_0950'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='marketplacetransactionrow',
            options={},
        ),
        migrations.AlterModelOptions(
            name='marketplacetransactionstatus',
            options={},
        ),
        migrations.AddIndex(
            model_name='marketplacetransactionrow',
            index=models.Index(fields=['booking', 'status'], name='mtr_booking_status_idx'),
        ),
        migrations.AddIndex(
            model_name='marketplacetransactionrow',
            index=models.Index(
                fields=['transaction', 'booking'], name='mtr_transaction_booking_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='marketplacetransactionrow',
            index=models.Index(
                fields=['status', 'refunded', 'created'], name='mtr_status_refund_created_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='marketplacetransactionstatus',
            index=models.Index(fields=['status', 'braintree_id'], name='mts_status_braintree_idx'),
        ),
        migrations.AddIndex(
            model_name='marketplacetransactionstatus',
            index=models.Index(fields=['type', 'transaction'], name='mts_type_transaction_idx'),
        ),
    ]
