import datetime
import uuid
from dataclasses import dataclass
from webapps.payments.enums import (
    KYCStatus,
    NewFinancialCenterActionButtons,
)
from lib.payment_providers.enums import PayoutMethodErrorCode


@dataclass
class ActionButton:
    type: NewFinancialCenterActionButtons
    label: str
    enabled: bool = False


@dataclass(frozen=True)
class Balance:
    total: int
    available: int
    pending: int
    available_for_fast_payout: int | None = None


@dataclass(frozen=True)
class Card:
    last_digits: str
    expiry_year: int
    expiry_month: int
    brand: str


@dataclass
class BankAccount:
    last_digits: str
    routing_number: str
    country: str
    iban: str | None = None


@dataclass(frozen=True)
class PayoutMethod:
    card: Card | None
    bank_account: BankAccount | None
    is_default_for_regular_payouts: bool
    is_default_for_fast_payouts: bool
    error_code: PayoutMethodErrorCode | None


@dataclass(frozen=True)
class Payout:
    next_payout_date: datetime.date | None
    payouts_active: bool
    fast_payouts_active: bool
    payout_methods: list[PayoutMethod]


@dataclass(frozen=True)
class FinancialCenterBusinessDetails:
    kyc_status: KYCStatus
    has_stripe_account: bool
    balance: Balance
    payout: Payout
    action_buttons: list[ActionButton]


@dataclass(frozen=True)
class PayoutItem:
    id: uuid.UUID
    amount: int
    date: str
    status: str
    type: str
    expected_arrival_date: str
    group_by: str
