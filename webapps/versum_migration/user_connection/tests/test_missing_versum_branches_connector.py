from unittest.mock import patch, call

from django.db.models import Max
from django.test import TestCase
from model_bakery import baker

from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.user.models import User
from webapps.versum_migration.user_connection.missing_versum_branches_connector import (
    MissingVersumBranchesConnector,
)
from webapps.versum_migration.user_connection.user_connector import UserConnector
from webapps.versum_migration.models import VersumUser, VersumFavoriteBranch


class MissingVersumBranchesConnectorTest(TestCase):
    @patch.object(UserConnector, 'call')
    def test_successful_connection(self, user_connector_call_mock):
        versum_branch_ids = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

        # versum_branch_id not in the list, no bci to create
        _versum_user_1 = baker.make(
            VersumUser,
            versum_user_id=101,
            versum_phone='+***********',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName',
            versum_last_name='SomeLastName',
        )

        _versum_favorite_branch_1 = baker.make(
            VersumFavoriteBranch,
            versum_user=_versum_user_1,
            versum_branch_id=100,
            versum_customer_id=1001,
            business=baker.make(Business),
        )

        _user_1_1 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _user_1_2 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _user_1_3 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeFirstName',
            last_name='SomeLastName',
        )

        # UserConnector to be called, no bci to be created (UserConnector is mocked here)
        versum_user_2 = baker.make(
            VersumUser,
            versum_user_id=102,
            versum_phone='+***********',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName-2',
            versum_last_name='SomeLastName-2',
        )

        _versum_favorite_branch_2_1 = baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_2,
            versum_branch_id=100,
            versum_customer_id=1002,
            business=baker.make(Business),
        )

        # matching versum branch for versum_user_2
        _versum_favorite_branch_2_2 = baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_2,
            versum_branch_id=3,
            versum_customer_id=10021,
            business=baker.make(Business),
        )

        _user_2_1 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeOtherFirstName-2',
            last_name='SomeOtherLastName-2',
        )

        _user_2_2 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeOtherFirstName-2',
            last_name='SomeOtherLastName-2',
        )

        user_2_3 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeFirstName-2',
            last_name='SomeLastName-2',
        )

        user_3 = baker.make(
            User,
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='SomeFirstName-3',
            last_name='SomeLastName-3',
        )

        versum_user_3 = baker.make(
            VersumUser,
            user=user_3,
            versum_user_id=103,
            versum_phone='+***********',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName-3',
            versum_last_name='SomeLastName-3',
        )

        # No bci to create, versum_branch_id not in the list
        business_3_1 = baker.make(Business)
        baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_3,
            versum_branch_id=100,
            versum_customer_id=10031,
            business=business_3_1,
        )

        # 1 bci to create
        business_3_2 = baker.make(Business)
        versum_favorite_branch_3_2 = baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_3,
            versum_branch_id=1,
            versum_customer_id=10032,
            business=business_3_2,
        )

        # No bci to create
        business_3_3 = baker.make(Business)
        baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_3,
            versum_branch_id=2,
            versum_customer_id=10033,
            business=business_3_3,
            bci=BusinessCustomerInfo.objects.create(user=user_3, business=business_3_3),
        )

        # No bci to create, connect to bci_3_4
        business_3_4 = baker.make(Business)
        bci_3_4 = BusinessCustomerInfo.objects.create(user=user_3, business=business_3_4)
        versum_favorite_branch_3_4 = baker.make(
            VersumFavoriteBranch,
            versum_user=versum_user_3,
            versum_branch_id=3,
            versum_customer_id=10034,
            business=business_3_4,
        )

        old_bci_count = BusinessCustomerInfo.objects.count()
        old_last_bci_id = BusinessCustomerInfo.objects.aggregate(Max('id'))['id__max']

        MissingVersumBranchesConnector.call(
            with_progress_bar=False,
            versum_branch_ids=versum_branch_ids,
        )

        self.assertEqual(
            user_connector_call_mock.mock_calls,
            [call(user=user_2_3, versum_user=versum_user_2)],
        )

        self.assertEqual(old_bci_count + 1, BusinessCustomerInfo.objects.count())

        versum_favorite_branch_3_2.refresh_from_db()
        self.assertIsNotNone(versum_favorite_branch_3_2.bci)
        self.assertEqual(versum_favorite_branch_3_2.bci.id, old_last_bci_id + 1)
        self.assertEqual(versum_favorite_branch_3_2.bci.business, business_3_2)

        versum_favorite_branch_3_4.refresh_from_db()
        self.assertEqual(versum_favorite_branch_3_4.bci, bci_3_4)
