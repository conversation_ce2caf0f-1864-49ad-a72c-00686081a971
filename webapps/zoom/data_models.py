import datetime
from dataclasses import dataclass

from webapps.zoom.enums import MeetingType


# pylint: disable=too-many-instance-attributes


@dataclass
class MeetingData:
    id: int  # pylint: disable=invalid-name
    host_id: str
    topic: str
    type: MeetingType
    status: str
    start_time: datetime.datetime
    duration: int
    timezone: str
    created_at: datetime.datetime
    start_url: str
    join_url: str
    password: str

    @staticmethod
    def from_response(data: dict) -> 'MeetingData':
        return MeetingData(
            id=data['id'],
            host_id=data['host_id'],
            topic=data['topic'],
            type=MeetingType(data['type']),
            status=data['status'],
            start_time=datetime.datetime.strptime(data['start_time'], '%Y-%m-%dT%H:%M:%SZ'),
            duration=data['duration'],
            timezone=data['timezone'],
            created_at=datetime.datetime.strptime(data['created_at'], '%Y-%m-%dT%H:%M:%SZ'),
            start_url=data['start_url'],
            join_url=data['join_url'],
            password=data.get('password'),
        )
