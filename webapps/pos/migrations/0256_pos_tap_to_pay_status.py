# Generated by Django 4.1.13 on 2024-04-02 11:44

from django.db import migrations, models
import webapps.pos.enums


class Migration(migrations.Migration):

    dependencies = [
        ("pos", "0255_pos_payout_method_change_allowed"),
    ]

    operations = [
        migrations.AddField(
            model_name="pos",
            name="tap_to_pay_status",
            field=models.CharField(
                choices=[("DISABLED", "DISABLED"), ("ENABLED", "ENABLED")],
                default=webapps.pos.enums.TapToPayStatus["DISABLED"],
                max_length=20,
            ),
        ),
    ]
