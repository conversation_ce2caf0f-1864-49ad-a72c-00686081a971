# Generated by Django 4.2.18 on 2025-04-10 07:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0291_alter_noshowsplash_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paymentmethod',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('gift_card', 'BooksyGiftCard'),
                    ('blik', 'Blik'),
                    ('other', ''),
                    ('kip', 'Keyed In Payment'),
                ],
                max_length=12,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='paymentrow',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('gift_card', 'BooksyGiftCard'),
                    ('blik', 'Blik'),
                    ('other', ''),
                    ('kip', 'Keyed In Payment'),
                ],
                max_length=64,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('gift_card', 'BooksyGiftCard'),
                    ('blik', 'Blik'),
                    ('other', ''),
                    ('kip', 'Keyed In Payment'),
                ],
                max_length=64,
                null=True,
            ),
        ),
    ]
