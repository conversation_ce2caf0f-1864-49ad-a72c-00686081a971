# Generated by Django 2.2.10 on 2020-06-08 06:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0195_fill_multibooking_id_in_transactions'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transaction',
            name='booking',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text='SubBooking associated with this Transaction. This booking is in one of TransactionRows.',
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='transactions',
                to='booking.SubBooking',
                verbose_name='SubBooking',
            ),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='multibooking',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text='Appointment associated with this TransactionAll of SubBookings in TransactionRows are part of this Appointment.',
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='transactions',
                to='booking.Appointment',
                verbose_name='Appointment',
            ),
        ),
    ]
