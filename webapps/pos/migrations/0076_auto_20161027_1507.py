# Generated by Django 1.9.5 on 2016-10-27 15:07
from django.conf import settings
from django.db import migrations, models
from country_config import Country


def get_bank_account_choices():
    if settings.API_COUNTRY == Country.US:
        return [
            ('checking', 'checking'),
            ('savings', 'savings'),
        ]
    else:
        return []


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0075_auto_20161027_1125'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bankaccount',
            name='routing_number',
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name='bankaccount',
            name='type',
            field=models.CharField(
                blank=True, choices=get_bank_account_choices(), max_length=16, null=True
            ),
        ),
    ]
