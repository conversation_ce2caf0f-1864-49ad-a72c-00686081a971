# Generated by Django 1.9.5 on 2016-09-21 12:17
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0069_paymentmethod_alias'),
    ]

    operations = [
        migrations.AlterField(
            model_name='receipt',
            name='status_code',
            field=models.CharField(
                choices=[
                    ('O', 'Call for Payment'),
                    ('A', 'Payment Awaiting'),
                    ('P', 'Payment Completed'),
                    ('F', 'Payment Failed'),
                    ('X', 'Payment Canceled'),
                    ('W', 'Deposit Verification Awaiting'),
                    ('D', 'Deposit Verification Completed'),
                    ('E', 'Deposit Verification Failed'),
                    ('T', 'Deposit Charge Awaiting'),
                    ('C', 'Deposit Charge Completed'),
                    ('L', 'Deposit Charge Failed'),
                    ('N', 'Deposit Charge Canceled'),
                    ('V', 'Deposit Cancel Awaiting'),
                    ('Y', 'Deposit Cancel Failed'),
                ],
                max_length=1,
            ),
        ),
    ]
