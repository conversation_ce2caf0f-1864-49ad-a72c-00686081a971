import elasticsearch.exceptions
import pytest
from model_bakery.recipe import baker

from lib.elasticsearch.consts import (
    ESIndex,
)
from lib.feature_flag.feature import UseExplicitRoutingWhenDeletingDocumentsFlag
from lib.feature_flag.old_experiment import ElasticsearchWithoutDeleteByQueryFlag
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.pos.elasticsearch.commodities import CommodityDocument
from webapps.pos.elasticsearch.service_variants import ServiceVariantDocument
from webapps.pos.models import Commodity


@pytest.mark.django_db
@override_feature_flag({ElasticsearchWithoutDeleteByQueryFlag.flag_name: True})
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_commodity_document_delete_extra(clean_index_function_fixture):
    clean_index_function_fixture(ESIndex.BUSINESS_ITEM)

    business = business_recipe.make()
    commodity_1 = baker.make(Commodity, business=business)
    commodity_1.reindex()
    commodity_2 = baker.make(Commodity, business=business)
    commodity_2.reindex(refresh_index=True)

    # ensure deleted item is also deleted from es
    commodity_1.soft_delete()
    CommodityDocument.delete_extra([commodity_1.id])

    with pytest.raises(elasticsearch.exceptions.NotFoundError):
        CommodityDocument.get(commodity_1.id)

    # ensure that other items are unaffected
    CommodityDocument.get(commodity_2.id)


@pytest.mark.django_db
@override_feature_flag({ElasticsearchWithoutDeleteByQueryFlag.flag_name: True})
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_service_variant_document_delete_extra(clean_index_function_fixture):
    index = clean_index_function_fixture(ESIndex.BUSINESS_ITEM)

    business = business_recipe.make()
    service = service_recipe.make(business=business)
    service_variant_1 = service_variant_recipe.make(service=service)
    service_variant_2 = service_variant_recipe.make(service=service)

    service_variant_1.reindex()
    service_variant_2.reindex()
    index.refresh()

    # ensure deleted item is also deleted from es
    ServiceVariantDocument.delete_extra([service_variant_1.id])

    with pytest.raises(elasticsearch.exceptions.NotFoundError):
        ServiceVariantDocument.get(service_variant_1.id)

    # ensure that other items are unaffected
    ServiceVariantDocument.get(service_variant_2.id)
