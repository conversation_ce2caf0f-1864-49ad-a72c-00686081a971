import os
from django.conf import settings


def get_booksy_gift_cards_address() -> str:
    if bgc_grpc_host := settings.YAML_CONFIG.get('bgc-grpc-host'):
        return bgc_grpc_host
    booksy_gift_cards_host = os.environ.get('BOOKSY_GIFT_CARDS_API_GRPC_API_SERVICE_HOST')
    booksy_gift_cards_port = os.environ.get('BOOKSY_GIFT_CARDS_API_GRPC_API_SERVICE_PORT')
    if not booksy_gift_cards_host or not booksy_gift_cards_port:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            "BOOKSY_GIFT_CARDS_ADDRESS not defined properly"
        )
    return f"{booksy_gift_cards_host}:{booksy_gift_cards_port}"
