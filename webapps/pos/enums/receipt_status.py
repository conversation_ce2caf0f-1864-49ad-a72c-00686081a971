from django.utils.translation import pgettext_lazy

CALL_FOR_PAYMENT = 'O'
CALL_FOR_PAYMENT_3DS = 'o'
PENDING = 'G'
PARK_SALE = 'Q'
PAYMENT_AWAITING = 'A'
PAYMENT_SUCCESS = 'P'
PAYMENT_FAILED = 'F'
PAYMENT_CANCELED = 'X'

CALL_FOR_PREPAYMENT = 'J'
CALL_FOR_PREPAYMENT_3DS = 'j'
PREPAYMENT_AUTHORISATION_SUCCESS = 'K'
PREPAYMENT_AUTHORISATION_FAILED = 'M'
PREPAYMENT_SUCCESS = 'B'
PREPAYMENT_FAILED = 'I'

SENT_FOR_REFUND = 'a'
REFUNDED = 'b'
CHARGEBACK = 'c'
CHARGEBACK_REVERSED = 'p'
SECOND_CHARGEBACK = 'r'

CALL_FOR_DEPOSIT = 'H'
CALL_FOR_DEPOSIT_3DS = 'h'
DEPOSIT_AUTHORISATION_AWAITING = 'W'
DEPOSIT_AUTHORISATION_SUCCESS = 'D'  # deposit can be charged
DEPOSIT_AUTHORISATION_FAILED = 'E'
DEPOSIT_CHARGE_AWAITING = 'T'
DEPOSIT_CHARGE_SUCCESS = 'C'
DEPOSIT_CHARGE_FAILED = 'L'
DEPOSIT_CHARGE_CANCELED = 'N'
DEPOSIT_CANCEL_AWAITING = 'V'
DEPOSIT_CANCEL_FAILED = 'Y'
GIFT_CARD_DEPOSIT = 'g'
ARCHIVED = 'R'
IN_PROGRESS = 'd'

VOUCHER_PARTIAL_REDEEM = 'Z'

# HACK STATUS ONLY FOR REPORTS
DONATION_SUCCESS = 'z'

CALL_FOR_BOOKSY_PAY = 'U'
CALL_FOR_BOOKSY_PAY_3DS = 'u'
BOOKSY_PAY_SUCCESS = 'S'
BOOKSY_PAY_FAILED = 'f'

# grouped for STATUS_TYPES:
CALL_STATUSES = (CALL_FOR_PAYMENT,)

PENDING_STATUSES = (PENDING,)

SUCCESS_STATUSES = (
    PAYMENT_SUCCESS,
    DEPOSIT_CHARGE_SUCCESS,
)
SUCCESS_STATUSES_WITH_PREPAYMENT = SUCCESS_STATUSES + (PREPAYMENT_SUCCESS,)
PENDING_3DS_STATUSES = (
    CALL_FOR_PAYMENT_3DS,
    CALL_FOR_PREPAYMENT_3DS,
    CALL_FOR_DEPOSIT_3DS,
    CALL_FOR_BOOKSY_PAY_3DS,
)
AUTHORIZED_STATUSES = (
    DEPOSIT_AUTHORISATION_SUCCESS,
    PREPAYMENT_AUTHORISATION_SUCCESS,
)
# CHARGED_STATUSES = (
#     DEPOSIT_CHARGE_SUCCESS,
# )
FAILED_STATUSES = (
    PAYMENT_FAILED,
    PREPAYMENT_FAILED,
    DEPOSIT_AUTHORISATION_FAILED,
    PREPAYMENT_AUTHORISATION_FAILED,
    DEPOSIT_CHARGE_FAILED,
    BOOKSY_PAY_FAILED,
)
AWAITING_STATUSES = (
    PAYMENT_AWAITING,
    DEPOSIT_AUTHORISATION_AWAITING,
    DEPOSIT_CHARGE_AWAITING,
    DEPOSIT_CANCEL_AWAITING,
)
CANCELED_STATUSES = (
    PAYMENT_CANCELED,
    DEPOSIT_CHARGE_CANCELED,
    DEPOSIT_CANCEL_FAILED,
)

FAKE_EDIT_STATUSES = (
    GIFT_CARD_DEPOSIT,
    PARK_SALE,
    PAYMENT_CANCELED,
    PREPAYMENT_AUTHORISATION_FAILED,
    PREPAYMENT_FAILED,
    PREPAYMENT_SUCCESS,
    VOUCHER_PARTIAL_REDEEM,
    BOOKSY_PAY_SUCCESS,
    BOOKSY_PAY_FAILED,
)

PAYABLE_STATUSES = FAKE_EDIT_STATUSES + (ARCHIVED, PAYMENT_FAILED)

NOT_LOCKING_STATUSES = FAKE_EDIT_STATUSES + (PAYMENT_SUCCESS,)

REFUND_STATUSES = (
    SENT_FOR_REFUND,
    REFUNDED,
    CHARGEBACK,
    CHARGEBACK_REVERSED,
    SECOND_CHARGEBACK,
)

REFUNDABLE_STATUSES = (
    PREPAYMENT_SUCCESS,
    PAYMENT_SUCCESS,
    DEPOSIT_CHARGE_SUCCESS,
    BOOKSY_PAY_SUCCESS,
)

NEGATIVE_VALUE_STATUSES = (
    REFUNDED,
    CHARGEBACK,
    SECOND_CHARGEBACK,
)

DELETABLE_STATUSES = (
    PAYMENT_SUCCESS,
    PARK_SALE,
    PAYMENT_CANCELED,
)

STATUSES_WITHOUT_MONEY = (
    PAYMENT_CANCELED,
    PAYMENT_FAILED,
    REFUNDED,
    CHARGEBACK,
    SECOND_CHARGEBACK,
    CALL_FOR_PAYMENT,
    PENDING,
    PREPAYMENT_FAILED,
    DEPOSIT_CHARGE_CANCELED,
    DEPOSIT_CHARGE_FAILED,
    DEPOSIT_AUTHORISATION_SUCCESS,
    DEPOSIT_AUTHORISATION_FAILED,
    BOOKSY_PAY_FAILED,
)

STATUSES_WITH_MONEY = (
    PAYMENT_SUCCESS,
    DEPOSIT_CHARGE_SUCCESS,
    PREPAYMENT_SUCCESS,
    SENT_FOR_REFUND,  # Merchant still has money
    CHARGEBACK_REVERSED,  # Merchant gets money back
    BOOKSY_PAY_SUCCESS,
)

CHARGEBACK_STATUSES = (
    CHARGEBACK,
    SECOND_CHARGEBACK,
)

CANCELABLE_STATUSES = (
    CALL_FOR_PAYMENT,
    CALL_FOR_PAYMENT_3DS,
    CALL_FOR_PREPAYMENT,
    CALL_FOR_PREPAYMENT_3DS,
    PENDING,
    PAYMENT_FAILED,
    PREPAYMENT_FAILED,
    PREPAYMENT_AUTHORISATION_SUCCESS,
    CALL_FOR_BOOKSY_PAY,
    CALL_FOR_BOOKSY_PAY_3DS,
    BOOKSY_PAY_FAILED,
)

# Hack for old apps to show full amount of transaction
# even if we haven't got that money
STATUS_TO_SHOW_FULL_AMOUNT = (
    DEPOSIT_CHARGE_CANCELED,
    DEPOSIT_CHARGE_FAILED,
    DEPOSIT_AUTHORISATION_SUCCESS,
    DEPOSIT_AUTHORISATION_FAILED,
)

# grouped statuses
CALL_FOR_PAYMENT_ACTIONS = (
    CALL_FOR_DEPOSIT,
    CALL_FOR_PREPAYMENT,
    CALL_FOR_PAYMENT,
    CALL_FOR_BOOKSY_PAY,
)

# grouped for navigation
NAV_ISSUED_STATUSES = (  # SUCCESS_STATUSES + CHARGED_STATUSES
    PAYMENT_SUCCESS,
    PREPAYMENT_SUCCESS,
    DEPOSIT_CHARGE_SUCCESS,
    BOOKSY_PAY_SUCCESS,
)
NAV_CANCELED_STATUSES = (  # FAILED_STATUSES + CANCELED_STATUSES
    PAYMENT_FAILED,
    DEPOSIT_AUTHORISATION_FAILED,
    DEPOSIT_CHARGE_FAILED,
    PAYMENT_CANCELED,
    DEPOSIT_CHARGE_CANCELED,
)
NAV_UNFINISHED = FAKE_EDIT_STATUSES + (  # CALL_STATUSES
    CALL_FOR_PAYMENT,
    PENDING,
    PAYMENT_FAILED,
)
NAV_FINISHED = NAV_ISSUED_STATUSES + NAV_CANCELED_STATUSES + REFUND_STATUSES
NAV_PAYMENT_HISTORY = NAV_FINISHED + (PAYMENT_FAILED,) + (PREPAYMENT_SUCCESS,)

# NAV_AWAITING??? - CALL_STATUSES + AUTHORIZED_STATUSES + AWAITING_STATUSES

STATUS_TYPE__CALL_FOR_PAYMENT = 'call_for_payment'
STATUS_TYPE__CALL_FOR_DEPOSIT = 'call_for_deposit'
STATUS_TYPE__CALL_FOR_3DS = 'call_for_3ds'
STATUS_TYPE__PENDING = 'pending'
STATUS_TYPE__PREPAYMENT = 'prepayment'
STATUS_TYPE__CHARGED = 'charged'
STATUS_TYPE__AWAITING = 'awaiting'
STATUS_TYPE__IN_PROGRESS = 'in_progress'
STATUS_TYPE__SUCCESS = 'success'
STATUS_TYPE__FAILED = 'failed'
STATUS_TYPE__CANCELED = 'canceled'
STATUS_TYPE__SENT_FOR_REFUND = 'sent_for_refund'
STATUS_TYPE__REFUNDED = 'refunded'
STATUS_TYPE__CHARGEBACK = 'chargeback'
STATUS_TYPE__CHARGEBACK_REVERSED = 'chargeback_reversed'
STATUS_TYPE__SECOND_CHARGEBACK = 'second_chargeback'
STATUS_TYPE__ARCHIVED = 'archived'
STATUS_TYPE__AUTHORIZED = 'authorized'
STATUS_TYPE__PARK_SALE = 'park_sale'
STATUS_TYPE__PARTIAL_SETTLEMENT = 'partial_settlement'
STATUS_TYPE__BOOKSY_GIFT_CARD = 'booksy_gift_card'
STATUS_TYPE__BOOKSY_PAY = 'booksy_pay'
STATUS_TYPE__REFUND_REQUESTED = 'refund_requested'
STATUS_TYPE__REFUND_OVERDUE = 'refund_overdue'

STATUS_TYPES = {
    STATUS_TYPE__CALL_FOR_PAYMENT: CALL_STATUSES,
    STATUS_TYPE__CALL_FOR_DEPOSIT: (CALL_FOR_DEPOSIT, CALL_FOR_PREPAYMENT, CALL_FOR_BOOKSY_PAY),
    STATUS_TYPE__CALL_FOR_3DS: (
        CALL_FOR_PAYMENT_3DS,
        CALL_FOR_PREPAYMENT_3DS,
        CALL_FOR_DEPOSIT_3DS,
        CALL_FOR_BOOKSY_PAY_3DS,
    ),
    STATUS_TYPE__PENDING: PENDING_STATUSES,
    STATUS_TYPE__PREPAYMENT: (PREPAYMENT_SUCCESS,),
    STATUS_TYPE__BOOKSY_GIFT_CARD: (GIFT_CARD_DEPOSIT,),
    STATUS_TYPE__BOOKSY_PAY: (BOOKSY_PAY_SUCCESS,),
    STATUS_TYPE__PARTIAL_SETTLEMENT: VOUCHER_PARTIAL_REDEEM,
    STATUS_TYPE__AUTHORIZED: AUTHORIZED_STATUSES,
    # 'charged': CHARGED_STATUSES,
    STATUS_TYPE__AWAITING: AWAITING_STATUSES,
    STATUS_TYPE__SUCCESS: SUCCESS_STATUSES,
    STATUS_TYPE__FAILED: FAILED_STATUSES,
    STATUS_TYPE__CANCELED: CANCELED_STATUSES,
    STATUS_TYPE__ARCHIVED: (ARCHIVED,),
    STATUS_TYPE__PARK_SALE: (PARK_SALE,),
    STATUS_TYPE__SENT_FOR_REFUND: (SENT_FOR_REFUND,),
    STATUS_TYPE__REFUNDED: (REFUNDED,),
    STATUS_TYPE__CHARGEBACK: (CHARGEBACK,),
    STATUS_TYPE__CHARGEBACK_REVERSED: (CHARGEBACK_REVERSED,),
    STATUS_TYPE__SECOND_CHARGEBACK: (SECOND_CHARGEBACK,),
}
STATUS_TYPES_REVERSE = {
    status: status_type
    for status_type, statuses in list(STATUS_TYPES.items())
    for status in statuses
}

STATUS_TYPES.update(
    {
        'call_for_payment_actions': CALL_FOR_PAYMENT_ACTIONS,
        # navigation status types
        'nav_issued': NAV_ISSUED_STATUSES,
        'nav_canceled': CANCELED_STATUSES,
        'nav_unfinished': NAV_UNFINISHED,
        'nav_finished': NAV_FINISHED,
        'nav_payment_history': NAV_PAYMENT_HISTORY,
    }
)

# finalized status means that there is no need to wait for status update:
# status change is impossible or an action is needed for further status change
FINALIZED_STATUSES = (
    # success on payment or deposit is final
    # - payment or charge success status will never change
    # - deposit authorization status will only change when
    #   business charges or cancels the deposit
    SUCCESS_STATUSES
    + AUTHORIZED_STATUSES
    +
    # cancel on payment and deposit is final
    # - status will never change
    # - business must create a new call for payment
    CANCELED_STATUSES
    +
    # deposit authorization or charge fail is final
    # - customer must authorize again (creating another booking and transaction)
    # - business must try to charge again
    (DEPOSIT_AUTHORISATION_FAILED, DEPOSIT_CHARGE_FAILED)
)

STATUS_TYPES_DISPLAY = {
    STATUS_TYPE__CALL_FOR_PAYMENT: pgettext_lazy(
        'receipt status',
        'Call for Payment',
    ),
    STATUS_TYPE__CALL_FOR_DEPOSIT: pgettext_lazy(
        'receipt_status',
        'Call for deposit or cancellation fee',  # Contains both NSP: CF and PP but also Booksy Pay!
    ),
    STATUS_TYPE__CALL_FOR_3DS: pgettext_lazy(
        'receipt_status',
        '3DSecure required',
    ),
    STATUS_TYPE__PENDING: pgettext_lazy('receipt status', 'Pending'),
    STATUS_TYPE__PREPAYMENT: pgettext_lazy('receipt_status', 'Deposit'),
    STATUS_TYPE__AUTHORIZED: pgettext_lazy('receipt status', 'Verified'),
    STATUS_TYPE__CHARGED: pgettext_lazy('receipt status', 'Charged'),
    STATUS_TYPE__AWAITING: pgettext_lazy('receipt status', 'Awaiting'),
    STATUS_TYPE__IN_PROGRESS: pgettext_lazy('receipt_status', 'In progress'),
    STATUS_TYPE__SUCCESS: pgettext_lazy('receipt status', 'Paid'),
    STATUS_TYPE__FAILED: pgettext_lazy('receipt status', 'Failed'),
    STATUS_TYPE__CANCELED: pgettext_lazy('receipt status', 'Cancelled'),
    STATUS_TYPE__ARCHIVED: pgettext_lazy('receipt status', 'Archived'),
    STATUS_TYPE__PARK_SALE: pgettext_lazy('receipt_status', 'Park sale'),
    STATUS_TYPE__PARTIAL_SETTLEMENT: pgettext_lazy(
        'receipt_status',
        'Partial settlement',
    ),
    STATUS_TYPE__SENT_FOR_REFUND: pgettext_lazy(
        'receipt status',
        'Sent for refund',
    ),
    STATUS_TYPE__REFUNDED: pgettext_lazy('receipt status', 'Refunded'),
    STATUS_TYPE__CHARGEBACK: pgettext_lazy('receipt status', 'Chargeback'),
    STATUS_TYPE__CHARGEBACK_REVERSED: pgettext_lazy(
        'receipt status',
        'Chargeback Reversed',
    ),
    STATUS_TYPE__SECOND_CHARGEBACK: pgettext_lazy(
        'receipt status',
        'Second Chargeback',
    ),
    STATUS_TYPE__BOOKSY_PAY: pgettext_lazy('receipt_status', 'Booksy Pay'),
    STATUS_TYPE__BOOKSY_GIFT_CARD: pgettext_lazy('receipt_status', 'Booksy Gift Card'),
    STATUS_TYPE__REFUND_REQUESTED: pgettext_lazy('receipt_status', 'Refund requested'),
    STATUS_TYPE__REFUND_OVERDUE: pgettext_lazy('receipt_status', 'Refund overdue'),
}

SHORT_STATUS_TYPES = {
    STATUS_TYPE__CALL_FOR_PAYMENT: CALL_STATUSES,
    STATUS_TYPE__CALL_FOR_DEPOSIT: (CALL_FOR_DEPOSIT, CALL_FOR_PREPAYMENT, CALL_FOR_BOOKSY_PAY),
    STATUS_TYPE__CALL_FOR_3DS: (
        CALL_FOR_PAYMENT_3DS,
        CALL_FOR_PREPAYMENT_3DS,
        CALL_FOR_DEPOSIT_3DS,
        CALL_FOR_BOOKSY_PAY_3DS,
    ),
    STATUS_TYPE__IN_PROGRESS: (
        PENDING,
        PARK_SALE,
        PREPAYMENT_SUCCESS,
        VOUCHER_PARTIAL_REDEEM,
        BOOKSY_PAY_SUCCESS,
    ),
    STATUS_TYPE__SUCCESS: (PAYMENT_SUCCESS, REFUNDED, DEPOSIT_CHARGE_SUCCESS),
    STATUS_TYPE__FAILED: FAILED_STATUSES,
    STATUS_TYPE__CANCELED: CANCELED_STATUSES,
    STATUS_TYPE__SENT_FOR_REFUND: (SENT_FOR_REFUND,),
    STATUS_TYPE__REFUNDED: (REFUNDED,),
    STATUS_TYPE__CHARGEBACK: (CHARGEBACK,),
    STATUS_TYPE__CHARGEBACK_REVERSED: (CHARGEBACK_REVERSED,),
    STATUS_TYPE__SECOND_CHARGEBACK: (SECOND_CHARGEBACK,),
    STATUS_TYPE__ARCHIVED: (ARCHIVED,),
    STATUS_TYPE__AUTHORIZED: AUTHORIZED_STATUSES,
    STATUS_TYPE__AWAITING: AWAITING_STATUSES,
}

SHORT_STATUS_TYPES_DESCRIPTION = {
    STATUS_TYPE__CALL_FOR_PAYMENT: {
        'business': pgettext_lazy(
            'receipt status',
            'Waiting for client to finish payment',
        ),
        'customer': pgettext_lazy(
            'receipt status',
            'Please complete payment',
        ),
    },
    STATUS_TYPE__CALL_FOR_3DS: pgettext_lazy(
        'receipt status',
        'Transaction requires 3DSecure authentication',
    ),
    STATUS_TYPE__IN_PROGRESS: pgettext_lazy(
        'receipt status',
        'Transaction is not finished yet',
    ),
    STATUS_TYPE__AUTHORIZED: pgettext_lazy(
        'receipt status',
        'Not charged',
    ),
    STATUS_TYPE__SENT_FOR_REFUND: pgettext_lazy(
        'receipt_status',
        'Refund proccess started',
    ),
    STATUS_TYPE__REFUND_REQUESTED: pgettext_lazy(
        'receipt status',
        'Refund requested',
    ),
    STATUS_TYPE__REFUND_OVERDUE: pgettext_lazy(
        'receipt status',
        'Refund overdue',
    )
}

SHORT_STATUS_TYPES_REVERSE = {
    status: status_type
    for status_type, statuses in list(SHORT_STATUS_TYPES.items())
    for status in statuses
}
