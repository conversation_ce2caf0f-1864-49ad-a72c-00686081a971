import datetime
from collections import namedtuple

from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache
from django.db.models import Q, Sum
from django.utils.translation import gettext_noop as _noop

from lib.enums import BaseEnum
from lib.payments.enums import RefundError
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.tools import minor_unit, tznow
from webapps.pos.enums import (
    PaymentProviderEnum,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.events import refund_received_event
from webapps.pos.exceptions import RefundNotPossibleException
from webapps.pos.models import (
    PaymentRow,
    PaymentRowChange,
    POS,
    OperationFee,
)
from webapps.pos.typing import RefundRequestStatusSettings


class RefundNonPossible(BaseEnum):
    ROW_NOT_REFUNDABLE = 'ROW_NOT_REFUNDABLE'
    REQUESTED = 'REQUESTED'
    SITE_DISABLED = 'SITE_DISABLED'
    POS_DISABLED = 'POS_DISABLED'
    EXPIRED = 'EXPIRED'
    SETTLED = 'SETTLED'
    MISSING_FUNDS = 'MISSING_FUNDS'


REFUND_NON_POSSIBLE_MESSAGE = {
    RefundNonPossible.ROW_NOT_REFUNDABLE: _noop('Row has wrong status or type'),
    RefundNonPossible.REQUESTED: _noop('Refund already requested'),
    RefundNonPossible.SITE_DISABLED: _noop('Refund on this site not possible'),
    RefundNonPossible.POS_DISABLED: _noop('Refund for this business disabled'),
    RefundNonPossible.EXPIRED: _noop('Refund possibility of this row expired'),
    RefundNonPossible.MISSING_FUNDS: _noop('Not enough payable balance'),
}


ErrorResult = namedtuple('ErrorResult', 'code, message')


LAST_SYNC_CACHE_KEY = "pending_refunds_last_sync"


def is_refund_possible(  # pylint: disable=too-many-branches, too-many-return-statements, unused-argument
    payment_row,
    check_requested=False,
    check_balance=False,
    from_admin=False,
    refresh_transaction=True,
):
    """Check if refund for given PaymentRow is possible
    :param payment_row: PaymentRow instance
    :param check_requested: should we check if PaymentRow already
    marked for request
    :param check_balance: should check online balance
    :param from_admin: called from admin
    :param refresh_transaction: We should not refresh transaction each time.
        Especially during GET listing. Refreshing is only required if real
        refund is present.
    """
    # pylint: disable=cyclic-import
    from webapps.stripe_integration.provider import StripeProvider

    # If payment_row has `transaction` cached by select_related,
    # checking last receipt of transaction may be incorrect.
    # Example:
    # Transaction has two payment rows, both in refund requested state.
    # If first has been refunded, second one must not be refunded in same run.
    if refresh_transaction:
        payment_row.receipt.transaction.refresh_from_db()

    if not settings.POS__REFUNDS:
        code = RefundNonPossible.SITE_DISABLED
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    # don't allow refunds for adyen because its disabled now :)
    if payment_row.provider in {
        PaymentProviderEnum.ADYEN_PROVIDER,
        PaymentProviderEnum.ADYEN_PROXY_PROVIDER,
    }:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    # check if it's the newest payment row
    if payment_row.receipt.transaction.children.all():
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    # check if the payment row is in the last receipt
    if payment_row.receipt.transaction.latest_receipt != payment_row.receipt:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    if txn_refactor_stage2_enabled(txn=payment_row.receipt.transaction):
        from webapps.point_of_sale.services.basket_payment import BasketPaymentService
        from webapps.point_of_sale.models import BasketPayment
        from webapps.point_of_sale.exceptions import InvalidBasketPayment

        try:
            basket_payment = BasketPayment.objects.get(id=payment_row.basket_payment_id)
            possible, error_code = BasketPaymentService.is_refund_possible(
                original_basket_payment=basket_payment,
            )
        except BasketPayment.DoesNotExist:
            code = RefundNonPossible.ROW_NOT_REFUNDABLE
            return False, ErrorResult(
                code=code,
                message=REFUND_NON_POSSIBLE_MESSAGE[code],
            )
        except InvalidBasketPayment:
            code = RefundNonPossible.ROW_NOT_REFUNDABLE
            return False, ErrorResult(
                code=code,
                message=REFUND_NON_POSSIBLE_MESSAGE[code],
            )

        if possible:
            return True, None

        code = {
            RefundError.ALREADY_REFUNDED: RefundNonPossible.REQUESTED,
            RefundError.EXPIRED: RefundNonPossible.EXPIRED,
            RefundError.INVALID_REFUND_AMOUNT: RefundNonPossible.ROW_NOT_REFUNDABLE,
            RefundError.MISSING_BALANCE: RefundNonPossible.MISSING_FUNDS,
            RefundError.INVALID_PAYMENT_STATUS: RefundNonPossible.ROW_NOT_REFUNDABLE,
        }[error_code]
        return False, ErrorResult(
            code=code,
            message=REFUND_NON_POSSIBLE_MESSAGE[code],
        )

    if payment_row.status not in receipt_status.REFUNDABLE_STATUSES:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    # check if it was an online payment
    if payment_row.payment_type.code not in [
        PaymentTypeEnum.PAY_BY_APP,
        PaymentTypeEnum.BLIK,
        PaymentTypeEnum.PREPAYMENT,
        PaymentTypeEnum.STRIPE_TERMINAL,
        PaymentTypeEnum.TAP_TO_PAY,
        PaymentTypeEnum.BOOKSY_PAY,
    ]:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    if check_requested and payment_row.refund_requested:
        code = RefundNonPossible.REQUESTED
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    # check refund settings on a pos
    if (
        not payment_row.receipt.transaction.pos.refund_enabled
        and payment_row.provider != StripeProvider.codename
    ):
        code = RefundNonPossible.POS_DISABLED
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    if payment_row.settled:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    if not payment_row.provider:
        code = RefundNonPossible.ROW_NOT_REFUNDABLE
        return False, ErrorResult(code=code, message=REFUND_NON_POSSIBLE_MESSAGE[code])

    return True, None


def mark_refund_requested(payment_row, operator):
    payment_row.refund_requested = tznow()
    payment_row.refund_operator = operator
    payment_row.save()


def execute_pending_refunds(payment_row_id=None, quiet=False):
    """Send for refund pending rows

    :param payment_row_id: if provided, execute for this row only
    :param quiet: dont send notifications
    :return: count of rows sent
    """
    pending = _get_pending_refunds(payment_row_id=payment_row_id).select_related(
        'receipt__transaction__pos__business',
        'receipt__transaction__customer_card',
    )

    count = 0
    for payment_row in pending:
        possible, _ = is_refund_possible(
            payment_row, check_balance=True
        )  # pylint: disable=unused-variable
        if possible:
            count += 1
            refund_row = payment_row.send_for_refund(
                operator=payment_row.refund_operator, from_admin=False
            )
            if not quiet:
                refund_received_event.send(refund_row)
    if not payment_row_id:
        cache.set(LAST_SYNC_CACHE_KEY, tznow(), timeout=24 * 60 * 60)
    return count


def _get_pending_refunds(payment_row_id=None):
    qs = PaymentRow.objects.filter(
        children__isnull=True,  # exclude already processed
        refund_requested__gt=tznow() - datetime.timedelta(days=31),
        status__in=receipt_status.REFUNDABLE_STATUSES,
    )
    if last_sync := cache.get(LAST_SYNC_CACHE_KEY, None):
        qs = qs.filter(updated__gte=last_sync - datetime.timedelta(hours=2))  # 2h overlap

    qs = qs.filter(
        Q(
            ~Q(provider=PaymentProviderEnum.STRIPE_PROVIDER),
            receipt__transaction__pos__account_holders__isnull=False,
            marketpay_splits__isnull=False,  # exclude non marketpay rows
        )
        | Q(
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            intents__account__isnull=False,
            payment_splits__isnull=False,
        )
    )
    if payment_row_id is not None:
        qs = qs.filter(pk=payment_row_id)
    return qs


def create_operation_fee(payment_row, commit=True):
    if payment_row.status not in receipt_status.REFUND_STATUSES:
        raise Exception(  # pylint: disable=broad-exception-raised
            'Wrong PaymentRow for fee', payment_row.id
        )

    # catch operation_fee from parent, if exists
    existing_fee = _get_operation_fee_from_parent(payment_row)
    if existing_fee:
        existing_fee.payment_row = payment_row
        if commit:
            existing_fee.save(update_fields=['payment_row'])
        return existing_fee

    calculate = (
        calculate_chargeback_fee
        if payment_row.status in receipt_status.CHARGEBACK_STATUSES
        else calculate_refund_fee
    )
    amount, parts = calculate(payment_row)
    instance = OperationFee(
        amount=amount,
        currency=settings.CURRENCY_CODE,
        parts=parts,
        payment_row=payment_row,
        pos=payment_row.receipt.transaction.pos,
    )
    if commit:
        instance.save()
    return instance


def _get_operation_fee_from_parent(payment_row):
    while payment_row.parent_payment_row:
        payment_row = payment_row.parent_payment_row
        operation_fee = payment_row.operation_fees.first()
        if operation_fee:
            return operation_fee


def calculate_refund_fee(payment_row):
    pos = payment_row.receipt.transaction.pos
    plan = pos.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)

    total_amount = minor_unit(payment_row.amount or 0)
    provision_amount = int(round(plan.refund_provision * total_amount))
    txn_fee_amount = minor_unit(plan.refund_txn_fee)

    amount = provision_amount + txn_fee_amount
    parts = {
        'provision': provision_amount,
        'txn_fee': txn_fee_amount,
    }
    return amount, parts


def calculate_chargeback_fee(payment_row):
    pos = payment_row.receipt.transaction.pos
    plan = pos.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)

    total_amount = minor_unit(payment_row.amount or 0)
    provision_amount = int(round(plan.chargeback_provision * total_amount))
    txn_fee_amount = minor_unit(plan.chargeback_txn_fee)

    amount = provision_amount + txn_fee_amount
    parts = {
        'provision': provision_amount,
        'txn_fee': txn_fee_amount,
    }
    return amount, parts


def get_pending_fees(before_midnight=True):
    """Returns pending fees created before midnight - business tz relative"""
    qs = OperationFee.objects.pending()
    if before_midnight:
        qs = qs.before_midnight()

    return (
        qs.values(
            'pos__business_id',
        )
        .order_by('pos__business_id')
        .annotate(
            amount=Sum('amount'),
            fee_refs=ArrayAgg('reference'),
        )
    )


def settle_fees(fee_refs):
    OperationFee.objects.filter(reference__in=fee_refs).update(settled=True)


def unsettle_fees(fee_refs):
    OperationFee.objects.filter(reference__in=fee_refs).update(settled=False)


def do_refund(payment_row: 'PaymentRow', user: 'User') -> None:
    possible, error = is_refund_possible(
        payment_row,
        check_requested=True,
        check_balance=False,
    )
    if not possible:
        raise RefundNotPossibleException(error.message)

    mark_refund_requested(payment_row, user)

    PaymentRowChange.add(
        operator=user,
        reason=PaymentRowChange.REFUND_REQUESTED,
        payment_row=payment_row,
    )
    execute_pending_refunds(payment_row.id)


def get_refund_request_status_settings(
    payment_type: PaymentTypeEnum,
    pos: POS,
) -> RefundRequestStatusSettings | None:
    if not pos:
        return

    if payment_type == PaymentTypeEnum.BOOKSY_PAY:
        return RefundRequestStatusSettings(
            auto_refund_delta=pos.booksy_pay_late_cancellation_window_or_default,
        )
