from uuid import UUID

from rest_framework import status
from rest_framework.exceptions import ValidationError as SerializerValidationError
from rest_framework.request import Request
from rest_framework.response import Response

from webapps.booking.v2.application.http_api.request.change_draft_staffer import (
    ChangeDraftStafferRequest,
)
from webapps.booking.v2.application.http_api.response.draft import DraftResponse
from webapps.booking.v2.application.http_api.view.base import BaseView
from webapps.booking.v2.application.http_api.view.utils import with_error_handling
from webapps.booking.v2.commons.errors import ValidationError


# pylint: disable=duplicate-code
class ChangeDraftStafferView(BaseView):
    serializer_class = ChangeDraftStafferRequest
    response_serializer_class = DraftResponse

    @with_error_handling
    def post(self, request: Request, draft_id: str) -> Response:
        user = self.user if self.user and not self.user.is_anonymous else None
        request_serializer = self.get_serializer(data=self.request.data)

        try:
            draft_id = UUID(draft_id)
        except ValueError as e:
            raise ValidationError from e

        try:
            request_serializer.is_valid(raise_exception=True)
        except SerializerValidationError as e:
            raise ValidationError from e

        version = request_serializer.validated_data['version']
        subbooking_id = request_serializer.validated_data['subbooking_id']
        staffer_id = request_serializer.validated_data['staffer_id']
        response_serializer = self._application_service.change_staffer(
            user=user,
            fingerprint=self.fingerprint,
            user_agent=self.user_agent,
            booking_source=self.booking_source,
            draft_id=draft_id,
            version=version,
            draft_item_id=subbooking_id,
            staffer_id=staffer_id,
            request=request,
        )

        return Response(
            status=status.HTTP_200_OK,
            data=response_serializer.data,
        )
