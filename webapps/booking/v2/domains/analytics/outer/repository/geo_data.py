from webapps.segment.models import ZipCodesToUrbanAreasMapper


class GeoDataRepository:
    def get_geo_data(self, zip_code: str | None) -> dict:
        empty_data = {'urban_area': None, 'urban_subarea': None}
        if not zip_code:
            return empty_data
        zip_code_mapping = (
            ZipCodesToUrbanAreasMapper.objects.filter(
                zip_code=zip_code,
            )
            .values('urban_area', 'urban_subarea')
            .first()
        )
        return zip_code_mapping or empty_data
