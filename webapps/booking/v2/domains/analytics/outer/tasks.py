from lib.celery_tools import segment_analytics_retry_post_transaction_task
from webapps.booking.v2.domains.analytics.outer.consts import CB_STARTED_FOR_CUSTOMER


@segment_analytics_retry_post_transaction_task
def send_draft_created_task(
    analytics,
    meta_properties,
) -> None:

    track_event_properties = _parse_track_event_properties(meta_properties)
    identify_event_properties = _parse_identify_event_properties(meta_properties)

    analytics.track(CB_STARTED_FOR_CUSTOMER, **track_event_properties)
    analytics.identify(**identify_event_properties)


def _parse_track_event_properties(meta_properties: dict) -> dict:
    _properties = {
        'properties': {},
        'context': {},
        'integrations': {},
    }
    for _property, property_item in _properties.items():
        if _val := meta_properties.get(_property):
            property_item.update(_val)
    return _properties


def _parse_identify_event_properties(meta_properties: dict) -> dict:
    _properties = {
        'traits': {},
    }
    for _property, property_item in _properties.items():
        if _val := meta_properties.get(_property):
            property_item.update(_val)
    return _properties
