from typing import Iterable
from domain_services.booking.src.domains.staffer.dto import Staffer
from domain_services.booking.src.domains.staffer.repository import StafferRepository
from webapps.business.enums import ResourceType
from webapps.business.models import Resource


class PostgresStafferRepository(StafferRepository):
    def get_staffer(self, staffer_id: int) -> Staffer | None:
        try:
            staffer = Resource.objects.prefetch_related(
                'photo',
            ).get(id=staffer_id, type=ResourceType.STAFF)
        except Resource.DoesNotExist:
            return None
        return self._as_staffer(staffer)

    def get_staffers(self, staffer_ids: Iterable[int]) -> list[Staffer]:
        return [
            self._as_staffer(staffer)
            for staffer in Resource.objects.prefetch_related(
                'photo',
            ).filter(
                id__in=staffer_ids,
                type=ResourceType.STAFF,
            )
        ]

    @staticmethod
    def _as_staffer(staffer: Resource) -> Staffer:
        return Staffer(
            id=staffer.id,
            name=staffer.name,
            photo_url=staffer.photo.image_url if staffer.photo else None,
        )
