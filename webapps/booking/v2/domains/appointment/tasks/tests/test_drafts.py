from freezegun import freeze_time
import pytest

from webapps.appointment_drafts.baker import draft_recipe
from webapps.appointment_drafts.models import DraftAppointment
from webapps.booking.v2.domains.appointment.tasks.drafts import remove_old_appointment_drafts


@pytest.mark.django_db
@freeze_time('2023-10-15')
def test_remove_old_appointment_drafts():
    with freeze_time('2023-10-10'):
        draft_recipe.make(_quantity=5)

    with freeze_time('2023-10-05'):
        draft_recipe.make(_quantity=5)

    assert DraftAppointment.objects.count() == 10
    remove_old_appointment_drafts()
    assert DraftAppointment.objects.count() == 5
