from datetime import datetime

import pytest
from model_bakery import baker
from pytz import UTC

from lib.test_utils import create_subbooking
from webapps.booking.tests.utils import create_appointment
from webapps.booking.enums import WhoMakesChange
from webapps.booking.models import (
    Appointment,
    BookingSources,
)
from webapps.business.models import Business
from webapps.pos.models import Transaction
from webapps.structure.models import Region
from webapps.user.models import User


@pytest.mark.django_db
def test_update_txn_charge_date_on_update_booking():
    user = baker.make(User)
    region = baker.make(Region, time_zone_name='Europe/London')
    business = baker.make(
        Business,
        owner=user,
        region=region,
        time_zone_name=region.time_zone_name,
    )
    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            booked_from=datetime(2011, 1, 1, 12, tzinfo=UTC),
            booked_till=datetime(2011, 1, 1, 12, tzinfo=UTC),
            updated_by=user,
        ),
    )
    appointment = booking.appointment

    txn = baker.make(Transaction, appointment_id=booking.appointment_id)
    assert txn.charge_date == datetime(2011, 1, 1, 12, tzinfo=UTC)

    appointment.update_appointment(
        subbooking=booking,
        updated_by=user,
        booked_till=datetime(2011, 1, 1, 13, tzinfo=UTC),
    )

    txn = Transaction.objects.get()
    assert txn.charge_date == datetime(2011, 1, 1, 13, tzinfo=UTC)


@pytest.mark.django_db
def test_update_txn_charge_date_on_make_booking():
    user = baker.make(User)
    business = baker.make(Business, owner=user)

    booking, *_ = create_subbooking(
        booking_kws=dict(
            business=business,
            booked_from=datetime(2011, 1, 1, 12, tzinfo=UTC),
            booked_till=datetime(2011, 1, 1, 12, tzinfo=UTC),
            updated_by=user,
        ),
    )
    appointment = booking.appointment

    txn = baker.make(Transaction, appointment_id=booking.appointment_id)
    assert txn.charge_date == datetime(2011, 1, 1, 12, tzinfo=UTC)

    booking.booked_till = datetime(2011, 1, 1, 13, tzinfo=UTC)
    appointment.make_appointment(
        [booking], who_makes_change=WhoMakesChange.BUSINESS, overbooking=True
    )

    txn = Transaction.objects.get()
    assert txn.charge_date == datetime(2011, 1, 1, 13, tzinfo=UTC)


@pytest.mark.django_db
def test_update_txn_charge_date_on_make_multibooking():
    user = baker.make(User)
    business = baker.make(Business, owner=user)
    source = baker.make(BookingSources)

    appointment = create_appointment(
        [
            dict(
                booked_from=datetime(2011, 1, 1, 11, tzinfo=UTC),
                booked_till=datetime(2011, 1, 1, 12, tzinfo=UTC),
            ),
            dict(
                booked_from=datetime(2011, 1, 1, 12, tzinfo=UTC),
                booked_till=datetime(2011, 1, 1, 13, tzinfo=UTC),
            ),
        ],
        business=business,
        source=source,
        updated_by=user,
        status=Appointment.STATUS.FINISHED,
    )
    booking1, booking2 = appointment.subbookings

    txn = baker.make(Transaction, appointment=appointment)
    assert txn.charge_date == datetime(2011, 1, 1, 13, tzinfo=UTC)

    booking2.booked_till = datetime(2011, 1, 1, 14, tzinfo=UTC)
    booking2.save(override=True)

    bking_list = [booking1, booking2]
    appointment.make_appointment(bking_list, [], WhoMakesChange.BUSINESS, False, user)

    txn = Transaction.objects.get()
    assert txn.charge_date == datetime(2011, 1, 1, 14, tzinfo=UTC)


@pytest.mark.django_db
def test_update_txn_charge_date_on_make_multibooking_delete_booking():
    user = baker.make(User)
    business = baker.make(Business, owner=user)
    source = baker.make(BookingSources)

    appointment = create_appointment(
        [
            dict(
                booked_from=datetime(2011, 1, 1, 11, tzinfo=UTC),
                booked_till=datetime(2011, 1, 1, 12, tzinfo=UTC),
            ),
            dict(
                booked_from=datetime(2011, 1, 1, 12, tzinfo=UTC),
                booked_till=datetime(2011, 1, 1, 13, tzinfo=UTC),
            ),
            dict(
                booked_from=datetime(2011, 1, 1, 13, tzinfo=UTC),
                booked_till=datetime(2011, 1, 1, 14, tzinfo=UTC),
            ),
        ],
        business=business,
        source=source,
        updated_by=user,
        status=Appointment.STATUS.FINISHED,
    )
    booking1, booking2, booking3 = appointment.subbookings

    txn = baker.make(Transaction, appointment=appointment)
    assert txn.charge_date == datetime(2011, 1, 1, 14, tzinfo=UTC)

    appointment.make_appointment(
        [booking1, booking2],
        subbooking_ids_to_delete=[booking3.id],
        who_makes_change=WhoMakesChange.BUSINESS,
        overbooking=False,
        updated_by=user,
    )

    txn = Transaction.objects.get()
    assert txn.charge_date == datetime(2011, 1, 1, 13, tzinfo=UTC)
