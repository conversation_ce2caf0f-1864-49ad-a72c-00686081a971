from datetime import datetime, timedelta

import pytest
from dateutil.tz import gettz

from webapps.booking.baker_recipes import (
    booking_recipe,
    booking_source_recipe,
    appointment_recipe,
)
from webapps.booking.models import Appointment
from webapps.business.baker_recipes import business_recipe


@pytest.mark.django_db
def test_localtime_filter():
    now = datetime(2019, 12, 1, 22, 0, tzinfo=gettz('UTC'))
    source = booking_source_recipe.make()

    tzname_a = 'Europe/Warsaw'
    tz_a = gettz(tzname_a)
    assert now.astimezone(tz_a).date() == now.date()

    business_a = business_recipe.make(
        time_zone_name=tzname_a,
    )
    appointment_a = appointment_recipe.make(
        business=business_a,
        source=source,
        updated_by=business_a.owner,
        booked_from=now,
        booked_till=now + timedelta(minutes=30),
    )
    booking_a = booking_recipe.prepare(
        appointment=appointment_a,
        booked_from=now,
        booked_till=now + timedelta(minutes=30),
    )
    booking_a.save(override=True)

    tzname_b = 'Europe/Minsk'
    tz_b = gettz(tzname_b)
    assert now.astimezone(tz_b).date() != now.date()

    business_b = business_recipe.make(
        time_zone_name=tzname_b,
    )
    appointment_b = appointment_recipe.make(
        business=business_b,
        source=source,
        updated_by=business_b.owner,
        booked_from=now,
        booked_till=now + timedelta(minutes=30),
    )
    booking_b = booking_recipe.prepare(
        appointment=appointment_b,
        booked_from=now,
        booked_till=now + timedelta(minutes=30),
    )
    booking_b.save(override=True)

    appointments = Appointment.objects.localtime_filter(
        business__in=[business_a, business_b],
        booked_from__gte=datetime(2019, 12, 1, 0, 0),
        booked_from__lte=datetime(2019, 12, 1, 23, 59),
    )
    assert appointment_a in appointments
    assert appointment_b not in appointments
