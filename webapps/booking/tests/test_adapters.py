import pytest
from model_bakery import baker

from webapps.booking.adapters import validate_api_key
from webapps.booking.models import BookingSources


@pytest.mark.django_db
@pytest.mark.parametrize(
    'api_key, path, invalid_api_key_desired_value',
    [
        ('wrong_key', '/customer_api/me', True),
        ('universal_api_key', '/customer_api/me', False),
        ('universal_api_key', '/business_api/me', False),
        ('business_api_key', '/customer_api/me', True),
        ('business_api_key', '/business_api/me', False),
    ],
)
def test_validate_api_key(
    api_key,
    path,
    invalid_api_key_desired_value,
):
    bake_sources()
    invalid_api_key, source = validate_api_key(
        api_key=api_key,
        request_path=path,
        api_key_required=True,
    )

    source_from_api_key = BookingSources.objects.filter(api_key=api_key).last()

    assert invalid_api_key is invalid_api_key_desired_value
    assert source == source_from_api_key


def bake_sources():
    baker.make(
        BookingSources,
        api_key='universal_api_key',
        app_type=BookingSources.UNIVERSAL_APP,
        name='Universal',
    )
    baker.make(
        BookingSources,
        api_key='business_api_key',
        app_type=BookingSources.BUSINESS_APP,
        name='Business',
    )
