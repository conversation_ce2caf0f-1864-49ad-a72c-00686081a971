from webapps.booking.tests.utils import create_appointment
from webapps.booking.tools.repeating import get_repeating_booking_conflicts


def test_repeating_booking_conflicts(service_variant, staffer, get_booked_time, business):
    # conflict appointment
    create_appointment(
        [
            dict(
                booked_from=get_booked_time(10, 30),
                booked_till=get_booked_time(11),
                staffer=staffer,
            )
        ],
        business=business,
    )
    repeating_appointments = [
        # conflict with booking
        create_appointment(
            [
                dict(
                    service_variant=service_variant,
                    staffer=staffer,
                    booked_from=get_booked_time(10),
                    booked_till=get_booked_time(11),
                )
            ],
            business=business,
        ),
        # conflict with working hours
        create_appointment(
            [
                dict(
                    service_variant=service_variant,
                    staffer=staffer,
                    booked_from=get_booked_time(9),
                    booked_till=get_booked_time(10, 30),
                )
            ],
            business=business,
        ),
    ]
    _business, conflicts = get_repeating_booking_conflicts(
        [booking.id for appt in repeating_appointments for booking in appt.subbookings]
    )
    assert conflicts[staffer.id] == [
        (get_booked_time(9), get_booked_time(10)),
        (get_booked_time(10, 30), get_booked_time(11)),
    ]
