from dataclasses import dataclass

from webapps.booking.models import Appointment
from webapps.booking.no_show_protection.business_appointment.business_appointment_can_have_deposit_validator import (  # pylint: disable=line-too-long
    BusinessAppointmentCanHaveDepositValidationRules,
    BusinessAppointmentCanHaveDepositValidator,
)
from webapps.booking.no_show_protection.business_appointment.validation_rules.appointment_in_the_future import (  # pylint: disable=line-too-long
    AppointmentInTheFuture,
)
from webapps.booking.no_show_protection.business_appointment.validation_rules.appointment_is_not_repeating import (  # pylint: disable=line-too-long
    AppointmentIsNotRepeating,
)
from webapps.booking.no_show_protection.business_appointment.validation_rules.business_appointment_deposit_feature_enabled import (  # pylint: disable=line-too-long
    BusinessAppointmentDepositFeatureEnabled,
)
from webapps.booking.no_show_protection.business_appointment.validation_rules.business_can_pay_deposit import (  # pylint: disable=line-too-long
    BusinessCanPayDeposit,
)
from webapps.business.adapters import BookingModeAdapter
from webapps.booking.no_show_protection.business_appointment.validation_rules.sub_bookings_at_least_one_price_set import (  # pylint: disable=line-too-long
    SubbookingsAtLeastOneServicePriceSet,
)


@dataclass
class BusinessAppointmentCanHaveDepositValidatorFactoryParams:
    business_id: int
    appointment: Appointment


class BusinessAppointmentCanHaveDepositValidatorFactory:
    @staticmethod
    def make(
        params: BusinessAppointmentCanHaveDepositValidatorFactoryParams,
    ) -> BusinessAppointmentCanHaveDepositValidator:
        validation_rules = BusinessAppointmentCanHaveDepositValidationRules(
            business_can_pay_deposit=BusinessCanPayDeposit(params.business_id),
            subbookings_at_least_one_price_set=SubbookingsAtLeastOneServicePriceSet(
                params.appointment.subbookings
            ),
            appointment_is_not_repeating=AppointmentIsNotRepeating(params.appointment),
            appointment_in_the_future=AppointmentInTheFuture(params.appointment),
            business_appointment_deposit_feature_enabled=BusinessAppointmentDepositFeatureEnabled(
                params.business_id
            ),
        )
        return BusinessAppointmentCanHaveDepositValidator(validation_rules)
