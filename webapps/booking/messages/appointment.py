from rest_framework import serializers
from django_socio_grpc.proto_serializers import ProtoSerializer

from booksy.pubsub.appointment.appointment_pb2 import (  # pylint: disable=no-name-in-module
    AppointmentChanged,
)
from booksy.types_pb2 import (  # pylint: disable=no-name-in-module
    ServicePrice_deprecated,
)
from webapps.pubsub.base import TopicOptions
from webapps.pubsub.message import Message

from lib.protobuf.fields import (
    DeprecatedTimestampDateTimeField,
)


class DeprecatedServicePriceSerializer(ProtoSerializer):
    class Meta:
        proto_class = ServicePrice_deprecated

    value = serializers.CharField()
    price_type = serializers.CharField()
    discount = serializers.CharField()

    def to_representation(self, instance):
        from webapps.booking.appointment_wrapper import AppointmentWrapper

        if self.root.instance and self.root.instance.business.pos:
            instance = AppointmentWrapper(self.root.instance.subbookings).total
        return super().to_representation(instance)


class AppointmentChangedSerializer(ProtoSerializer):
    class Meta:
        proto_class = AppointmentChanged

    booked_for_id = serializers.IntegerField()
    appointment_id = serializers.IntegerField(source='id')
    total_value = serializers.CharField()
    total = DeprecatedServicePriceSerializer()
    booked_from = DeprecatedTimestampDateTimeField()
    business_id = serializers.IntegerField()
    previous_status = serializers.SerializerMethodField()

    def get_previous_status(self, _):
        return self.context.get('prev_status', None)


class AppointmentStatusSerializer(serializers.Serializer):
    status = serializers.CharField()


class AppointmentChangedMessage(Message):
    topic_proto_class = AppointmentChanged
    topic_options = TopicOptions(by_country=True)
    serializer = AppointmentChangedSerializer
    attributes_serializer = AppointmentStatusSerializer
