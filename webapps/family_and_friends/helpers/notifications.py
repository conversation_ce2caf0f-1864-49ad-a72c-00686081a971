import datetime
from functools import partial

from lib.tools import tznow
from webapps.family_and_friends.events import send_push_with_invitation
from webapps.pop_up_notification.enums import FamilyAndFriendsInvitationResponseType
from webapps.pop_up_notification.models import (
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsUnlinkNotification,
)


def add_invite_notification(matched_user: 'User', invitation: 'MemberInvitation'):
    params = dict(
        user=matched_user,
        notification_type=(
            FamilyAndFriendsInvitationNotification.TYPE_FAMILY_AND_FRIENDS_INVITATION
        ),
        valid_till=invitation.valid_till,
        parent=invitation.parent,
        key=invitation.key,
    )
    notification = FamilyAndFriendsInvitationNotification.objects.filter(**params).first()
    if notification:
        return
    notification = FamilyAndFriendsInvitationNotification(**params)
    notification.save()
    send_push_with_invitation.send(
        invitation,
        notification_id=notification.id,
        user_id=matched_user.id,
    )


def add_invitation_response_notification(invitation: 'MemberInvitation', invitation_status: str):
    notification_type = (
        FamilyAndFriendsInvitationResponseNotification.TYPE_FAMILY_AND_FRIENDS_INVITATION_RESPONSE
    )
    notification = FamilyAndFriendsInvitationResponseNotification(
        user=invitation.parent.user_profile.user,
        notification_type=notification_type,
        valid_since=tznow(),
        valid_till=tznow() + datetime.timedelta(days=365 * 5),
        member=invitation.member,
        invitation_status=invitation_status,
    )
    notification.save()


add_invitation_accepted_notification = partial(
    add_invitation_response_notification,
    invitation_status=FamilyAndFriendsInvitationResponseType.ACCEPTED,
)


add_invitation_rejected_notification = partial(
    add_invitation_response_notification,
    invitation_status=FamilyAndFriendsInvitationResponseType.REJECTED,
)


add_invitation_expired_notification = partial(
    add_invitation_response_notification,
    invitation_status=FamilyAndFriendsInvitationResponseType.EXPIRED,
)


def add_unlink_notification(trigger_profile: 'MemberProfile', unlinked_profile: 'MemberProfile'):
    if not unlinked_profile.is_booksy_user:
        return

    notification = FamilyAndFriendsUnlinkNotification(
        user=unlinked_profile.user_profile.user,
        notification_type=FamilyAndFriendsUnlinkNotification.TYPE_FAMILY_AND_FRIENDS_UNLINK,
        valid_since=tznow(),
        valid_till=tznow() + datetime.timedelta(days=365 * 5),
        trigger_profile=trigger_profile,
        unlinked_profile=unlinked_profile,
    )
    notification.save()
