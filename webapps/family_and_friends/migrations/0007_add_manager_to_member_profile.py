# Generated by Django 3.2.7 on 2021-10-12 09:57

from django.db import migrations
import lib.models
import webapps.family_and_friends.models


class Migration(migrations.Migration):

    dependencies = [
        ('family_and_friends', '0006_add_member_invitation'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='memberprofile',
            managers=[
                ('objects', webapps.family_and_friends.models.MemberProfileManager()),
            ],
        ),
        migrations.AlterModelManagers(
            name='memberrelations',
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
