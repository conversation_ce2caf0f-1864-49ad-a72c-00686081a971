import pytest

from webapps.family_and_friends.enums import RelationshipType


@pytest.mark.parametrize(
    'member_side, expected_parent_side',
    (
        pytest.param(RelationshipType.FRIEND, 'Friend', id='Friend for friend'),
        pytest.param(RelationshipType.CHILD, 'Parent', id='Parent for child'),
        pytest.param(RelationshipType.PET, 'Pet Owner', id='Pet Owner for Pet'),
    ),
)
def test_relations_mapping(member_side, expected_parent_side):
    assert RelationshipType.get_for_parent(member_side).label == expected_parent_side
