from datetime import datetime

import pytz
from django.test import TestCase
from freezegun import freeze_time

from webapps.booking.enums import AppointmentType, AppointmentStatus
from webapps.booking.models import Appointment
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.tests.utils import (
    create_boost_finished_visit,
    create_potentially_boost_appointment,
)
from webapps.marketplace.tasks import set_chargeable_task


class TestBoostChargingForBusinessBookings(TestCase):
    TODAY = (2024, 6, 5)

    @classmethod
    def today_at(cls, hour, minute=0):
        return datetime(*cls.TODAY, hour=hour, minute=minute, tzinfo=pytz.UTC)

    @classmethod
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make()


class CommonTestsInCheatedScenarioMixin:
    business: 'Business'
    originally_first_appointment: 'Appointment'
    cheated_business_appointment: 'Appointment'
    bci: 'BusinessCustomerInfo'

    def test_correct_selection_of_originally_first_appointment(self):
        originally_first_appointment = self.bci.originally_first_appointment
        self.assertEqual(originally_first_appointment, self.originally_first_appointment)

    def test_correct_first_appointment(self):
        self.assertEqual(self.bci.first_appointment.id, self.cheated_business_appointment.id)

    def test_business_booking_chargeable_and_payable(self):
        self.assertEqual(self.cheated_business_appointment.chargeable, True)
        self.assertEqual(self.cheated_business_appointment.payable, True)

    def test_customer_booking_is_no_longer_chargeable_nor_payable(self):
        self.assertEqual(self.originally_first_appointment.chargeable, False)
        self.assertEqual(self.originally_first_appointment.payable, False)


class BoostDetailsTestsMixin:
    business: 'Business'
    originally_first_appointment: 'Appointment'
    cheated_business_appointment: 'Appointment'
    bci: 'BusinessCustomerInfo'

    def test_business_booking_displayed_in_boost_details_as_new_boost_appt(self):
        new_boost_appointments = Appointment.objects.filter_merchants_new_boost_appointments(
            self.business
        )
        self.assertEqual(new_boost_appointments.count(), 1)

        new_boost_appt = new_boost_appointments[0]
        self.assertEqual(new_boost_appt.id, self.cheated_business_appointment.id)

    def test_customer_booking_displayed_in_boost_details_as_returning_boost_appt(self):
        returning_appointments_of_boost_clients = (
            Appointment.objects.filter_returning_appointments_of_boost_clients(self.business)
        )
        self.assertEqual(returning_appointments_of_boost_clients.count(), 1)

        returning_appt = returning_appointments_of_boost_clients[0]
        self.assertEqual(returning_appt.id, self.originally_first_appointment.id)

    def test_both_bookings_taken_into_boost_profit(self):
        all_finished_appointments_of_boost_clients = (
            Appointment.objects.filter_all_finished_appointments_of_boost_clients(self.business)
        )
        self.assertEqual(all_finished_appointments_of_boost_clients.count(), 2)


class TestBBCreatedRightBeforeCB(
    TestBoostChargingForBusinessBookings, CommonTestsInCheatedScenarioMixin, BoostDetailsTestsMixin
):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # simulating that the client had a visit today, already finished
        with freeze_time(cls.today_at(15)):
            cls.originally_first_appointment = create_boost_finished_visit(
                business=cls.business,
                subbookings=[
                    {
                        'booked_from': cls.today_at(14),
                        'booked_till': cls.today_at(14, 30),
                    },
                ],
            )

        cls.bci = cls.originally_first_appointment.booked_for

        # later the Px tried to cheat by creating the BB right before the CB
        with freeze_time(cls.today_at(16)):
            cls.cheated_business_appointment = create_potentially_boost_appointment(
                bci=cls.bci,
                subbookings=[
                    {
                        'booked_from': cls.today_at(13, 30),
                        'booked_till': cls.today_at(14),
                    },
                ],
                source_chargeable=False,
                type=AppointmentType.BUSINESS,
            )

            set_chargeable_task.run(cls.cheated_business_appointment.id)

        cls.originally_first_appointment.refresh_from_db()
        cls.cheated_business_appointment.refresh_from_db()
        cls.bci.refresh_from_db()


class TestBBMovedRightBeforeCB(
    TestBoostChargingForBusinessBookings, CommonTestsInCheatedScenarioMixin, BoostDetailsTestsMixin
):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # simulating that the client had a visit today, already finished
        with freeze_time(cls.today_at(15)):
            cls.originally_first_appointment = create_boost_finished_visit(
                business=cls.business,
                subbookings=[
                    {
                        'booked_from': cls.today_at(14),
                        'booked_till': cls.today_at(14, 30),
                    },
                ],
            )

        cls.bci = cls.originally_first_appointment.booked_for

        # later the Px tried to cheat by:
        # first - they create the BB
        with freeze_time(cls.today_at(16)):
            cls.cheated_business_appointment = create_potentially_boost_appointment(
                bci=cls.bci,
                subbookings=[
                    {
                        'booked_from': cls.today_at(16),
                        'booked_till': cls.today_at(16, 30),
                    },
                ],
                source_chargeable=False,
                type=AppointmentType.BUSINESS,
                status=AppointmentStatus.ACCEPTED,
            )

            set_chargeable_task.run(cls.cheated_business_appointment.id)

        # second - they move it before the CB
        with freeze_time(cls.today_at(16, 15)):
            Appointment.objects.filter(id=cls.cheated_business_appointment.id).update(
                booked_from=cls.today_at(13, 30),
                booked_till=cls.today_at(14),
                status=AppointmentStatus.FINISHED,
            )
            set_chargeable_task.run(cls.cheated_business_appointment.id)

        cls.originally_first_appointment.refresh_from_db()
        cls.cheated_business_appointment.refresh_from_db()
        cls.bci.refresh_from_db()
