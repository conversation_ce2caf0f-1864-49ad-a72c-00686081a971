from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('profile_completeness', '0002_auto_20210824_1026'),
    ]

    operations = [
        migrations.RenameModel(
            'BusinessTierStep',
            'CompletedStep',
        ),
        migrations.RenameModel(
            'BusinessCompletedTier',
            'CompletedTier',
        ),
        migrations.RenameModel(
            'BusinessTierReward',
            'Reward',
        ),
        migrations.AlterModelTable(
            name='CompletedStep',
            table='profile_completeness_businesstierstep',
        ),
        migrations.AlterModelTable(
            name='CompletedTier',
            table='profile_completeness_businesscompletedtier',
        ),
        migrations.AlterModelTable(
            name='Reward',
            table='profile_completeness_businesstierreward',
        ),
        migrations.AlterModelManagers(
            name='CompletedTier',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.AlterModelManagers(
            name='Reward',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.AlterModelManagers(
            name='CompletedStep',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.AlterModelManagers(
            name='ProfileCompleteness',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.AlterField(
            model_name='completedstep',
            name='tier_level',
            field=models.CharField(
                choices=[
                    ('1', 'Novice'),
                    ('2', 'Apprentice'),
                    ('3', 'Associate'),
                    ('4', 'Master'),
                    ('5', 'Influencer'),
                ],
                db_column='tier_level',
                max_length=1,
            ),
        ),
    ]
