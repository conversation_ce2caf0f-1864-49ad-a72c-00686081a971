from lib.celery_tools import celery_task
from webapps.admin_extra.commodities import load_wholesaler_commodities_without_spec
from webapps.admin_extra.import_utils import remove_admin_file_log_error
from webapps.warehouse.tasks import (
    LOGGER,
    import_wholesaler_commodities_task,
)


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def wholesaler_commodities_file_import_task(wholesaler_id, email, file_path):
    """
    Import data from Wholesaler using file upload.

    :param wholesaler_id:
    :param email: <EMAIL>
    :param file_path: path to the file on S3
    :return:
    """

    try:
        # load customers list of dicts
        commodities, *_ = load_wholesaler_commodities_without_spec(
            file_path,
            wholesaler_id,
        )
        import_wholesaler_commodities_task.delay(
            wholesaler_id,
            commodities,
            email,
        )
    except Exception:
        LOGGER.exception('Parse and import wholesaler commodities. Wholesaler: %id', wholesaler_id)
        remove_admin_file_log_error(file_path)
        raise
    else:
        remove_admin_file_log_error(file_path)
