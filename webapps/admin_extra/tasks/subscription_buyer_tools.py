import datetime
import io
import logging
from itertools import chain

from rest_framework.exceptions import ValidationError

import pandas as pd
import numpy as np

from lib.celery_tools import (
    celery_task,
)
from lib.email import (
    send_email,
)
from lib.fields.tax_number_field import TaxNumberFieldSerializer
from lib.enums import BaseEnum
from lib.tools import (
    tznow,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.models import Business
from webapps.purchase.models import SubscriptionBuyer
from webapps.purchase.serializers.subscription_buyer import (
    SubscriptionBuyerImporterSerializer,
    SubscriptionBuyerCSVSerializer,
)
from webapps.user.models import User

admin_logger = logging.getLogger('booksy.admin_extra')


class InvoiceAddressFields(BaseEnum):
    ADDRESS_DETAILS1 = 'address_details1'
    CITY = 'city'
    ZIPCODE = 'zipcode'


class SubscriptionBuyerFields(BaseEnum):
    BUYER_ID = 'buyer_id'
    TAX_ID = 'tax_id'
    ENTITY_NAME = 'entity_name'
    INVOICE_EMAIL = 'invoice_email'
    BATCH_INVOICES = 'batch_invoices'
    ACTIVE = 'active'
    IS_VERIFIED = 'is_verified'
    BUSINESSES = 'businesses'
    INVOICING_ALLOWED = 'invoicing_allowed'
    INVOICING_EXCLUSION_REASON = 'invoicing_exclusion_reason'
    TAX_GROUP_ID = 'tax_group_id'


CSV_READ_FIELDS = list(SubscriptionBuyerFields.values()) + list(InvoiceAddressFields.values())
CSV_WRITE_FIELDS = CSV_READ_FIELDS + ['reason', 'result']


def prepare_data(validated_data):
    data = {}
    invoice_address = {}
    for field, value in validated_data.items():
        if field in InvoiceAddressFields.values():
            invoice_address[field] = value
        elif field in {'business_id', *SubscriptionBuyerFields.values()}:
            data[field] = value

    if invoice_address:
        data['invoice_address'] = invoice_address

    return data


class CSVLoader:
    tax_id_field = TaxNumberFieldSerializer(allow_blank=False, allow_null=True)

    EMPTY_VALUE = 'EMPTY'

    VALUES_MAP = {
        EMPTY_VALUE: None,
    }

    @classmethod
    def serialize_tax_id(cls, value):
        if not value or pd.isna(value):
            return None
        if value == cls.EMPTY_VALUE:
            return value
        try:
            return cls.tax_id_field.run_validation(value)
        except ValidationError as err:
            raise ValidationError(
                f'The attempt to process the value you passed: {value}'
                f' resulted in the following error: {err.args[0][0][:]}.'
            ) from err

    @classmethod
    def serialize_additional_business_ids(cls, value):
        if not value or pd.isna(value):
            return None
        if value == cls.EMPTY_VALUE:
            return ()
        return tuple(sorted({int(e.strip()) for e in value.split(";")}))

    @classmethod
    def read_csv(cls, filepath_or_buffer):
        return pd.read_csv(
            filepath_or_buffer,
            dtype={
                SubscriptionBuyerFields.TAX_ID.value: str,
                SubscriptionBuyerFields.BUYER_ID.value: int,
                SubscriptionBuyerFields.BUSINESSES.value: str,
            },
        )

    @classmethod
    def _drop_nan_values(cls, records):
        return dict(
            (index, {k: cls.VALUES_MAP.get(v, v) for k, v in values.items() if not pd.isna(v)})
            for (index, values) in records
        )

    @staticmethod
    def validate_unique_ids(data_frame):
        ids = list(chain(*data_frame[SubscriptionBuyerFields.BUSINESSES.value].dropna()))

        unique_ids = set(ids)
        duplicates = set()
        for _id in ids:
            try:
                unique_ids.remove(_id)
            except KeyError:
                duplicates.add(_id)

        if duplicates:
            raise ValueError(
                f'Ids in "businesses" have to be unique along CSV: {sorted(duplicates)}'
            )

    @classmethod
    def clean_data_frame(cls, data_frame):
        data_frame[SubscriptionBuyerFields.TAX_ID.value] = data_frame[
            SubscriptionBuyerFields.TAX_ID.value
        ].map(cls.serialize_tax_id)

        data_frame[SubscriptionBuyerFields.BUSINESSES.value] = data_frame[
            SubscriptionBuyerFields.BUSINESSES.value
        ].map(cls.serialize_additional_business_ids)

        cls.validate_unique_ids(data_frame)

        try:
            data_frame.set_index(
                [SubscriptionBuyerFields.BUYER_ID.value], inplace=True, verify_integrity=True
            )
        except ValueError as err:
            raise ValueError(f'"buyer_id" values have to be unique: {err}') from err
        data_frame = data_frame.dropna(axis=0, how='all')
        data_frame['result'] = "not processed"
        data_frame['reason'] = 'SubscriptionBuyer not found'
        data_frame[SubscriptionBuyerFields.BUYER_ID.value] = data_frame.index

        records = data_frame.to_dict(orient='index').items()
        return cls._drop_nan_values(records)

    @classmethod
    def load_records(cls, file_path_or_buffer):
        data_frame = cls.read_csv(file_path_or_buffer)
        return cls.clean_data_frame(data_frame)


class S3CSVLoader(CSVLoader):
    @classmethod
    def load_records(cls, file_path_or_buffer):  # pylint: disable=arguments-differ
        from webapps.admin_extra.admin_import_s3 import AdminImporterS3

        with AdminImporterS3.open_file(file_path_or_buffer) as file:
            data_frame = cls.read_csv(file)
        return cls.clean_data_frame(data_frame)


def get_actual_business_buyer_assigned(input_records):
    all_business_ids = set()
    for _, row in input_records.items():
        all_business_ids.update(row.get(SubscriptionBuyerFields.BUSINESSES.value, ()))

    return dict(
        Business.objects.filter(id__in=all_business_ids)
        .distinct('id')
        .values_list('id', 'buyer_id')
    )


@celery_task
def batch_update_subscription_buyers_task(file_path, email, user_id):
    loading_dt = datetime.datetime.now().strftime('%d_%m_%Y_%H_%M_%S')
    try:
        input_records = S3CSVLoader.load_records(file_path)
    except Exception as err:  # pylint: disable=broad-except
        send_email(
            email,
            f'{err.args[0][:]} No report was generated.',
            subject=f'Bulk update subscription buyers - error loading file {loading_dt}',
            omit_celery=False,
        )
        return

    admin_logger.info("Start processing %s records", len(input_records))
    operator = User.objects.get(id=user_id)
    additional_info = {
        'verified_by': operator,
        'verified_at': tznow(),
    }

    buyers_qs = SubscriptionBuyer.objects.filter(id__in=input_records.keys())

    changed_business_ids = []
    old_obj_vars = {}
    new_obj_vars = {}
    business_to_buyer_actual_map = get_actual_business_buyer_assigned(input_records)

    for buyer in buyers_qs.iterator():
        row = input_records.get(buyer.id, {})

        is_verified = row.get(SubscriptionBuyerFields.IS_VERIFIED.value)

        data = prepare_data(row)
        # we update verification info only if it wasn't there before
        if not buyer.verified_by and is_verified:
            data.update(additional_info)

        serializer = SubscriptionBuyerImporterSerializer(
            instance=buyer,
            data=data,
            partial=True,
        )
        if not serializer.is_valid():
            result = 'failed'
            reason = serializer.errors
        else:
            serializer.save()
            reason = None
            result = 'success'
            additional_business_ids = row.get(SubscriptionBuyerFields.BUSINESSES.value, ())
            Business.objects.filter(id__in=additional_business_ids).update(buyer_id=buyer.id)
            changed_business_ids.extend(additional_business_ids)
            for _id in additional_business_ids:
                old_obj_vars[_id] = {'buyer_id': business_to_buyer_actual_map[_id]}
                new_obj_vars[_id] = {'buyer_id': buyer.id}

        row.update(
            {
                "result": result,
                "reason": reason,
            }
        )

    BusinessChange.bulk_change(
        business_ids=changed_business_ids,
        old_obj_vars=old_obj_vars,
        new_obj_vars=new_obj_vars,
        operator=operator,
        metadata={
            'task': 'batch_update_subscription_buyers_task',
        },
    )

    df_output = pd.DataFrame.from_dict(
        input_records,
        orient='index',
        columns=CSV_WRITE_FIELDS,
    )
    df_output = df_output.replace(np.nan, 'NULL')

    writer = io.BytesIO()
    writer.write(df_output.to_csv(index=False).encode())
    writer.seek(0)

    # Send the same data back, with additional status ("result")
    # and error ("reason") columns
    admin_logger.info("Sending email with report.")
    send_email(
        email,
        'Bulk update subscription buyers report in attachment',
        subject=f'Bulk update subscription buyers - report {loading_dt}',
        attachments=[
            (f'bulk_update_subscription_buyers_{loading_dt}.csv', writer.getvalue(), 'text/csv')
        ],
        omit_celery=False,
    )


def mark_serialization_errors(records, errors):
    for record, row_errors in zip(records, errors):
        for field, error in row_errors.items():
            if field in record:
                value = record[field]
                record[field] = f'{value} - {error}'
            else:
                record['errors'] = f'{record.get("errors", "")}\n{error}'


@celery_task
def import_subscription_buyers_task(file_path, email, user_id):
    from webapps.admin_extra.admin_import_s3 import AdminImporterS3

    loading_dt = datetime.datetime.now().strftime('%d_%m_%Y_%H_%M_%S')
    try:
        with AdminImporterS3.open_file(file_path) as file:
            input_records = pd.read_csv(
                file,
                dtype={
                    'entity_name': pd.StringDtype(),
                    'invoice_emails': pd.StringDtype(),
                    'batch_invoices': pd.BooleanDtype(),
                    'is_verified': pd.BooleanDtype(),
                    'tax_id': pd.StringDtype(),
                    'businesses': pd.StringDtype(),
                    'subscriptions': pd.StringDtype(),
                    'address_details1': pd.StringDtype(),
                    'city': pd.StringDtype(),
                    'zipcode': pd.StringDtype(),
                    'state': pd.StringDtype(),
                    'invoicing_allowed': pd.BooleanDtype(),
                    'invoicing_exclusion_reason': pd.StringDtype(),
                    'tax_group_id': pd.StringDtype(),
                },
                converters={
                    'invoice_emails': lambda string: string.split(',') if string else None,
                    'businesses': lambda string: string.split(',') if string else None,
                    'subscriptions': lambda string: string.split(',') if string else None,
                },
            ).replace({np.nan: None})
    except Exception as err:  # pylint: disable=broad-except
        send_email(
            email,
            f'Import subscription buyers error {err}',
            subject=f'Import subscription buyers - error loading file {loading_dt}',
            omit_celery=False,
        )
        raise

    data = input_records.to_dict('records')
    for row in data:
        if row['subscriptions'] is None:
            row.pop('subscriptions')

    operator = User.objects.get(id=user_id)
    serializer = SubscriptionBuyerCSVSerializer(
        data=data,
        context={
            'operator': operator,
            'metadata': {
                'task': 'webapps.admin_extra.tasks'
                '.subscription_buyer_tools.import_subscription_buyers_task'
            },
        },
        many=True,
    )

    if not serializer.is_valid():
        mark_serialization_errors(data, serializer.errors)

        email_body = 'Import subscription buyers error'
        email_subject = f'Import subscription buyers error - report {loading_dt}'
        attachment_file_name = f'import_subscription_buyers_errors_{loading_dt}.csv'
    else:
        email_body = 'Import subscription buyers report'
        email_subject = f'Import subscription buyers - report {loading_dt}'
        attachment_file_name = f'import_subscription_buyers_{loading_dt}.csv'

        serializer.save()

    data_frame = pd.DataFrame(data)

    writer = io.BytesIO()
    writer.write(data_frame.to_csv(index=False).encode())
    writer.seek(0)

    send_email(
        email,
        email_body,
        subject=email_subject,
        attachments=[
            (
                attachment_file_name,
                writer.getvalue(),
                'text/csv',
            )
        ],
        omit_celery=False,
    )
