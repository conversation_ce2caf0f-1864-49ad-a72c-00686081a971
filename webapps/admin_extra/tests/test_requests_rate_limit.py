import mock
import pytest
from django.urls.base import reverse

from service.tests import get_cache_format, clear_cache_format
from webapps.admin_extra.tests import DjangoTestCase


@pytest.mark.random_failure  # https://booksy.atlassian.net/browse/PY-195
class TestRequestsRateLimitAdmin(DjangoTestCase):
    @mock.patch(
        'service.mixins.throttling.BooksyAdminRateThrottle.get_rate', return_value='3/minute'
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.BooksyRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    def test_no_rate_limit_on_login_page(self, cache_format_mock, ip_mock, rate_mock):
        with self.modify_settings(
            MIDDLEWARE={
                'append': [
                    'webapps.admin_extra.middleware.rate_limits_middleware',
                ],
            }
        ):
            url = reverse('admin:login')
            assert self.client.get(url).status_code == 200
            assert self.client.get(url).status_code == 200
            assert self.client.get(url).status_code == 200
            assert self.client.get(url).status_code == 429
            clear_cache_format(cache_format_mock)

    @mock.patch(
        'service.mixins.throttling.BooksyAdminRateThrottle.get_rate', return_value='2/minute'
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.BooksyAdminRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    def test_rate_limit_after_login(self, cache_format_mock, ip_mock, rate_mock):
        with self.modify_settings(
            MIDDLEWARE={
                'append': [
                    'webapps.admin_extra.middleware.rate_limits_middleware',
                ],
            }
        ):
            self.login_admin()  # pylint: disable=no-value-for-parameter
            url_0 = reverse('admin:index')
            assert self.client.get(url_0).status_code == 200
            url_1 = reverse('admin:businesses_work_schedule')
            assert self.client.get(url_1).status_code == 429
            # this view is not limited
            url_2 = reverse('admin:jsi18n')
            assert self.client.get(url_2).status_code == 429
            clear_cache_format(cache_format_mock)
