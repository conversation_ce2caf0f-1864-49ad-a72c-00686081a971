from django import forms
from django.utils.translation import gettext_lazy as _
from lib.payment_gateway.entities import WalletEntity
from lib.spreadsheet import load_spreadsheet_with_pandas

from webapps.admin_extra.import_utils import strip_xlsx_data
from webapps.business.models import Business
from webapps.payment_gateway.ports import PaymentGatewayPort


class StripeManualTransferForm(forms.Form):
    source_business_id = forms.IntegerField(
        label='Source business id',
        help_text=_('Type -1 for Booksy Account'),
    )
    destination_business_id = forms.IntegerField(
        label='Destination business id',
        help_text=_('Type -1 for Booksy Account'),
    )
    amount = forms.IntegerField(
        required=True,
        min_value=0,
        initial=0,
        help_text='In minor unit (ie. 1.2 USD is 120)',
    )
    reference_text = forms.CharField(
        help_text=_('Description of the transfer for the source account'),
        required=False,
        max_length=256,
        widget=forms.Textarea(attrs={'rows': 3}),
    )

    def clean_source_business_id(self):
        business_id = self.cleaned_data.get('source_business_id')
        if business_id is None:
            raise forms.ValidationError('Source business id is required')

        if business_id != -1:
            try:
                Business.objects.get(id=business_id)
            except Business.DoesNotExist:
                raise forms.ValidationError(  # pylint: disable=raise-missing-from
                    f"Source business with id={business_id} doesn't exist. "
                    f"Provide valid business id."
                )

        return business_id

    def clean_destination_business_id(self):
        business_id = self.cleaned_data.get('destination_business_id')
        if business_id is None:
            raise forms.ValidationError('Destination business id is required')

        if business_id != -1:
            try:
                Business.objects.get(id=business_id)
            except Business.DoesNotExist:
                raise forms.ValidationError(  # pylint: disable=raise-missing-from
                    f"Destination business with id={business_id} doesn't exist. "
                    f"Provide valid business id."
                )

        return business_id

    def clean(self):
        cleaned_data = super().clean()
        if self.errors:
            return cleaned_data

        source_business_id = cleaned_data['source_business_id']
        destination_business_id = cleaned_data['destination_business_id']

        if source_business_id != -1 and destination_business_id != -1:
            raise forms.ValidationError('Cannot transfer funds between Booksy <-> Booksy')

        source_wallet: WalletEntity
        if source_business_id == -1:
            source_wallet = PaymentGatewayPort.get_booksy_wallet()
        else:
            source_wallet = PaymentGatewayPort.get_business_wallet(
                business_id=source_business_id,
            )
        if not source_wallet:
            raise forms.ValidationError('Source Business has no Wallet instance')
        cleaned_data['source_wallet_entity'] = source_wallet

        destination_wallet: WalletEntity
        if destination_business_id == -1:
            destination_wallet = PaymentGatewayPort.get_booksy_wallet()
        else:
            destination_wallet = PaymentGatewayPort.get_business_wallet(
                business_id=destination_business_id,
            )
        if not destination_wallet:
            raise forms.ValidationError('Destination Business has no Wallet instance')
        cleaned_data['destination_wallet_entity'] = destination_wallet

        if source_wallet.id == destination_wallet.id:
            raise forms.ValidationError('Cannot transfer funds between Businesses')
        return cleaned_data


class ImportStripeManualTransferForm(forms.Form):
    import_file = forms.FileField(
        label='Import File.',
    )

    def clean(self):
        cleaned_data = super().clean()
        import_file = cleaned_data['import_file']
        stripped_data = strip_xlsx_data(load_spreadsheet_with_pandas(import_file.read()))[1:]
        for i, row in enumerate(stripped_data, 1):
            row_serializer = StripeManualTransferForm(
                data={
                    'source_business_id': row[0],
                    'destination_business_id': row[1],
                    'amount': row[2],
                    'reference_text': row[3],
                },
            )
            if not row_serializer.is_valid():
                raise forms.ValidationError(f'Error in {i} row: {repr(row_serializer.errors)}')
        import_file.seek(0)
        return cleaned_data
