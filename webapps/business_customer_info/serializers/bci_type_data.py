from datetime import date

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from lib.fields.custom_text_fields import ShortText, Paragraph
from lib.serializers import safe_get
from lib.tools import quick_error

from webapps.business.enums import CustomerCardType
from webapps.business.models.bci import BCITypeData


class PetSerializer(serializers.Serializer):
    pet_type = ShortText()
    breed = ShortText()
    weight = serializers.FloatField(
        allow_null=True,
        required=False,
    )
    additional_info = Paragraph()


class VehicleSerializer(serializers.Serializer):
    manufacturer = ShortText()
    model = ShortText()
    registration_number = ShortText(
        max_length=8, required=True, allow_blank=False, allow_null=False
    )
    vin_number = ShortText(max_length=17)
    year = serializers.IntegerField(
        max_value=date.today().year,
        allow_null=True,
        required=False,
    )
    additional_info = Paragraph()


CARD_SERIALIZER_MATCHER = {
    CustomerCardType.PERSON: None,
    CustomerCardType.VEHICLE: VehicleSerializer,
    CustomerCardType.PET: PetSerializer,
}


class BCITypeDataSerializer(serializers.ModelSerializer):
    default_error_messages = {
        'unknown_card_type': _('Unknown card type'),
        'unknown_options': _('Unknown parameter(s): {unknown_options}'),
        'required_option_missing': _('Required parameter(s) missing: {required_options}'),
    }

    class Meta:
        model = BCITypeData
        fields = [
            'card_type',
            'additional_data',
        ]

    def validate(self, attrs):
        attrs = super().validate(attrs)
        additional_data = safe_get(attrs, ['additional_data'])
        card_type = safe_get(attrs, ['card_type'])
        if card_type not in CARD_SERIALIZER_MATCHER:
            self.fail('unknown_card_type')
        attrs['additional_data'] = self.validate_options(additional_data, card_type)
        return attrs

    def validate_options(self, additional_data: dict, card_type: CustomerCardType):
        read_only_fields = BCITypeData.READ_ONLY_FIELDS.get(card_type) or []
        for option in read_only_fields:
            if option in additional_data:
                additional_data.pop(option)
        fields = BCITypeData.ADDITIONAL_DATA_FIELDS.get(card_type) or []
        unknown_options = set(additional_data.keys()) - set(fields)
        if unknown_options:
            raise serializers.ValidationError(
                self.default_error_messages['unknown_options'].format(
                    unknown_options=", ".join(unknown_options)
                ),
                code='unknown_options',
            )
        required_fields = BCITypeData.REQUIRED_FIELDS.get(card_type) or []
        not_required_options = set(required_fields) - set(additional_data.keys())
        if not_required_options:
            raise serializers.ValidationError(
                self.default_error_messages['required_option_missing'].format(
                    required_options=", ".join(not_required_options)
                ),
                code='required_option_missing',
            )

        required_from_bci = BCITypeData.REQUIRED_BCI_FIELDS.get(card_type) or []
        for field in required_from_bci:
            if (
                self.parent
                and self.parent.initial_data
                and field in self.parent.initial_data.keys()
                and not self.parent.initial_data.get(field)
            ):
                quick_error(
                    ['required', 'validation', field], description=_("This field is required")
                )

        if serializer_class := CARD_SERIALIZER_MATCHER.get(card_type):
            serializer = serializer_class(data=additional_data)
            if not serializer.is_valid(raise_exception=False):
                field = list(serializer.errors.keys())[0]
                quick_error(
                    [serializer.errors[field][0].code, 'validation', field],
                    description=str(serializer.errors[field][0]),
                )
            for field in additional_data:
                additional_data[field] = serializer.validated_data[field]
        return additional_data

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        card_type = safe_get(instance, ['card_type'])
        for k in list(ret.get('additional_data').keys()):
            fields = BCITypeData.ADDITIONAL_DATA_FIELDS.get(card_type) or []
            if k not in fields:
                del ret['additional_data'][k]
        return ret

    @classmethod
    def default_representation(cls):
        return {
            'card_type': CustomerCardType.PERSON,
            'additional_data': {},
        }

    @staticmethod
    def get_full_name_based_on_card_type(
        type_data: BCITypeData,
        full_name: str = '',
        first_name: str = '',
    ) -> str:
        card_type = safe_get(type_data, ['card_type'])
        additional_data = safe_get(type_data, ['additional_data'])

        match card_type:
            case CustomerCardType.PET:
                pet_type = safe_get(additional_data, ['pet_type'])
                breed = safe_get(additional_data, ['breed'])
                full_name = first_name
                if pet_type:
                    full_name += f', {pet_type}'
                if breed:
                    full_name += f', {breed}'
            case CustomerCardType.VEHICLE:
                full_name = safe_get(additional_data, ['registration_number'])
                manufacturer = safe_get(additional_data, ['manufacturer']) or ''
                model = safe_get(additional_data, ['model']) or ''
                if manufacturer or model:
                    full_name += ', ' + f'{manufacturer} {model}'.strip()
        return full_name


def hide_fields(bci: 'BusinessCustomerInfo', res: dict):
    if not bci.type_data or not bci.type_data.card_type:
        return
    hidden_fields = BCITypeData.HIDDEN_FIELDS.get(bci.type_data.card_type) or {}
    for field, replacement in hidden_fields.items():
        if res.get(field):
            res[field] = replacement


def save_bci_type_data(bci: 'BusinessCustomerInfo', type_data_dict: dict):
    if bci.type_data:
        bci.type_data.card_type = type_data_dict.get('card_type')
        additional_data = type_data_dict.get('additional_data') or {}
        if bci.type_data.additional_data:
            read_only_fields = BCITypeData.READ_ONLY_FIELDS.get(bci.type_data.card_type) or []
            for option in read_only_fields:
                additional_data[option] = bci.type_data.additional_data.get(option)
        bci.type_data.additional_data = type_data_dict.get('additional_data')
        bci.type_data.save()
    else:
        bci.type_data = BCITypeData.objects.create(**type_data_dict)
