# Generated by Django 1.11.7 on 2018-02-28 15:00
from django.db import migrations


def create_control_group(apps, schema_editor):
    db_name = schema_editor.connection.alias
    BizExperiment = apps.get_model('experiment', 'BizExperiment')
    BizExperiment.objects.using(db_name).get_or_create(name='CONTROL_GROUP', fixed_percent=20)


class Migration(migrations.Migration):
    dependencies = [
        ('experiment', '0018_auto_20180226_1343'),
    ]

    operations = [
        migrations.RunPython(
            code=create_control_group,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
