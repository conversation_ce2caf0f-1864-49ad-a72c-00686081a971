import operator
from functools import reduce

from django.db.models import Q
from django_filters import (
    rest_framework as filters,
)


class PublicAPIBaseFilterBackend(filters.DjangoFilterBackend):
    def get_filterset_kwargs(self, request, queryset, view):
        kwargs = super().get_filterset_kwargs(request, queryset, view)

        # merge filterset kwargs provided by view class
        if hasattr(view, 'get_filterset_kwargs'):
            kwargs.update(view.get_filterset_kwargs())

        return kwargs


class SoftPublicAPIBaseFilterBackend(PublicAPIBaseFilterBackend):
    raise_exception = False


class TimestampFilterSet(filters.FilterSet):
    created_from = filters.DateTimeFilter(
        field_name="created",
        lookup_expr='gte',
        required=False,
    )
    created_till = filters.DateTimeFilter(
        field_name="created",
        lookup_expr='lte',
        required=False,
    )
    updated_from = filters.DateTimeFilter(
        field_name="updated",
        lookup_expr='gte',
        required=False,
    )
    updated_till = filters.DateTimeFilter(
        field_name="updated",
        lookup_expr='lte',
        required=False,
    )

    def __init__(self, *args, data=None, **kwargs):
        if data is not None:
            data = data.copy()

            for name, f in self.base_filters.items():
                initial = f.extra.get('initial')

                if not data.get(name) and initial:
                    data[name] = initial

        super().__init__(data, *args, **kwargs)


class MultiValueCharFilter(filters.BaseCSVFilter, filters.CharFilter):
    def filter(self, qs, value):
        if value:
            expr = reduce(
                operator.or_, (Q(**{f'{self.field_name}__{self.lookup_expr}': v}) for v in value)
            )
            return qs.filter(expr)
        return qs.all()


class ImportUIDFilterSet(filters.FilterSet):
    import_uid = MultiValueCharFilter(field_name='import_uid', lookup_expr='iexact', required=False)
