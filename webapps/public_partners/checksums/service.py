from rest_framework import serializers

from webapps.business.models import Service, ServiceVariant
from webapps.public_partners.checksums.base import BaseChecksumSerializer
from webapps.public_partners.models import ServiceChecksum


class ServiceVariantChecksumSerializer(BaseChecksumSerializer):
    class Meta(BaseChecksumSerializer.Meta):
        model = ServiceVariant
        fields = read_only_fields = (
            'service_id',
            'valid_from',
            'valid_till',
            'active',
            'duration',
            'type',
            'price',
            'label',
            'time_slot_interval',
            'gap_hole_start_after',
            'gap_hole_duration',
            'deleted',
        )


class ServiceChecksumSerializer(BaseChecksumSerializer):
    class Meta(BaseChecksumSerializer.Meta):
        model = Service
        checksum_model = ServiceChecksum
        fields = read_only_fields = (
            'business_id',
            'name',
            'order',
            'active',
            'padding_type',
            'padding_time',
            'description',
            'note',
            'service_category_id',
            'treatment_id',
            'parallel_clients',
            'questions',
            'is_online_service',
            'is_available_for_customer_booking',
            'is_traveling_service',
            'color',
            'service_variants',
            'deleted',
        )

    service_variants = ServiceVariantChecksumSerializer(many=True, read_only=True)
    questions = serializers.ListSerializer(child=serializers.CharField(), read_only=True)
