from django.db import transaction
from django.db.models.signals import post_save

from webapps.public_partners.enum import WebhookEnum
from webapps.public_partners.utils import webhooks_for_business_enabled


def send_resource_webhooks_to_partner_apps_signal(
    sender, instance, **kwargs
):  # pylint: disable=unused-argument
    event = (
        WebhookEnum.RESOURCE_CREATED
        if kwargs.get('created', False)
        else (
            WebhookEnum.RESOURCE_UPDATED if not instance.deleted else WebhookEnum.RESOURCE_DELETED
        )
    )
    if webhooks_for_business_enabled(instance.business_id, event):
        from webapps.public_partners.checksums import ResourceChecksumSerializer

        serializer = ResourceChecksumSerializer(instance=instance)
        transaction.on_commit(lambda: serializer.save(event=event, **kwargs))


def clear_user_oauth2_installations(sender, instance, **kwargs):  # pylint: disable=unused-argument
    if not instance.staff_user_id or not instance.deleted:
        return

    from webapps.public_partners.models import OAuth2Installation

    for installation in OAuth2Installation.objects.filter(
        business_id=instance.business_id,
        user_id=instance.staff_user_id,
        deleted__isnull=True,
    ):
        installation.soft_delete()


post_save.connect(
    send_resource_webhooks_to_partner_apps_signal,
    sender='business.Resource',
    dispatch_uid='post_save_resource_webhooks',
)
post_save.connect(
    clear_user_oauth2_installations,
    sender='business.Resource',
    dispatch_uid='post_save_resource_oauth_installations',
)
