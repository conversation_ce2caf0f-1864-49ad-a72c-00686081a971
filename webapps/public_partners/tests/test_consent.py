from model_bakery import baker
from rest_framework import status

from lib.test_utils import compare_expected_fields, create_subbooking
from lib.tools import tznow
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.consents.fields import TextField, CheckboxField, InputField
from webapps.public_partners.tests import (
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
)
from webapps.consents.models import ConsentForm


class ConsentListViewSetV02TestCase(FirewallTestCaseMixin, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/appointment/{}/consent/'
    _VERSION = '0.2'
    CONSENT_FIELDS = {
        'uuid',
        'form_id',
        'form_title',
        'form_fields',
        'form_version',
        'signed',
    }
    SIGNED_ATTR = 'signed'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.service = baker.make_recipe(
            'webapps.business.service_recipe',
            business=cls.business,
        )
        cls.service_variant = baker.make_recipe(
            'webapps.business.service_variant_recipe',
            service=cls.service,
        )
        cls.consent_form = baker.make(
            ConsentForm,
            business=cls.business,
            services=[cls.service],
            title='Per appointment consent form',
            fields=[TextField('Foo'), CheckboxField('Bar'), InputField('Baz')],
            required_per_appointment=True,
        )
        cls.consent_form2 = baker.make(
            ConsentForm,
            business=cls.business,
            services=[cls.service],
            title='Non per appointment consent form',
            fields=[TextField('Foo'), CheckboxField('Bar'), InputField('Baz')],
            required_per_appointment=False,
        )
        cls.consent_form3 = baker.make(
            ConsentForm,
            business=cls.business,
            services=[],
            title='General consent form',
            fields=[TextField('Foo'), CheckboxField('Bar'), InputField('Baz')],
            required_per_appointment=False,
        )
        cls.customer = baker.make_recipe('webapps.business.bci_recipe', business=cls.business)
        cls.subbooking, *_ = create_subbooking(
            business=cls.business,
            booking_kws=dict(booked_for=cls.customer, service_variant=cls.service_variant),
        )
        cls.appointment = AppointmentWrapper([cls.subbooking])

    def get_url(self, business_id=None, appointment_id=None):
        return self._url.format(
            business_id or self.business.id, appointment_id or self.subbooking.appointment.id
        )

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'post', 'get', 'delete', 'options', 'put'})

    def test_get_consents(self):
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK

        consents = resp.json()
        assert len(consents) == 2
        assert not all(consent[self.SIGNED_ATTR] for consent in consents)
        for consent in consents:
            compare_expected_fields(consent.keys(), self.CONSENT_FIELDS)
            assert not consent[self.SIGNED_ATTR]
            assert len(consent['form_fields']) == 3
            assert consent['form_fields'][0]['label'] == 'Foo'
            assert consent['form_fields'][0]['type'] == 'text'
            assert 'value' not in consent['form_fields'][0]
            assert consent['form_fields'][1]['label'] == 'Bar'
            assert consent['form_fields'][1]['type'] == 'checkbox'
            assert 'value' not in consent['form_fields'][1]
            assert 'value_boolean' not in consent['form_fields'][1]
            assert consent['form_fields'][2]['label'] == 'Baz'
            assert consent['form_fields'][2]['type'] == 'input'
            assert 'value' not in consent['form_fields'][2]
            assert 'value_string' not in consent['form_fields'][2]

    def test_get_signed_consents(self):
        self.consent = self.consent_form.create_consent(
            self.customer,
            appointment_id=self.appointment.id,
            notify=False,
        )
        self.consent.form_fields[1].value = True
        self.consent.form_fields[2].value = "Baz value"
        self.consent.signed = tznow()
        self.consent.save()
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK

        consents = resp.json()
        assert len(consents) == 2
        assert any(consent[self.SIGNED_ATTR] for consent in consents)
        for consent in consents:
            compare_expected_fields(consent.keys(), self.CONSENT_FIELDS)

    def test_get_consents_empty(self):
        subbooking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(booked_for=self.customer),
        )
        resp = self.get(self.get_url(appointment_id=subbooking.appointment_id))
        assert resp.status_code == status.HTTP_200_OK
        consents = resp.json()
        assert len(consents) == 0

    def test_get_consents_mismatch_appointment_business(self):
        other_business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.partner.add_business(other_business)
        other_service = baker.make_recipe(
            'webapps.business.service_recipe',
            business=self.business,
        )
        other_customer = baker.make_recipe(
            'webapps.business.bci_recipe',
            business=other_business,
        )
        other_consent_form = baker.make(
            ConsentForm,
            business=other_business,
            services=[other_service],
            title='Take-My-Kidneys Consent',
            fields=[TextField('Foo'), CheckboxField('Bar')],
            required_per_appointment=True,
        )
        other_subbooking, *_ = create_subbooking(
            business=other_business,
            booking_kws=dict(booked_for=other_customer),
        )
        other_appointment = AppointmentWrapper([other_subbooking])
        other_consent_form.create_consent(
            other_customer,
            appointment_id=other_appointment.id,
            notify=False,
        )

        resp = self.get(
            self.get_url(
                business_id=self.business.id, appointment_id=other_subbooking.appointment.id
            )
        )
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def basic_response_for_firewall_testing(self):
        self.consent_form.create_consent(
            self.customer,
            appointment_id=self.appointment.id,
            notify=False,
        )
        return self.get(self.get_url())


class ConsentListViewSetV03TestCase(ConsentListViewSetV02TestCase):
    _VERSION = '0.3'
    CONSENT_FIELDS = {
        'id',
        'form_title',
        'form_fields',
        'form_version',
        'consent_uuid',
        'consent_signed',
    }
    SIGNED_ATTR = 'consent_signed'
