# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.http import Http404
from django.utils.functional import cached_property
from rest_framework import status
from rest_framework.generics import get_object_or_404
from rest_framework.permissions import IsAuthenticated

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.booking.models import Appointment
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_MIN_RECEPTION
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import (
    PAReviewSerializerV02,
    PAReviewSerializerV03,
    PAReviewSerializerV04,
)
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BusinessRelatedViewMixin,
    FullModelMixin,
    GenericVersionPublicAPIViewSet,
)
from webapps.reviews.models import Review


class AppointmentReviewViewSet(
    FullModelMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            list=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            retrieve=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            default=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            default=STAFF_ACCESS_LEVELS_MIN_RECEPTION,
        ),
    }
    action_versions_map = {
        'retrieve': VersionSpec(PublicAPIVersionEnum.V02),
        'create': VersionSpec(
            min_version=PublicAPIVersionEnum.V02,
            max_version=PublicAPIVersionEnum.V02,
        ),
        'update': VersionSpec(PublicAPIVersionEnum.V02),
        'partial_update': VersionSpec(PublicAPIVersionEnum.V02),
        'destroy': VersionSpec(PublicAPIVersionEnum.V02),
    }
    versioned_serializer_classes = {
        PublicAPIVersionEnum.V02: dict(default=PAReviewSerializerV02),
        PublicAPIVersionEnum.V03: dict(default=PAReviewSerializerV03),
        PublicAPIVersionEnum.V04: dict(default=PAReviewSerializerV04),
        # currently use v0.4 serializers, change if needed
        PublicAPIVersionEnum.V05: dict(default=PAReviewSerializerV04),
    }

    def get_queryset(self):
        return Review.objects.filter(
            deleted__isnull=True,
            business__deleted__isnull=True,
            business__id=self.kwargs['business_pk'],
            subbooking__appointment__pk=self.appointment.pk,
        )

    def get_object(self):
        instance = self.get_queryset().first()
        if not instance:
            if self.action == 'update':
                return None
            raise Http404
        self.check_object_permissions(self.request, instance)
        return instance

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['appointment'] = self.appointment
        return context

    @using_db_for_reads(READ_ONLY_DB)
    def retrieve(self, request, *args, **kwargs):
        if self._version == PublicAPIVersionEnum.V02:
            return self.list(request, *args, **kwargs)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if self._version == PublicAPIVersionEnum.V02:
            kwargs['partial'] = True
            return super().update(request, *args, **kwargs)
        instance_exists = self.get_object()
        response = super().update(request, *args, **kwargs)
        if not instance_exists:
            response.status_code = status.HTTP_201_CREATED
        return response

    def perform_destroy(self, instance):
        instance.soft_delete()

    @cached_property
    @using_db_for_reads(READ_ONLY_DB)
    def appointment(self):
        return get_object_or_404(
            Appointment.objects.filter(deleted__isnull=True),
            business_id=self.kwargs['business_pk'],
            id=self.kwargs['appointment_pk'],
        )
