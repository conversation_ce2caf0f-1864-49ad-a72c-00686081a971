# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.http import Http404
from rest_framework.generics import get_object_or_404
from rest_framework.mixins import (
    DestroyModelMixin,
    RetrieveModelMixin,
    UpdateModelMixin,
)
from rest_framework.permissions import IsAuthenticated

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.business.models import TravelingToClients
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_ALL, STAFF_ACCESS_LEVELS_MIN_MANAGER
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import PATravelingToCustomerSerializerV02
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
)


class TravelingToCustomerViewSet(
    DestroyModelMixin,
    RetrieveModelMixin,
    UpdateModelMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    singular_routes = True
    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            retrieve=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            default=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            retrieve=STAFF_ACCESS_LEVELS_ALL,
            default=STAFF_ACCESS_LEVELS_MIN_MANAGER,
        ),
    }
    action_versions_map = {
        'retrieve': VersionSpec(PublicAPIVersionEnum.V02),
        'update': VersionSpec(PublicAPIVersionEnum.V02),
        'partial_update': VersionSpec(PublicAPIVersionEnum.V02),
        'destroy': VersionSpec(PublicAPIVersionEnum.V02),
    }
    serializer_class = PATravelingToCustomerSerializerV02

    def get_object(self):
        try:
            instance = get_object_or_404(TravelingToClients, business=self.business)
        except Http404 as e:
            if self.action == 'update':
                return TravelingToClients.all_objects.filter(business=self.business).first()
            raise Http404 from e

        self.check_object_permissions(self.request, instance)
        return instance

    @using_db_for_reads(READ_ONLY_DB)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, args, kwargs)

    def perform_destroy(self, instance):
        instance.soft_delete()
