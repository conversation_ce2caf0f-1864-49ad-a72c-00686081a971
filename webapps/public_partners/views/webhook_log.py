# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework.mixins import (
    ListModelMixin,
    RetrieveModelMixin,
)
from rest_framework.permissions import IsAuthenticated

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.filters import PublicAPIBaseFilterBackend, WebhookLogListingFilterSet
from webapps.public_partners.models import WebhookLog
from webapps.public_partners.paginators import PaginationV02
from webapps.public_partners.permissions import (
    OAuth2ApplicationOwnerPermission,
    OAuth2TokenMustHaveScopePermission,
)
from webapps.public_partners.serializers.webhook import PAWebhookLogSerializerV04
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    GenericVersionPublicAPIViewSet,
    OAuth2ApplicationRelatedViewMixin,
)


class WebhookLogViewSet(
    ListModelMixin,
    RetrieveModelMixin,
    OAuth2ApplicationRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        OAuth2ApplicationOwnerPermission,
        OAuth2TokenMustHaveScopePermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            default=[[settings.OAUTH2_APPLICATION_READ_SCOPE]],
        ),
    }
    action_versions_map = {
        'retrieve': VersionSpec(PublicAPIVersionEnum.V04),
        'list': VersionSpec(PublicAPIVersionEnum.V04),
    }
    serializer_class = PAWebhookLogSerializerV04
    filter_backends = (PublicAPIBaseFilterBackend,)
    filterset_class = WebhookLogListingFilterSet
    pagination_class = PaginationV02

    def get_queryset(self):
        return WebhookLog.objects.filter(
            application=self.application,
            deleted__isnull=True,
        ).order_by('-created')

    def get_filterset_kwargs(self):
        return dict(application=self.application)

    @using_db_for_reads(READ_ONLY_DB)
    def list(self, request, *args, **kwargs):
        return super().list(request, args, kwargs)

    @using_db_for_reads(READ_ONLY_DB)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, args, kwargs)
