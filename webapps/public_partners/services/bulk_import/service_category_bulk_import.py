from django.db import transaction

from webapps.business.models import ServiceCategory
from webapps.public_partners.services.bulk_import.base_bulk_import import BaseBulkImport


class ServiceCategoryBulkImport(BaseBulkImport):
    import_mapping = {
        'service_categories': ServiceCategory,
    }

    @transaction.atomic
    def perform(self):
        self._prepare()
        self._bulk_import()
        return self.items('service_categories', compact=False)

    def _prepare(self):
        for data in self.validated_data.copy():
            service_category = self._prepare_service_category(data=data)
            self._add_item('service_categories', service_category)

    def _prepare_service_category(self, data):
        return ServiceCategory(created=self._now, updated=self._now, **data)
