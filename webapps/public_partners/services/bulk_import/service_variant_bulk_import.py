from django.db import transaction

from lib.rivers import bump_document, River
from webapps.business.models import ServiceVariant
from webapps.public_partners.services.bulk_import.base_bulk_import import BaseBulkImport


class ServiceVariantBulkImport(BaseBulkImport):
    import_mapping = {
        'service_variants': ServiceVariant,
    }

    @transaction.atomic
    def perform(self):
        self._prepare()
        self._bulk_import()
        transaction.on_commit(self._bump_document)
        return self.items('service_variants', compact=False)

    def _prepare(self):
        for data in self.validated_data.copy():
            service_variant = self._prepare_service_variant(data=data)
            self._add_item('service_variants', service_variant)

    def _prepare_service_variant(self, data):
        return ServiceVariant(created=self._now, updated=self._now, **data)

    def _bump_document(self):
        bump_document(
            River.SERVICE_VARIANT,
            [instance.pk for instance in self.items('service_variants')],
        )
        bump_document(River.SERVICE, [self.context['service'].pk])
