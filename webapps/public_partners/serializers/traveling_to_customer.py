from rest_framework import serializers
from rest_framework.fields import CreateOnlyDefault

from webapps.business.models.models import TravelingToClients
from webapps.public_partners.fields import CurrentContextDefault
from webapps.public_partners.serializers.base import BaseModelSerializer


class PATravelingToCustomerSerializerV02(BaseModelSerializer):
    class Meta:
        model = TravelingToClients
        fields = (
            'id',
            'business',
            'business_id',
            'price',
            'price_type',
            'distance',
            'distance_unit',
            'policy',
            'hide_address',
            'traveling_only',
        )
        read_only_fields = (
            'id',
            'business_id',
        )

    business = serializers.HiddenField(default=CreateOnlyDefault(CurrentContextDefault('business')))

    def update(self, instance, validated_data):
        validated_data['deleted'] = None
        return super().update(instance, validated_data)
