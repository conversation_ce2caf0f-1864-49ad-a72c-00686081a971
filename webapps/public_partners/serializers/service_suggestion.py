from rest_framework import serializers

from lib.serializers import NullableDurationField
from webapps.business.models import ServiceSuggestion


class PAServiceSuggestionSerializer(serializers.ModelSerializer):
    duration = NullableDurationField()

    class Meta:
        model = ServiceSuggestion
        fields = (
            'treatment',
            'name',
            'duration',
            'price',
            'tax_rate',
            'popularity',
        )
