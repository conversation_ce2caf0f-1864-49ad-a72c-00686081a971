from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.fields import CreateOnlyDefault

import settings.defaults

from lib.serializers import Du<PERSON><PERSON>ield
from lib.tools import tznow
from webapps.business.enums import ComboType
from webapps.business.models import Service, ServiceCategory, ServiceVariant, ComboMembership
from webapps.business.models.category import BusinessCategory
from webapps.public_partners.enum import ServiceKindEnum
from webapps.public_partners.fields import (
    ServiceCategoryField,
    ColorField,
    DefaultServiceColorValue,
    CurrentContextDefault,
)
from webapps.public_partners.models import ServiceMetadata
from webapps.public_partners.serializers.base import BaseModelSerializer, BulkImportListSerializer
from webapps.public_partners.serializers.service_variant import (
    PAServiceVariantSerializerV01,
    PAServiceVariantSerializerV02,
    PAServiceVariantSerializerV03,
    PAComboServiceVariantSerializerV04,
    PABasicServiceVariantSerializerV04,
    PAServiceVariantSchemaSerializerV02,
)
from webapps.public_partners.serializers.metadata import MetadataSerializer, MetadataMixin
from webapps.public_partners.services.bulk_import import ServiceBulkImport
from webapps.public_partners.validators import (
    UnsupportedComboServiceValidator,
)


class PAServiceSimpleSerializer(BaseModelSerializer):
    class Meta:
        model = Service
        fields = read_only_fields = (
            'id',
            'name',
        )


class PAServiceSerializerV01(BaseModelSerializer):
    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'variants',
        )

    variants = PAServiceVariantSerializerV01(many=True, read_only=True, source='active_variants')


class PAServiceSerializerV02(BaseModelSerializer):
    class Meta:
        model = Service
        fields = (
            'id',
            'business',
            'business_id',
            'active',
            'name',
            'description',
            'note',
            'questions',
            'order',
            'is_online_service',
            'is_available_for_customer_booking',
            'is_traveling_service',
            'padding_type',
            'padding_time',
            'service_category',
            'treatment',
            'variants',
            'import_uid',
        )
        read_only_fields = (
            'id',
            'business_id',
        )
        actions_readonly_fields = {('update', 'partial_update'): ('import_uid',)}
        validators = [
            UnsupportedComboServiceValidator(),
        ]

    business = serializers.HiddenField(default=CreateOnlyDefault(CurrentContextDefault('business')))
    padding_time = DurationField(required=False, allow_null=True)
    variants = PAServiceVariantSerializerV02(many=True, read_only=True, source='active_variants')
    service_category = ServiceCategoryField(
        queryset=ServiceCategory.objects.filter(deleted__isnull=True),
        required=False,
        allow_null=True,
    )
    treatment = serializers.PrimaryKeyRelatedField(
        queryset=BusinessCategory.objects.filter(deleted__isnull=True),
        required=False,
        allow_null=True,
    )

    def validate(self, attrs):
        data = super().validate(attrs)
        padding_time = attrs.get('padding_time', None)
        padding_type = attrs.get('padding_type', None)

        if self.instance and padding_time is None:
            data['padding_time'] = self.instance.padding_time
        if self.instance and padding_type is None:
            data['padding_type'] = self.instance.padding_type

        if padding_type and padding_time is None:
            raise serializers.ValidationError(
                {'padding_time': _('Padding time is required when padding type specified')}
            )

        return data

    def validate_is_online_service(self, value):
        if value and self.context['business'].is_traveling_only:
            raise serializers.ValidationError(
                _("Business Traveling to Clients cannot have online services")
            )
        return value

    def validate_is_traveling_service(self, value):
        if value and not self.context['business'].is_traveling:
            raise serializers.ValidationError(_('Travel settings not defined for business'))

        return value

    def update(self, instance, validated_data):
        deactivate_service = instance.active and not validated_data.get('active', True)
        updated_instance = super().update(instance, validated_data)

        if deactivate_service:
            instance.service_variants.filter(active=True, valid_till__isnull=True).update(
                active=False, valid_till=tznow()
            )

        return updated_instance


class PAServiceSerializerV03(PAServiceSerializerV02):
    class Meta(PAServiceSerializerV02.Meta):
        fields = (
            'id',
            'business',
            'business_id',
            'active',
            'name',
            'description',
            'note',
            'questions',
            'order',
            'is_online_service',
            'is_available_for_customer_booking',
            'is_traveling_service',
            'padding_type',
            'padding_time',
            'tax_rate',
            'parallel_clients',
            'service_category_id',
            'treatment_id',
            'variants',
            'import_uid',
        )

    variants = PAServiceVariantSerializerV03(many=True, read_only=True, source='active_variants')
    service_category_id = ServiceCategoryField(
        queryset=ServiceCategory.objects.filter(deleted__isnull=True),
        required=False,
        allow_null=True,
        source='service_category',
    )
    treatment_id = serializers.PrimaryKeyRelatedField(
        queryset=BusinessCategory.objects.filter(deleted__isnull=True),
        required=False,
        allow_null=True,
        source='treatment',
    )


class PAServiceMetadataSerializerV04(MetadataSerializer):
    class Meta(MetadataSerializer.Meta):
        model = ServiceMetadata
        fields = (
            'application',
            'kind',
            'extra',
        )

    kind = serializers.ChoiceField(
        choices=ServiceKindEnum,
        source='metadata.kind',
        required=False,
        allow_null=True,
    )


class PAServiceListSerializerV04(BulkImportListSerializer):
    class Meta(BulkImportListSerializer.Meta):
        bulk_import_service = ServiceBulkImport

    BULK_IMPORT_MAX_SIZE = 50


class PAServiceSerializerV04(MetadataMixin, PAServiceSerializerV03):
    class Meta(PAServiceSerializerV03.Meta):
        list_serializer_class = PAServiceListSerializerV04
        fields = PAServiceSerializerV03.Meta.fields + (
            'color',
            'combo_type',
            'metadata',
        )
        actions_readonly_fields = {
            (
                'update',
                'partial_update',
            ): (
                'import_uid',
                'combo_type',
            )
        }
        timestamp_fields = True
        validators = []

    variants = PABasicServiceVariantSerializerV04(
        source='active_variants',
        required=False,
        many=True,
    )
    color = ColorField(
        choices=[color[1] for color in settings.SERVICE_COLORS],
        required=False,
        allow_null=True,
        default=DefaultServiceColorValue(),
    )
    metadata = PAServiceMetadataSerializerV04(required=False, allow_null=True)

    def to_internal_value(self, data):
        combo_type = data.get('combo_type', self.get_from_instance('combo_type'))
        if combo_type in (
            ComboType.PARALLEL,
            ComboType.SEQUENCE,
        ):
            self.fields['variants'] = PAComboServiceVariantSerializerV04(
                required=False,
                many=True,
            )
        return super().to_internal_value(data)

    def create(self, validated_data):
        variants_data = validated_data.pop('active_variants', None)
        instance = super().create(validated_data)
        if variants_data is not None:
            self._upsert_service_variants(instance, variants_data)
        return instance

    def update(self, instance, validated_data):
        variants_data = validated_data.pop('active_variants', None)
        instance = super().update(instance, validated_data)
        if variants_data is not None:
            self._upsert_service_variants(instance, variants_data)
        return instance

    def _upsert_service_variants(self, instance: Service, data: dict):
        service_variant_ids = set(c.get('id') for c in data)

        for variant_data in data:
            combo_children = variant_data.pop('combo_children_through', None)
            variant_data['service'] = instance
            service_variant, _ = ServiceVariant.objects.update_or_create(
                id=variant_data.pop('id', None),
                defaults=variant_data,
            )
            if combo_children is not None:
                self._upsert_combo_children(service_variant, combo_children)
            service_variant_ids.add(service_variant.id)

        for service_variant in ServiceVariant.objects.filter(service=instance).exclude(
            id__in=service_variant_ids
        ):
            service_variant.soft_delete()

    def _upsert_combo_children(self, instance: ServiceVariant, data: dict):
        combo_membership_ids = set(c.get('id') for c in data)

        for combo_child_data in data:
            combo_child_data['combo'] = instance
            combo, _ = ComboMembership.objects.update_or_create(
                id=combo_child_data.pop('id', None),
                defaults=combo_child_data,
            )
            combo_membership_ids.add(combo.id)

        for combo in ComboMembership.objects.filter(combo=instance).exclude(
            id__in=combo_membership_ids
        ):
            combo.soft_delete()


class PAServiceSchemaSerializerV02(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(read_only=True)
    variants = PAServiceVariantSchemaSerializerV02(
        many=True,
        read_only=True,
        source='all_service_variants',
    )
    deleted = serializers.SerializerMethodField()
    import_uid = serializers.CharField(read_only=True)

    @staticmethod
    def get_deleted(obj):
        return bool(obj.deleted)
