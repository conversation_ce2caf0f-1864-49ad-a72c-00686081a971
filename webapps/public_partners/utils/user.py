from webapps.business.models import Business
from webapps.public_partners.exceptions import NotPlaceholderOwnerError
from webapps.user.tools import get_public_api_user


def create_placeholder_owner(uid):
    return get_public_api_user(partner_name=f'public_api_user_{uid}', partner_uuid=uid)


def has_placeholder_owner(business):
    return is_placeholder_owner(business.owner)


def is_placeholder_owner(user):
    return user.username.startswith('public_api_user_')


def replace_placeholder_owner(business, new_owner):
    if not has_placeholder_owner(business):
        raise NotPlaceholderOwnerError

    placeholder_owner = business.owner
    business.owner = new_owner

    # recalculate flag - owner is no longer system user
    business.include_in_analysis = False
    business.save()

    if not Business.objects.filter(owner=placeholder_owner).exists():
        placeholder_owner.delete()
