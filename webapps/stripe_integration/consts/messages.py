from django.utils.translation import gettext_lazy as _

# pylint: disable=line-too-long,pointless-string-statement

"""
source: https://stripe.com/docs/declines/codes

js for extracting data (execute in browsers console):

const rows = [...document.querySelectorAll("#content > div.Document > div > table > tbody > tr")]
const data = {}
rows.forEach(row => (data[row.querySelectorAll("td")[0].id] = row.querySelectorAll("td")[1].innerText))
JSON.stringify(data)
"""

STRIPE_MESSAGES = {
    "approve_with_id": _("The payment cannot be authorized."),
    "authentication_required": _(
        "The card was declined as the transaction requires authentication."
    ),
    "call_issuer": _("The card has been declined for an unknown reason."),
    "card_not_supported": _("The card does not support this type of purchase."),
    "card_velocity_exceeded": _(
        "The customer has exceeded the balance or credit limit available on their card."
    ),
    "currency_not_supported": _("The card does not support the specified currency."),
    "do_not_honor": _("The card has been declined for an unknown reason."),
    "do_not_try_again": _("The card has been declined for an unknown reason."),
    "duplicate_transaction": _(
        "A transaction with identical amount and credit card information was submitted very recently."
    ),
    "expired_card": _("The card has expired."),
    "fraudulent": _("The payment has been declined as Stripe suspects it is fraudulent."),
    "generic_decline": _("The card has been declined for an unknown reason."),
    "incorrect_cvc": _("The CVC number is incorrect."),
    "incorrect_number": _("The card number is incorrect."),
    "incorrect_pin": _(
        "The PIN entered is incorrect. This decline code only applies to payments made with a card reader."
    ),
    "incorrect_zip": _("The ZIP/postal code is incorrect."),
    "insufficient_funds": _("The card has insufficient funds to complete the purchase."),
    "invalid_account": _("The card, or account the card is connected to, is invalid."),
    "invalid_amount": _("The payment amount is invalid, or exceeds the amount that is allowed."),
    "invalid_cvc": _("The CVC number is incorrect."),
    "invalid_expiry_month": _("The expiration month is invalid."),
    "invalid_expiry_year": _("The expiration year is invalid."),
    "invalid_number": _("The card number is incorrect."),
    "invalid_pin": _(
        "The PIN entered is incorrect. This decline code only applies to payments made with a card reader."
    ),
    "issuer_not_available": _(
        "The card issuer could not be reached, so the payment could not be authorized."
    ),
    "lost_card": _("The payment has been declined because the card is reported lost."),
    "merchant_blacklist": _(
        "The payment has been declined because it matches a value on the Stripe user’s block list."
    ),
    "new_account_information_available": _(
        "The card, or account the card is connected to, is invalid."
    ),
    "no_action_taken": _("The card has been declined for an unknown reason."),
    "not_permitted": _("The payment is not permitted."),
    "offline_pin_required": _("The card has been declined as it requires a PIN."),
    "online_or_offline_pin_required": _("The card has been declined as it requires a PIN."),
    "pickup_card": _(
        "The card cannot be used to make this payment (it is possible it has been reported lost or stolen)."
    ),
    "pin_try_exceeded": _("The allowable number of PIN tries has been exceeded."),
    "processing_error": _("An error occurred while processing the card."),
    "reenter_transaction": _(
        "The payment could not be processed by the issuer for an unknown reason."
    ),
    "restricted_card": _(
        "The card cannot be used to make this payment (it is possible it has been reported lost or stolen)."
    ),
    "revocation_of_all_authorizations": _("The card has been declined for an unknown reason."),
    "revocation_of_authorization": _("The card has been declined for an unknown reason."),
    "security_violation": _("The card has been declined for an unknown reason."),
    "service_not_allowed": _("The card has been declined for an unknown reason."),
    "stolen_card": _("The payment has been declined because the card is reported stolen."),
    "stop_payment_order": _("The card has been declined for an unknown reason."),
    "testmode_decline": _("A Stripe test card number was used."),
    "transaction_not_allowed": _("The card has been declined for an unknown reason."),
    "try_again_later": _("The card has been declined for an unknown reason."),
    "withdrawal_count_limit_exceeded": _(
        "The customer has exceeded the balance or credit limit available on their card."
    ),
    "reader_unreachable": _(
        "Please make sure that the reader is powered on and connected to the network."
    ),
    # ttp errors:
    "card_read_timed_out": _(
        "Payment could not be completed because the transaction timed out, please try again."
    ),
    "tap_to_pay_device_tampered": _(
        "We detected a possible security issue. "
        "This could happen if parts of your device have been replaced or if the device’s software was modified."
    ),
    "tap_to_pay_nfc_disabled": _(
        "NFC must be enabled to process Tap to Pay transactions. Please check your settings and try again."
    ),
    "location_services_disabled": _(
        "Location services must be enabled to process Tap to Pay transactions. "
        "Please check your settings and try again."
    ),
    "tap_to_pay_insecure_environment": _(
        "We detected a possible security issue. Please ensure screen recording is off, "
        "camera is not active, and developer options are disabled in your settings."
    ),
}
# pylint: enable=line-too-long,pointless-string-statement
