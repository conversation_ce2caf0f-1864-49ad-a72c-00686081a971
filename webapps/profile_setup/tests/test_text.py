from webapps.profile_setup.text import ProfileSetupTargets
from webapps.profile_setup.models import ProfileSetupProgress
from webapps.notification.enums import NotificationTarget


def test_profile_setup_step_model_integrity():
    for step in ProfileSetupTargets:
        assert hasattr(ProfileSetupProgress, step)


def test_profile_setup_step_map_integrity():
    for step in ProfileSetupTargets:
        assert step in NotificationTarget
