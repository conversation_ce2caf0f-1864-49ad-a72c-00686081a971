from django.test import TestCase, override_settings
from mock import patch
from model_bakery import baker

from webapps.business.models import Business, Resource
from webapps.qr_code_origami.generator import BusinessQRCodeGenerator
from webapps.qr_code_origami.storage import QRCodeFileStorage
from webapps.qr_code_origami.tests.commons.base import QRCodeGeneratorBaseTestCases


@override_settings(QR_CODE_GCS_DOMAIN_NAME='test.domain.com')
class BusinessQRCodeGeneratorTestCase(QRCodeGeneratorBaseTestCases, TestCase):
    def setUp(self):
        super().setUp()
        self._business = baker.make(Business)

    def _get_qr_code_image_url(self) -> str:
        return BusinessQRCodeGenerator(business_id=self._business.id).generate()

    def _get_qr_code_image_url_force_generate(self) -> str:
        return BusinessQRCodeGenerator(business_id=self._business.id).force_generate()

    def _get_expected_qr_code_image_url(self) -> str:
        file_storage_path = BusinessQRCodeGenerator(business_id=self._business.id).file_storage_path
        return QRCodeFileStorage().domain_url(file_storage_path)

    def _get_expected_mobile_deeplink(self) -> str:
        return f'show_business/{self._business.id}'

    def _get_expected_desktop_url(self) -> str:
        return self._business.get_marketplace_url_for_desktop_deeplink()

    def _get_expected_fallback_url(self) -> str:
        return (
            f'{self._business.get_marketplace_url_for_desktop_deeplink()}'
            '&qr_code_fallback=1'
            '&app_deeplink=https%253A%252F%252Ftdl.booksy.com%252F000000000'
            '-business_qr_code-business_qr_code_app_redirect'
        )

    def _get_expected_deeplink_channel(self) -> str:
        return 'business_qr_code'

    def _get_expected_deeplink_campaign(self) -> str:
        return 'business_qr_code'

    def _get_generic_qr_code_image_url(self) -> str:
        return BusinessQRCodeGenerator(
            business_id=self._business.id, qr_type='invite_your_customers', generic=True
        ).generate()

    def _get_expected_generic_qr_code_image_url(self) -> str:
        file_storage_path = BusinessQRCodeGenerator(
            business_id=self._business.id, qr_type='invite_your_customers', generic=True
        ).file_storage_path
        return QRCodeFileStorage().domain_url(file_storage_path)

    @patch('webapps.qr_code_origami.storage.QRCodeFileStorage.exists', return_value=True)
    def test_generate_generic_qr_code_exists(self, _exists_mock):
        url = self._get_generic_qr_code_image_url()
        self.assertEqual(url, self._get_expected_generic_qr_code_image_url())
        self.assertEqual(_exists_mock.call_count, 1)

    @patch('lib.deeplink.cache.DeepLinkCache.get')
    @patch('webapps.qr_code_origami.storage.QRCodeFileStorage.exists', return_value=False)
    def test_generate_generic_qr_code_not_exists(self, _, _deeplink_cache_mock):
        self._get_generic_qr_code_image_url()
        self.assertEqual(_deeplink_cache_mock.call_count, 1)


@override_settings(QR_CODE_GCS_DOMAIN_NAME='test.domain.com')
class StafferQRCodeGeneratorTestCase(QRCodeGeneratorBaseTestCases, TestCase):
    def setUp(self):
        super().setUp()
        self._staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=baker.make(Business),
        )

    def _get_qr_code_image_url(self) -> str:
        return BusinessQRCodeGenerator(
            business_id=self._staffer.business.id, staffer_id=self._staffer.id
        ).generate()

    def _get_qr_code_image_url_force_generate(self) -> str:
        return BusinessQRCodeGenerator(
            business_id=self._staffer.business.id, staffer_id=self._staffer.id
        ).force_generate()

    def _get_expected_qr_code_image_url(self) -> str:
        file_storage_path = BusinessQRCodeGenerator(
            business_id=self._staffer.business.id, staffer_id=self._staffer.id
        ).file_storage_path
        return QRCodeFileStorage().domain_url(file_storage_path)

    def _get_expected_mobile_deeplink(self) -> str:
        return f'show_business/{self._staffer.business.id}/?staffer_id={self._staffer.id}'

    def _get_expected_desktop_url(self) -> str:
        return self._staffer.business.get_marketplace_url_for_desktop_deeplink(
            staffer_id=self._staffer.id,
        )

    def _get_expected_fallback_url(self) -> str:
        base_url = self._get_expected_desktop_url()

        return (
            f'{base_url}&qr_code_fallback=1&app_deeplink='
            f'https%253A%252F%252Ftdl.booksy.com%252F000000000'
            f'-staffer_qr_code-business_qr_code_app_redirect'
        )

    def _get_expected_deeplink_channel(self) -> str:
        return 'staffer_qr_code'

    def _get_expected_deeplink_campaign(self) -> str:
        return 'staffer_qr_code'
