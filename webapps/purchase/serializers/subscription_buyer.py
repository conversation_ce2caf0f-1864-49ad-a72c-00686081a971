from collections import defaultdict

from django.conf import settings
from django.db import transaction
from django.utils.translation import (
    gettext_lazy as _,
)
from rest_framework import serializers

from lib.fields.state import StateField
from lib.fields.tax_number_field import TaxNumberFieldSerializer
from lib.fields.zipcode import Z<PERSON>code<PERSON>ield
from lib.serializers import (
    BooksyEmailField,
)
from lib.tools import tznow

from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.navision.models import TaxGroup
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress, Subscription
from webapps.structure.models import Region
from webapps.user.tools import get_system_user


class InvoiceAddressCreateSerializer(serializers.ModelSerializer):
    address_details1 = serializers.CharField(
        max_length=100,
        label=_('Street'),
    )
    city = serializers.CharField(
        max_length=100,
        label=_('City'),
    )
    zipcode = ZipcodeField(
        label=defaultdict(
            us=_('Zip Code'),
            gb='Postal Code',
            pl=_('Zip Code'),
            es=_('Zip Code'),
            ie='Eircode',
        ),
    )
    state = StateField(
        label=_('State'),
        required=False,
        allow_null=True,
    )

    class Meta:
        model = InvoiceAddress
        fields = (
            "address_details1",
            "city",
            "zipcode",
            "state",
        )

    @staticmethod
    def _add_state_to_data(validated_data):
        if not validated_data.get('state') and (zipcode := validated_data.get('zipcode')):
            state = zipcode.get_parent_by_type(types=[Region.Type.STATE])
            if state:
                validated_data['state'] = state

    @staticmethod
    def _check_zipcode(validated_data):
        if not settings.CHECK_ZIPCODE_IN_REGION_TABLE and 'zipcode' in validated_data:
            validated_data['zipcode_textual'] = validated_data.pop('zipcode')

    def create(self, validated_data):
        self._check_zipcode(validated_data)
        self._add_state_to_data(validated_data)
        instance = super().create(validated_data)
        return instance

    def update(self, instance, validated_data):
        self._check_zipcode(validated_data)
        self._add_state_to_data(validated_data)
        instance = super().update(instance, validated_data)
        return instance


class InvoiceAddressGetSerializer(serializers.ModelSerializer):
    zipcode = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    state_abbrev = serializers.SerializerMethodField()

    @staticmethod
    def get_zipcode(instance):
        return instance.zipcode_str

    @staticmethod
    def get_state(instance):
        if instance.state:
            return instance.state.name

    @staticmethod
    def get_state_abbrev(instance):
        if instance.state:
            return instance.state.abbrev

    class Meta:
        model = InvoiceAddress
        fields = (
            "address_details1",
            "city",
            "zipcode",
            "state",
            "state_abbrev",
        )


class SubscriptionBuyerGetSerializer(serializers.ModelSerializer):
    invoice_address = InvoiceAddressGetSerializer()
    invoice_emails = serializers.SerializerMethodField()
    tax_group = serializers.SlugRelatedField(
        slug_field='name',
        read_only=True,
    )

    class Meta:
        model = SubscriptionBuyer
        fields = (
            'entity_name',
            'tax_id',
            'invoice_address',
            'tax_group',
            'invoice_emails',
            'enova_uid',
            'batch_invoices',
            'payment_due_days',
            'is_verified',
            'verified_at',
            'verified_by',
            'active',
        )

    @staticmethod
    def get_invoice_emails(instance):
        emails = []

        if instance.invoice_email is not None:
            emails.append(instance.invoice_email.lower())

        if instance.extra_invoice_emails:
            emails.extend(email.lower() for email in instance.extra_invoice_emails)

        return emails


class _SubscriptionBuyerCreateSerializer(serializers.ModelSerializer):
    business_id = serializers.PrimaryKeyRelatedField(
        required=True,
        queryset=Business.objects.all(),
    )
    invoice_address = InvoiceAddressCreateSerializer(
        required=True,
    )
    invoice_email = BooksyEmailField(required=True)
    tax_group_id = serializers.PrimaryKeyRelatedField(
        source='tax_group',
        required=False,
        allow_null=True,
        queryset=TaxGroup.objects.filter(deleted__isnull=True),
    )

    class Meta:
        model = SubscriptionBuyer
        fields = ()

    @staticmethod
    def validate_tax_group_id(data):
        if data is not None and not settings.NAVISION_USE_TAX_GROUPS:
            raise serializers.ValidationError(_('Tax Groups are not supported.'))
        if data is None and settings.NAVISION_USE_TAX_GROUPS:
            raise serializers.ValidationError(_('Please provide a valid Tax Group ID.'))

        return data

    @staticmethod
    def validate_tax_id(data):
        if not data or data.isspace():
            return None
        return data

    @staticmethod
    def validate_verification_rules(buyer, data):
        is_verified = data.get('is_verified')
        tax_id = data.get('tax_id')

        tax_id_not_matched = (tax_id and buyer.tax_id != tax_id) or (not tax_id and buyer.tax_id)
        if 'tax_id' in data and tax_id_not_matched and not buyer.admin_can_edit_tax_related_fields:
            raise serializers.ValidationError(_("You cannot change tax_id"))
        if buyer.is_verified and is_verified is False:
            raise serializers.ValidationError(_("Can't unverify buyer once is_verified is checked"))

    def validate(self, attrs):
        data = super().validate(attrs)

        buyer = self.instance
        if buyer:
            self.validate_verification_rules(buyer, data)

        return data

    @transaction.atomic
    def create(self, validated_data):
        business_id = validated_data.pop('business_id')
        invoice_address = InvoiceAddressCreateSerializer().create(
            validated_data.pop('invoice_address'),
        )
        validated_data['invoice_address'] = invoice_address
        validated_data['vat_registered'] = bool(validated_data.get('tax_id'))
        instance = super().create(validated_data)
        instance.businesses.add(business_id, bulk=False)
        return instance

    @transaction.atomic
    def update(self, instance, validated_data):
        validated_data['vat_registered'] = bool(validated_data.get('tax_id'))
        invoice_address_data = validated_data.pop('invoice_address', None)
        if invoice_address_data:
            if instance.invoice_address:
                invoice_address = InvoiceAddressCreateSerializer().update(
                    instance.invoice_address,
                    invoice_address_data,
                )
            else:
                invoice_address = InvoiceAddressCreateSerializer().create(
                    invoice_address_data,
                )
            validated_data['invoice_address'] = invoice_address
        buyer = super().update(instance, validated_data)
        return buyer


class SubscriptionBuyerImporterSerializer(_SubscriptionBuyerCreateSerializer):
    class Meta(_SubscriptionBuyerCreateSerializer.Meta):
        fields = (
            'entity_name',
            'invoice_address',
            'invoice_email',
            'batch_invoices',
            'is_verified',
            'verified_at',
            'verified_by',
            'active',
            'tax_id',
            'business_id',
            'invoicing_allowed',
            'invoicing_exclusion_reason',
            'tax_group_id',
        )

    def validate(self, attrs):
        result = super().validate(attrs)

        invoicing_allowed = attrs.get('invoicing_allowed', True)
        if not (invoicing_allowed or attrs.get('invoicing_exclusion_reason')):
            raise serializers.ValidationError(
                {_('Please provide a reason for invoicing exclusion.')}
            )

        return result

    def create(self, validated_data):
        from webapps.navision.tasks.buyer_merchant_integration import sync_buyer_with_merchant_task

        buyer = super().create(validated_data)
        if buyer.is_verified:
            sync_buyer_with_merchant_task.delay(buyer_id=buyer.id)
        return buyer

    def update(self, instance, validated_data):
        from webapps.navision.tasks.buyer_merchant_integration import sync_buyer_with_merchant_task

        buyer = super().update(instance, validated_data)
        if buyer.is_verified:
            sync_buyer_with_merchant_task.delay(buyer_id=buyer.id)
        return buyer


class SubscriptionBuyerCSVSerializer(serializers.ModelSerializer):
    tax_id = TaxNumberFieldSerializer(
        required=False,
        allow_null=True,
        allow_blank=False,
    )
    businesses = serializers.PrimaryKeyRelatedField(
        queryset=Business.objects.all(),
        required=True,
        many=True,
    )
    subscriptions = serializers.PrimaryKeyRelatedField(
        queryset=Subscription.objects.all(),
        required=False,
        allow_null=True,
        many=True,
    )
    invoice_emails = serializers.ListField(
        child=BooksyEmailField(),
        min_length=1,
        max_length=4,
    )

    address_details1 = serializers.CharField(
        max_length=100,
        required=True,
    )
    city = serializers.CharField(
        max_length=30,
        required=True,
    )
    zipcode = ZipcodeField(
        required=True,
    )
    state = StateField(
        required=False,
        allow_null=True,
    )

    invoicing_allowed = serializers.BooleanField(
        required=True,
        allow_null=False,
    )

    invoicing_exclusion_reason = serializers.CharField(
        max_length=100,
        required=False,
        allow_null=True,
    )

    tax_group_id = serializers.PrimaryKeyRelatedField(
        source='tax_group',
        required=False,
        allow_null=True,
        queryset=TaxGroup.objects.filter(deleted__isnull=True),
    )

    class Meta:
        model = SubscriptionBuyer
        fields = (
            'entity_name',
            'invoice_emails',
            'batch_invoices',
            'is_verified',
            'verified_at',
            'verified_by',
            'tax_id',
            'businesses',
            'subscriptions',
            'address_details1',
            'city',
            'zipcode',
            'state',
            'invoicing_allowed',
            'invoicing_exclusion_reason',
            'tax_group_id',
        )

    @staticmethod
    def validate_tax_group_id(data):
        if data is not None and not settings.NAVISION_USE_TAX_GROUPS:
            raise serializers.ValidationError(_('Tax Groups are not supported.'))
        if data is None and settings.NAVISION_USE_TAX_GROUPS:
            raise serializers.ValidationError(_('Please provide a valid Tax Group ID.'))

        return data

    @staticmethod
    def validate_tax_id(data):
        if not data or data.isspace():
            return None
        return data

    def validate(self, attrs):
        result = super().validate(attrs)
        if not (attrs.get('invoicing_allowed') or attrs.get('invoicing_exclusion_reason')):
            raise serializers.ValidationError(
                {_('Please provide a reason for invoicing exclusion.')}
            )
        return result

    @transaction.atomic
    def create(self, validated_data):
        validated_data['vat_registered'] = bool(validated_data.get('tax_id'))
        invoice_email, *extra_invoice_emails = validated_data.pop('invoice_emails')

        validated_data['invoice_email'] = invoice_email
        validated_data['extra_invoice_emails'] = extra_invoice_emails
        validated_data['invoice_address'] = InvoiceAddressCreateSerializer().create(
            {
                'address_details1': validated_data.pop('address_details1'),
                'city': validated_data.pop('city'),
                'zipcode': validated_data.pop('zipcode'),
                'state': validated_data.pop('state', None),
            }
        )

        if validated_data.get('is_verified', False):
            validated_data['verified_by'] = self.context.get('operator', get_system_user())
            validated_data['verified_at'] = tznow()

        business_ids = []
        old_obj_vars = {}

        for business in validated_data['businesses']:
            # never move this lines. DRF .create is removing all 'many' fields from passed dict
            business_ids.append(business.id)
            old_obj_vars[business.id] = {'buyer_id': business.buyer_id}

        instance = super().create(validated_data)

        new_obj_vars = {
            'default': {
                'buyer_id': instance.id,
            },
        }

        operator = self.context.get('operator', get_system_user())
        metadata = self.context.get(
            'metadata',
            {
                'serializer': 'webapps.purchase.serializers.subscription_buyer'
                '.SubscriptionBuyerCSVSerializer'
            },
        )
        BusinessChange.bulk_change(
            business_ids=business_ids,
            operator=operator,
            old_obj_vars=old_obj_vars,
            new_obj_vars=new_obj_vars,
            metadata=metadata,
        )

        return instance
