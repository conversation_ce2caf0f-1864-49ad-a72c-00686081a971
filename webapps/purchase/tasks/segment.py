from lib.celery_tools import post_transaction_task
from lib.segment_analytics import get_segment_api
from rest_framework import serializers


def get_kwargs_to_segment_status_task(subscription):
    dirty_fields = subscription.get_dirty_fields()
    is_new_subscription = 'id' in dirty_fields and dirty_fields['id'] is None

    old_expiry = dirty_fields.get('expiry')
    if old_expiry:
        date_time_field = serializers.DateTimeField()
        old_expiry = date_time_field.to_representation(old_expiry)

    return dict(
        subscription_id=subscription.id,
        is_new_subscription=is_new_subscription,
        old_expiry=old_expiry,
    )


@post_transaction_task
def SegmentStatusChange(subscription_id, is_new_subscription=False, old_expiry=None):
    from datetime import timedelta

    import braintree
    from django.db.models import Count

    from lib.tools import tznow
    from webapps.business.models import Business
    from webapps.purchase.models import Subscription

    if old_expiry is not None:
        date_time_field = serializers.DateTimeField()
        old_expiry = date_time_field.to_internal_value(old_expiry)

    sub = Subscription.objects.get(id=subscription_id)

    business = (
        Business.objects.filter(
            id=sub.business_id,
        )
        .annotate(
            Count('subscriptions'),
        )
        .first()
    )

    # Mimicking condition from purchase.tasks.ComputeBusinessStatusTask for
    # Business.Status.PAID, consider refactor to be more DRY!
    now = tznow()
    is_business_paid = (
        sub.source == Business.PaymentSource.BRAINTREE
        and sub.receipt.get('status') == braintree.Subscription.Status.Active
    ) or (
        sub.source != Business.PaymentSource.BRAINTREE
        and (sub.expiry and (sub.start <= now <= sub.expiry) or not sub.expiry and sub.start <= now)
    )

    if is_business_paid and (
        is_new_subscription
        or
        # only significant expiry changes are treated as new purchase
        (old_expiry and old_expiry + timedelta(days=7) < sub.expiry)
    ):
        segment_api = get_segment_api(business)
        segment_api.subscription_purchased(sub)
