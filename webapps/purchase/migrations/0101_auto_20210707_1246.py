# Generated by Django 3.1.12 on 2021-07-07 12:46

from django.db import migrations, models
import webapps.purchase.models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0100_auto_20210705_1142'),
    ]

    operations = [
        migrations.AlterField(
            model_name='addon',
            name='price_amount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=9,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
        migrations.AlterField(
            model_name='coaching',
            name='price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9),
        ),
        migrations.AlterField(
            model_name='creditcardverificationattempt',
            name='amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='churn_mrr',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name=' Churn MRR'
            ),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='ex_con_mrr',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name='Extraction/Contraction MRR'
            ),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='last_subscription_price',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name='Last subscription price'
            ),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='new_mrr',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name='New MRR'
            ),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='reactive_mrr',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name='Reactive MRR'
            ),
        ),
        migrations.AlterField(
            model_name='mrrreports',
            name='total_mrr',
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=9, verbose_name='Total MRR'
            ),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='monthly_base_price',
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=9,
                null=True,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='price_amount',
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=9,
                null=True,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
        migrations.AlterField(
            model_name='subscriptiondiscount',
            name='amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionlisting',
            name='discount_amount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=9,
                validators=[webapps.purchase.models.is_positive],
                verbose_name='Discount amount (deprecated)',
            ),
        ),
        migrations.AlterField(
            model_name='subscriptionlisting',
            name='price_amount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=9,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
        migrations.AlterField(
            model_name='subscriptionsmspackage',
            name='price_amount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=9,
                null=True,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
        migrations.AlterField(
            model_name='subscriptiontransaction',
            name='price_with_discount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=9,
                validators=[webapps.purchase.models.is_positive],
            ),
        ),
    ]
