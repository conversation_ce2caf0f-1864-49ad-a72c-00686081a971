import typing as t
from datetime import timedelta
from decimal import Decimal

from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from lib.tools import format_currency_wo_decimals, tznow
from webapps.b2b_referral.helpers import B2BReferralDetails
from webapps.b2b_referral.models import B2BReferral, B2BReferralSetting
from webapps.business.models import Business
from webapps.business.notifications.planner import AheadSchedulePlanner
from webapps.notification.base import (
    BaseNotification,
    Context,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    PopupChannel,
    PushChannel,
    push_from_popup,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.recipients import Managers, SystemSender


class ReferralContext(Context):
    def __init__(self, notification: 'BaseReferralNotification') -> None:
        super().__init__(notification)
        self.settings: B2BReferralSetting = notification.referral.base_referral_setting

    def get_context(self) -> dict:
        future_reward = self.notification.future_reward_for_referring
        formatted_future_reward_for_referring = (
            format_currency_wo_decimals(future_reward) if future_reward else ''
        )

        return dict(
            referrer_first_name=self.notification.referrer.owner.first_name,
            invited_first_name=self.notification.invited.owner.first_name,
            referrer_reward=format_currency_wo_decimals(self.settings.referrer_reward_amount),
            invited_reward=format_currency_wo_decimals(self.settings.invited_reward_amount),
            future_reward_for_referring=formatted_future_reward_for_referring,
        )


class BaseReferralNotification(BaseNotification):
    category = NotificationCategory.REFERRAL
    sender = SystemSender
    recipients = (Managers,)
    contexts = (ReferralContext,)

    def __init__(self, referral: B2BReferral, **parameters):
        super().__init__(referral, **parameters)
        self.referral = referral
        self.referrer: Business = referral.referrer
        self.invited: Business = referral.invited

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.referral.id},{self.invited.id}'

    @cached_property
    def future_reward_for_referring(self) -> t.Optional[Decimal]:
        setting_for_invited = B2BReferralSetting.get_setting(self.referral.invited)
        if setting_for_invited:
            return setting_for_invited.referrer_reward_amount
        if self.referral.base_referral_setting.ambassador:
            # ambassador's deal setting won't be inherited by invited
            return None
        return self.referral.base_referral_setting.referrer_reward_amount

    def get_target(self) -> PushTarget:
        return PushTarget(type=NotificationTarget.REFERRAL.value)


class ToReferrerNotification(BaseReferralNotification):
    def __init__(self, referral: B2BReferral, **parameters):
        super().__init__(referral, **parameters)
        self.business = referral.referrer


class ToInvitedNotification(BaseReferralNotification):
    def __init__(self, referral: B2BReferral, **parameters):
        super().__init__(referral, **parameters)
        self.business = referral.invited

    def should_skip_with_plea(self) -> t.Tuple[bool, t.Optional[str]]:
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if not self.future_reward_for_referring:
            # rare edge-case, but possible
            return True, ('Invited business will not get rewards for inviting in future')

        return False, None


class InvitedBusinessSignedUpNotificationToReferrer(ToReferrerNotification):
    channels = (PopupChannel,)

    popup_template = PopupTemplate(
        crucial=False,
        icon=NotificationIcon.REFERRAL,
        relevance=3,
        group=NotificationGroup.REFERRAL,
        size=NotificationSize.BIG,
        messages=[
            _('Congratulations!'),
            _('{invited_first_name} has signed up with Booksy'),
            _('Share your tips to help them get set up.'),
        ],
    )


class InvitedBusinessSignedUpNotificationToInvited(ToInvitedNotification):
    channels = (PopupChannel,)

    @property
    def popup_template(self) -> PopupTemplate:
        if self.referral.base_referral_setting.both_award_flow:
            messages = [
                _('Did you know?'),
                _(
                    'If you subscribe to Booksy both you and '
                    '{referrer_first_name} will get '
                    '{future_reward_for_referring}.'
                ),
            ]
        else:
            messages = [
                _("We're glad {referrer_first_name} sent you!"),
                _('Subscribe to Booksy to start collecting referral bonuses yourself.'),
            ]

        return PopupTemplate(
            crucial=False,
            icon=NotificationIcon.REFERRAL,
            relevance=3,
            group=NotificationGroup.REFERRAL,
            size=NotificationSize.BIG,
            messages=messages,
        )


class InvitedBusinessPaidNotificationToInvited(ToInvitedNotification):
    channels = (PopupChannel,)

    @property
    def popup_template(self) -> PopupTemplate:
        if self.referral.base_referral_setting.both_award_flow:
            messages = [
                _('Welcome. You and {referrer_first_name} just ' 'got {invited_reward}!'),
                _('Spread the word to keep collecting referral bonuses.'),
            ]
        else:
            messages = [
                _("We're glad {referrer_first_name} sent you!"),
                _("Refer other professionals and " "we'll send you {future_reward_for_referring}."),
            ]

        return PopupTemplate(
            crucial=False,
            icon=NotificationIcon.REFERRAL,
            relevance=3,
            group=NotificationGroup.REFERRAL,
            size=NotificationSize.BIG,
            messages=messages,
        )


class InvitedBusinessPaidNotificationToReferrer(ToReferrerNotification):
    channels = (PushChannel, PopupChannel)

    def get_target(self) -> PushTarget:
        if self.referrer.completed_kyc:
            return PushTarget(type=NotificationTarget.REFERRAL.value)
        return PushTarget(type=NotificationTarget.REFERRAL_KYC.value)

    @property
    def push_template(self):
        return push_from_popup(self.popup_template)

    @property
    def popup_template(self) -> PopupTemplate:
        if self.referrer.completed_kyc:
            messages = [
                _('Congratulations!'),
                _('You got a {referrer_reward} reward'),
                _("It's been sent to your bank account"),
            ]
        else:
            messages = [
                _('Congratulations!'),
                _('Your {referrer_reward} reward is pending'),
                _('Verify your account details so we can send ' 'it to your bank account.'),
            ]

        return PopupTemplate(
            crucial=False,
            icon=NotificationIcon.REFERRAL,
            relevance=3,
            group=NotificationGroup.REFERRAL,
            size=NotificationSize.BIG,
            messages=messages,
        )


class PendingReferralRewardWaitingForKYCVerification(BaseNotification):
    category = NotificationCategory.REFERRAL
    sender = SystemSender
    recipients = (Managers,)
    channels = (PopupChannel,)
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        crucial=False,
        icon=NotificationIcon.REFERRAL,
        relevance=3,
        group=NotificationGroup.REFERRAL,
        size=NotificationSize.BIG,
        messages=[
            _('Your {referral_sum_reward} reward is pending.'),
            _('Please verify your account to access your funds.'),
        ],
    )

    def __init__(self, business: Business, **parameters):
        super().__init__(business, **parameters)
        self.business = business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.business.id}'

    def get_target(self) -> PushTarget:
        return PushTarget(type=NotificationTarget.REFERRAL_KYC.value)

    def should_skip_with_plea(self) -> t.Tuple[bool, t.Optional[str]]:
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if self.business.completed_kyc:
            return True, 'Business already completed KYC'

        if not self.pending_rewards:
            return True, 'There is no pending reward'

        # Just before sending current notification, schedule next reminder
        next_notification_date = tznow() + timedelta(days=3)
        self.__class__(self.business).schedule(when=next_notification_date)

        return False, None

    @cached_property
    def pending_rewards(self) -> Decimal:
        return B2BReferralDetails.get_details(self.business).pending

    def get_context(self) -> dict:
        return dict(referral_sum_reward=format_currency_wo_decimals(self.pending_rewards))


class ReferralEncouragementBaseNotification(BaseNotification):
    category = NotificationCategory.REFERRAL
    sender = SystemSender
    recipients = (Managers,)
    channels = (PopupChannel,)

    def __init__(self, business: Business, **parameters):
        super().__init__(business, **parameters)
        self.business = business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.business.id}'

    def get_target(self) -> PushTarget:
        return PushTarget(type=NotificationTarget.REFERRAL.value)

    def get_context(self) -> dict:
        future_reward = self.reward_for_referring
        formatted_reward_for_referring = (
            format_currency_wo_decimals(future_reward) if future_reward else ''
        )

        return dict(
            reward_for_referring=formatted_reward_for_referring,
        )

    def should_skip_with_plea(self) -> t.Tuple[bool, t.Optional[str]]:
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if not self.reward_for_referring:
            return True, "Business isn't in a referral program"

        # TODO: Here's also good place to check if during last three days
        #       there were sent any other referral notifications
        #       (condition eventually removed from 3.0 scope, but in the future
        #       all `Encouragement` notifications will probably have it)

        return False, None

    @cached_property
    def reward_for_referring(self) -> t.Optional[Decimal]:
        referral_setting = B2BReferralSetting.get_setting(self.business)
        if referral_setting:
            return referral_setting.referrer_reward_amount


class EncourageToReferralsRegardingReviewsNotification(ReferralEncouragementBaseNotification):
    popup_template = PopupTemplate(
        crucial=False,
        icon=NotificationIcon.REFERRAL,
        relevance=2,
        group=NotificationGroup.REFERRAL,
        size=NotificationSize.BIG,
        messages=[
            _('Hooray!'),
            _("You've just earned {positive_reviews_count} positive reviews!"),
            _('Refer other professionals for a {reward_for_referring} reward!'),
        ],
    )

    def get_context(self) -> dict:
        context = super().get_context()
        context['positive_reviews_count'] = self.positive_reviews_count
        return context

    @cached_property
    def positive_reviews_count(self) -> int:
        return self.business.reviews.filter(rank=5).count()


class EncourageToReferralsIfActiveSubscriberNotification(ReferralEncouragementBaseNotification):
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        crucial=False,
        icon=NotificationIcon.REFERRAL,
        relevance=2,
        group=NotificationGroup.REFERRAL,
        size=NotificationSize.BIG,
        messages=[
            _('Want an extra {reward_for_referring}?'),
            _('Refer Booksy to other professionals and to get rewarded.'),
        ],
    )

    def should_skip_with_plea(self) -> t.Tuple[bool, t.Optional[str]]:
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if self.business.successful_appointments_count() < 10:
            return True, 'Business does not have 10+ appointments'

        return False, None
