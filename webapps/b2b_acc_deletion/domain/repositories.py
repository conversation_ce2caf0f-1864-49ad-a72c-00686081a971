from abc import ABC, abstractmethod

from webapps.b2b_acc_deletion.domain.enums import B2BAccountDeletionExecutionMethod
from webapps.b2b_acc_deletion.domain.models import (
    Business,
    FakeUser,
    UserProfile,
    User,
)
from webapps.b2b_acc_deletion.shared.types import OwnerId, BusinessId, UserId


class BusinessRepository(ABC):
    @abstractmethod
    def get_businesses_ids_by_owner(self, owner_id: OwnerId) -> list[int]:
        pass

    @abstractmethod
    def get_businesses_to_anonymize(self, business_ids: list[int]) -> list[Business]:
        pass

    @abstractmethod
    def delete_business_images(self, business_id: BusinessId) -> None:
        pass

    @abstractmethod
    def save(self, business: Business, fake_owner: FakeUser, fake_staffers: list[FakeUser]) -> None:
        pass


class UserProfileRepository(ABC):
    @abstractmethod
    def delete_all_user_sessions(
        self, user_id: UserId, excluded_session_keys: list[str] = None
    ) -> None:
        pass

    @abstractmethod
    def get_user_profile(self, user_id: UserId, profile_fields: list[str]) -> UserProfile:
        pass

    @abstractmethod
    def check_if_customer_profile_exists(self, user_id: UserId) -> bool:
        pass

    @abstractmethod
    def update_user_profile(self, profile: UserProfile, profile_fields: list[str]) -> None:
        pass

    @abstractmethod
    def revoke_user_apple_token(self, user_id: UserId) -> None:
        pass

    @abstractmethod
    def delete_apple_identities(self, profile: UserProfile) -> None:
        pass


class UserRepository(ABC):
    @abstractmethod
    def get_user_to_anonymize(self, user_id: UserId) -> User:
        pass

    @abstractmethod
    def update_user_data(self, user: User, user_data_fields: list[str]) -> None:
        pass

    @abstractmethod
    def update_user_agreements(self, user_id: UserId) -> None:
        pass

    @abstractmethod
    def update_user_internal_data(
        self,
        user_id: UserId,
        method: B2BAccountDeletionExecutionMethod = B2BAccountDeletionExecutionMethod.SCRIPT,
    ) -> None:
        pass

    @abstractmethod
    def delete_all_user_social_account_connections(self, user_id: UserId) -> None:
        pass
