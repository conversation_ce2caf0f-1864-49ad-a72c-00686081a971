# Generated by Django 4.0.2 on 2022-04-11 08:11

from django.db import migrations, models

CHOICES = [
    ('appointment_history', 'Enable ES appointment history'),
    ('appointment_timeslots_test', 'Appointment timeslots test'),
    (
        'auto_notify_about_reschedule',
        'Send reschedule notification automatically - killswitch ON = manually (change state at night if needed to avoid errors)',
    ),
    ('auto_update_user_recommendation_groups', 'Auto-update user recommendation groups'),
    ('available_today', "Generates 'Available Today' gallery (MyBooksy)"),
    (
        'billing_migration_via_api',
        'Auto-migration merchants into new Billing through the API (frontdesk/mobile)',
    ),
    ('billing_new_accounts_with_stripe', 'Turn on/off Stripe for a new Px in Billing'),
    ('billing_suspend_enabled', 'Billing Suspend Enabled'),
    ('booking_reactivation', 'Adds booking reactivation actions (gallery, sms)'),
    ('booking_user_search_data', 'Synchronous calculate search data for new booking'),
    ('booksy_auth', 'Booksy Auth layer is enabled'),
    ('business_version_locks', 'Use BusinessVersion based locks'),
    ('c2b_referral', 'C2B Referral'),
    (
        'claim_appointments_merge_bci',
        'Turn On/Off feature that let our customers claim/merge appointments/bci by deeplink.',
    ),
    ('disclosure_obligation_agreement', 'Turn on/off disclosure obligation agreement email'),
    ('discount_after_pob', '1 free month of subscription after Payment Overdue Blocked period.'),
    ('donations_promo', 'Donations Promo'),
    ('double_subscriptions_report', 'Double subscriptions report'),
    (
        'sms_booking_confirmation_default',
        'Enable customer SMS booking confirmations for new businesses',
    ),
    ('experiment_activity_status_cache', 'Experiment activity status cache'),
    ('generate_sitemaps', 'Generate sitemaps'),
    ('google_reserve_live_updates', 'Google reserve live updates'),
    ('groupon_reserve_live_updates', 'Groupon reserve live updates'),
    ('gtm_for_admin', 'Enable Google Tag Manager (GTM) for admin'),
    ('iterable_email_service', 'Enable sending messages via Iterable.'),
    ('match_users_by_phone_number', 'Match users by phone number'),
    ('navision_integration', 'Turn on/off Navision integration'),
    ('navision_invoicing', 'Turn on/off Navision invoicing integration'),
    (
        'navision_use_tax_rate_table',
        'Take tax rates from our internal table (when killed we assume 0% for each product)',
    ),
    ('near_availability', 'Add inner hits to real availability (LA badge)'),
    ('old_mail_account_added', 'Turn on/off sending account added email'),
    ('old_mail_inactivity_login', 'Turn on/off sending inactivity login email'),
    ('patterns', 'Use patterns experiment in book again'),
    ('patterns_popup', 'Send patterns popup'),
    ('patterns_push', 'Send patterns push'),
    ('prompted_reviews_android', 'Send prompted reviews notifications on Android'),
    ('prompted_reviews_ios', 'Send prompted reviews notifications on iOS'),
    (
        'prompted_reviews_without_experiment',
        'Enable new experiment-less logic for prompted reviews',
    ),
    ('rate_limit', 'Disable Rate limit check on every request'),
    ('replace_banned_twilio_number', 'Replace banned Twilio number'),
    ('replica_booking_notifications', 'Use Replica DB for Booking Notifications'),
    ('replica_calendar_changes', 'Use Replica DB for CalendarChangeHandler'),
    ('replica_feed_generator', 'Enable using replica db for RwG feed generator.'),
    ('replica_live_update_rwg_task', 'Enable using replica db for RwG RTUs.'),
    ('replica_pos_transactions', 'Use Replica DB for POS Transactions'),
    ('replica_yelp_feed_generation', 'Enable using replica db for Yelp feed generator.'),
    (
        'reports_logical_replication',
        'Stats and reports will be calculated using logical replica db. When killed queries are routed to physical replica.',
    ),
    ('send_sms_booking_confirmations', 'Send SMS booking confirmations to customers'),
    ('skip_hidden_on_web', 'Turn on/off businesses on web search'),
    ('slave_booking_details', 'Use Replica DB for booking details'),
    ('slave_booking_listing', 'Use Replica DB for customer bookings list handler'),
    ('slave_calendar', 'Use Replica DB for business calendar'),
    ('slave_pos_trans_details', 'Use Replica DB for POS Transaction details '),
    ('slots_from_slave', 'Use Replica DB for time slots handler'),
    ('sms_whitelist_enabled', 'Blocking SMSes to non-whitelisted countries'),
    ('use_sms_with_deeplinks_enabled', "Enables customer booking sms's with deeplinks"),
    ('3d secure enabled', '3D Secure enabled'),
    ('trial_messages_push_channel', 'Push for TRIAL related messages'),
    ('utt2_backend', 'Enable operation included in UTT2 upgrade.'),
    ('utt2_experiment', 'Enable search using UTT2 based fields.'),
    ('versum_migration_enabled', 'Enable versum migration handling'),
    ('yelp_reserve_live_updates', 'Yelp reserve live updates'),
    ('martech_analytics', 'Turn on/off MarTech analytics'),
    ('old_martech_analytics', 'Turn on/off old MarTech analytics'),
    ('bb_no_show_for_business', 'Turn on/off event bb_no_show_for_business'),
    ('bcr_order_received', 'Turn on/off event bcr_order_received'),
    ('bcr_reset_account_verify', 'Turn on/off event bcr_reset_account_verify'),
    (
        'bcr_stripe_kyc_not_verified_account',
        'Turn on/off event bcr_stripe_kyc_not_verified_account',
    ),
    ('bcr_stripe_kyc_pending_account', 'Turn on/off event bcr_stripe_kyc_pending_account'),
    ('bcr_stripe_kyc_verified_account', 'Turn on/off event bcr_stripe_kyc_verified_account'),
    ('bcr_terminal_ordered', 'Turn on/off event bcr_terminal_ordered'),
    ('boost_on_off', 'Turn on/off event boost_on_off'),
    ('business_app_opened', 'Turn on/off event business_app_opened'),
    ('business_categories_updated', 'Turn on/off event business_categories_updated'),
    ('business_contact_pref_upd', 'Turn on/off event business_contact_pref_upd'),
    ('business_delay_set', 'Turn on/off event business_delay_set'),
    ('business_info_updated', 'Turn on/off event business_info_updated'),
    ('business_pba_enabled', 'Turn on/off event business_pba_enabled'),
    ('business_pos_updated', 'Turn on/off event business_pos_updated'),
    ('business_registration_completed', 'Turn on/off event business_registration_completed'),
    ('business_registration_started', 'Turn on/off event business_registration_started'),
    ('business_status_updated', 'Turn on/off event business_status_updated'),
    ('business_subscription_updated', 'Turn on/off event business_subscription_updated'),
    ('cb_created_for_business', 'Turn on/off event cb_created_for_business'),
    ('cb_created_for_customer', 'Turn on/off event cb_created_for_customer'),
    ('cb_customer_info_updated', 'Turn on/off event cb_customer_info_updated'),
    ('cb_finished_for_business', 'Turn on/off event cb_finished_for_business'),
    ('cb_finished_for_customer', 'Turn on/off event cb_finished_for_customer'),
    ('cb_no_show_for_business', 'Turn on/off event cb_no_show_for_business'),
    ('checkout_transaction_completed', 'Turn on/off event checkout_transaction_completed'),
    ('contact_preferences_updated', 'Turn on/off event contact_preferences_updated'),
    ('customer_app_opened', 'Turn on/off event Customer_App_Opened'),
    ('customer_registration_completed', 'Turn on/off event customer_registration_completed'),
    ('customer_search_query', 'Turn on/off event Customer_Search_Query'),
    ('1st_no_show_for_business', 'Turn on/off event 1st_no_show_for_business'),
    ('invite_all_clicked', 'Turn on/off event invite_all_clicked'),
    ('invite_process_completed', 'Turn on/off event invite_process_completed'),
    (
        'iterable_notification_center',
        'Turn on/off showing iterable push history in Notification Center',
    ),
    ('iterable_user_email_sync', "Sync user's email in iterable when changed"),
    ('kyc_failure', 'Turn on/off event kyc_failure'),
    ('kyc_success', 'Turn on/off event kyc_success'),
    ('onboarding_business_go_live', 'Turn on/off event onboarding_business_go_live'),
    ('paid_status_achieved', 'Turn on/off event paid_status_achieved'),
    ('payment_transaction_completed', 'Turn on/off event payment_transaction_completed'),
    ('postponed_invites_sent', 'Turn on/off event postponed_invites_sent'),
    ('protection_service_enabled', 'Turn on/off event protection_service_enabled'),
    ('review_completed', 'Turn on/off event review_completed'),
    ('staffer_created', 'Turn on/off event staffer_created'),
    ('user_language_set', 'Turn on/off event user_language_set'),
    ('view_item_list', 'Turn on/off event view_item_list'),
    ('view_item_list_in_query_hints', 'Turn on/off view_item_list event in query hints'),
    ('reports_appointments_by_days_and_hours', 'Reports -> AppointmentsByDaysAndHours'),
    ('reports_appointments_cancellations', 'Reports -> AppointmentsCancellations'),
    ('reports_appointments_list', 'Reports -> AppointmentsList'),
    ('reports_appointments_by_services', 'Reports -> AppointmentsListByServices'),
    ('reports_appointments_by_staffer', 'Reports -> AppointmentsListByStaffer'),
    ('reports_appointments_no_shows', 'Reports -> AppointmentsNoShows'),
    ('reports_appointments_summary', 'Reports -> AppointmentsSummary'),
    ('reports_categories_services_summary', 'Reports -> CategoriesServicesSummary'),
    ('reports_clients_list', 'Reports -> ClientsList'),
    ('reports_clients_no_shows', 'Reports -> ClientsNoShows'),
    ('reports_clients_summary', 'Reports -> ClientsSummaryReport'),
    ('reports_new_clients', 'Reports -> NewClients'),
    ('reports_potential_clients', 'Reports -> PotentialClientsReport'),
    ('reports_returning_clients', 'Reports -> ReturningClients'),
    ('reports_slipping_away_clients', 'Reports -> SlippingAwayClients'),
    ('reports_sales_log', 'Reports -> SalesLog'),
    ('reports_sales_trends', 'Reports -> SalesTrends'),
    ('reports_sales_summary', 'Reports -> SalesSummary'),
    ('reports_sales_by_products', 'Reports -> SalesByProducts'),
    ('reports_sales_by_services', 'Reports -> SalesByServices'),
    ('reports_taxes_summary', 'Reports -> TaxesSummary'),
    ('reports_sales_by_memberships', 'Reports -> SalesByMemberships'),
    ('reports_sales_by_gift_cards', 'Reports -> SalesByGiftCards'),
    ('reports_sales_by_packages', 'Reports -> SalesByPackages'),
    ('reports_memberships_summary', 'Reports -> MembershipsSummary'),
    ('reports_memberships_redemptions', 'Reports -> MembershipsRedemptions'),
    ('reports_packages_redemptions', 'Reports -> PackagesRedemptions'),
    ('reports_packages_summary', 'Reports -> PackagesSummary'),
    ('reports_revenue_forecast', 'Reports -> RevenueForecast'),
    ('reports_staff_revenue_forecast', 'Reports -> StaffRevenueForecast'),
    ('reports_discount', 'Reports -> DiscountsReport'),
    ('reports_appointments_profitability', 'Reports -> AppointmentsProfitability'),
    ('reports_outstanding_invoices', 'Reports -> OutstandingInvoices'),
    ('reports_cancellation_fees', 'Reports -> CancellationFeesReport'),
    ('reports_mobile_payments_log', 'Reports -> MobilePaymentsLogReport'),
    (
        'reports_mobile_payments_transactions_summary',
        'Reports -> MobilePaymentsTransactionsSummaryReport',
    ),
    ('reports_payout_batches', 'Reports -> PayoutBatchesReport'),
    ('reports_prepayments', 'Reports -> PrepaymentsReport'),
    ('reports_refunds', 'Reports -> RefundsReport'),
    ('reports_payment_types_summary', 'Reports -> PaymentTypesSummary'),
    ('reports_cash_registers_summary', 'Reports -> CashRegistersSummary'),
    ('reports_cash_registers_transactions', 'Reports -> CashRegistersTransactions'),
    ('reports_square_payments', 'Reports -> SquarePaymentsReport'),
    ('reports_bcr_transactions_summary', 'Reports -> BCRTransactionsSummaryReport'),
    ('reports_bcr_payout_batches', 'Reports -> BCRPayoutBatchesReport'),
    ('reports_bcr_log', 'Reports -> BCRLogReport'),
    ('reports_bcr_refunds', 'Reports -> BCRRefundsReport'),
    ('reports_stock_on_hand', 'Reports -> StockOnHand'),
    ('reports_low_stock', 'Reports -> LowStock'),
    ('reports_stock_value', 'Reports -> StockValue'),
    ('reports_stock_rotation', 'Reports -> StockRotation'),
    ('reports_stock_movement_summary', 'Reports -> StockMovementSummary'),
    ('reports_internal_stock_consumption_summary', 'Reports -> InternalStockConsumptionSummary'),
    (
        'reports_internal_stock_consumption_by_booking',
        'Reports -> InternalStockConsumptionByBooking',
    ),
    ('reports_staff_commission_details', 'Reports -> StaffCommissionDetails'),
    ('reports_staff_commission_summary', 'Reports -> StaffCommissionSummary'),
    ('reports_staff_performance_summary', 'Reports -> StaffPerformanceSummary'),
    ('reports_staff_gratuity', 'Reports -> StaffGratuity'),
    ('reports_staff_time_offs', 'Reports -> StaffTimeOffsReport'),
    ('reports_staff_working_hours', 'Reports -> StaffWorkingHoursReport'),
    ('reports_staff_revenue_payment_methods', 'Reports -> StaffRevenuePaymentMethods'),
    ('reports_boost_claims', 'Reports -> BoostClaimsReport'),
    ('reports_new_clients_from_boost', 'Reports -> NewClientsFromBoostReport'),
    ('reports_special_offers_summary', 'Reports -> SpecialOffersSummaryReport'),
    ('reports_business_performance_report', 'Reports -> BusinessPerformanceReport'),
]

NEW_NAMES = [
    'prompted_reviews_without_experiment',
]
KILLED_BY_DEFAULT = NEW_NAMES


def forward(apps, schema_editor):
    from webapps.kill_switch.utils import ensure_all_exist

    db_alias = schema_editor.connection.alias
    KillSwitchModel = apps.get_model('kill_switch', 'KillSwitch')
    ensure_all_exist(
        default_off=KILLED_BY_DEFAULT,
        model=KillSwitchModel,
        names=NEW_NAMES,
        using=db_alias,
    )


def revert(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    KillSwitch = apps.get_model('kill_switch', 'KillSwitch')
    KillSwitch.objects.using(db_alias).filter(
        name__in=NEW_NAMES,
    ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0092_prompted_reviews'),
    ]

    operations = [
        migrations.AlterField(
            model_name='killswitch',
            name='name',
            field=models.CharField(
                choices=CHOICES, max_length=60, primary_key=True, serialize=False
            ),
        ),
        migrations.RunPython(
            code=forward,
            reverse_code=revert,
        ),
    ]
