# Generated by Django 1.11.11 on 2018-09-07 10:22
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0165_merge_20180907_1021'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessTuning',
            fields=[
                (
                    'business',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='bst',
                        serialize=False,
                        to='business.Business',
                    ),
                ),
                (
                    'secondary_category_score',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
            ],
        ),
    ]
