from datetime import timedelta, datetime, time
from random import sample, shuffle, randint, choice
from unittest import mock
from unittest.mock import patch

import pytest
from django.shortcuts import reverse
from django.contrib.postgres.fields.ranges import DateRange
from freezegun import freeze_time
from model_bakery import baker
from pytz import UTC
from rest_framework import status
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature import StaffManagementRedesignFlag
from lib.tests.utils import override_feature_flag
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import resource_recipe
from webapps.business.models import Business
from webapps.business.models.models import Resource
from webapps.consts import WEB
from webapps.schedule.enums import STAFF_TIME_OFF_REASONS_CODES
from webapps.schedule.models import ResourceHours, ResourceTimeOff
from webapps.turntracker.baker_recipes import (
    tt_settings_on_recipe,
    tt_staffer_recipe,
    utc_business_recipe,
)
from webapps.turntracker.helpers import get_tt_staffers, sort_turntracker_staffers
from webapps.turntracker.models import <PERSON><PERSON><PERSON>ta<PERSON><PERSON><PERSON>, TrackerStaffer, TrackerSettings
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum


class TrackerAuthBaseSetUp(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )

        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.user = user_recipe.make()

    def setUp(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = utc_business_recipe.make()

    def build_url(self, business_id=None, staffer_id=None):
        return reverse(
            'turntracker_staffer_update',
            args=(business_id or self.business.id, staffer_id or self.resource.id),
        )


class TestTrackerStafferCheckins(TrackerAuthBaseSetUp):
    def setUp(self):
        super().setUp()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            active=True,
            order=1,
        )
        self.resources = resource_recipe.make(
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            active=True,
            _quantity=4,
            order=lambda: randint(2, 100),
        )
        self.resources.append(self.resource)
        self.staff = TrackerStaffList.get_or_create_staff(
            self.business.id, self.business.tznow.date()
        )

    def build_url(self, business_id=None):
        return reverse(
            'turntracker_staffers_availability',
            args=(business_id or self.business.id,),
        )

    def test_update_200_check_availability_is_true(self):
        response = self.client.put(self.build_url(), **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.filter(staff_list__id=self.staff.id).values_list(
            'available', flat=True
        )
        self.assertTrue(all(edited_staffer))

    def test_update_200_check_availability_is_false(self):
        response = self.client.put(self.build_url(), **self.headers, data={'available': False})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.filter(staff_list__id=self.staff.id).values_list(
            'available', flat=True
        )
        self.assertFalse(all(edited_staffer))

    def test_update_404(self):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()
        url = self.build_url()
        response = self.client.put(url, **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_200_only_today_staff_list(self):
        """Check that only today staff list will be updated"""
        date_3_days_before = datetime.today() - timedelta(days=3)
        self.staff.date = date_3_days_before
        self.staff.save()

        url = self.build_url()
        response = self.client.put(url, **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        staffers_from_3_days_before = TrackerStaffer.objects.filter(
            staff_list__id=self.staff.id
        ).values_list('available', flat=True)
        self.assertFalse(all(staffers_from_3_days_before))

        today_staff_list = TrackerStaffList.get_or_create_staff(
            self.business.id, self.business.tznow.date()
        )
        edited_staffer = TrackerStaffer.objects.filter(
            staff_list__id=today_staff_list.id
        ).values_list('available', flat=True)
        self.assertTrue(all(edited_staffer))


@override_feature_flag({StaffManagementRedesignFlag.flag_name: True})
class TestTrackerStafferUpdate(TrackerAuthBaseSetUp):
    def setUp(self):
        super().setUp()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            active=True,
            order=1,
        )
        self.resources = resource_recipe.make(
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            active=True,
            _quantity=4,
            order=lambda: randint(2, 100),
        )
        self.resources.append(self.resource)
        self.staff = TrackerStaffList.get_or_create_staff(
            self.business.id, self.business.tznow.date()
        )

    def test_prevent_from_setting_available_null(self):
        response = self.client.put(
            self.build_url(), **self.headers, data={'available': None}, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_200_check_availability(self):
        response = self.client.put(self.build_url(), **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.get(resource_id=self.resource.id)
        self.assertTrue(edited_staffer.available)

        response = self.client.put(self.build_url(), **self.headers, data={'available': False})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.get(resource_id=self.resource.id)
        self.assertFalse(edited_staffer.available)

    def test_update_200_check_order(self):
        response = self.client.put(self.build_url(), **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.get(resource_id=self.resource.id)
        edited_staffer.refresh_from_db()
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.ordered_staffers_ids[0], edited_staffer.resource_id)
        self.assertEqual(
            response.json()['staff_list']['ordered_staffers_ids'][0], edited_staffer.resource_id
        )

        response = self.client.put(
            self.build_url(staffer_id=self.resources[0].id),
            **self.headers,
            data={'available': True},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.get(resource_id=self.resources[0].id)
        edited_staffer.refresh_from_db()
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.ordered_staffers_ids[1], edited_staffer.resource_id)
        self.assertEqual(
            response.json()['staff_list']['ordered_staffers_ids'][1], edited_staffer.resource_id
        )

        response = self.client.put(self.build_url(), **self.headers, data={'available': False})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        edited_staffer = TrackerStaffer.objects.get(resource_id=self.resources[0].id)
        edited_staffer.refresh_from_db()
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.ordered_staffers_ids[0], edited_staffer.resource_id)
        self.assertEqual(
            response.json()['staff_list']['ordered_staffers_ids'][0], edited_staffer.resource_id
        )

    def test_update_400(self):
        response = self.client.put(self.build_url(), **self.headers, data={'available': 123})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_404_wrong_id(self):
        url = self.build_url(staffer_id=self.resource.id << 5)
        response = self.client.put(url, **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_404(self):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()
        url = self.build_url(staffer_id=self.resource.id)
        response = self.client.put(url, **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_queries_todays_date(self):
        """Check that staffer is queried by it's StaffList date."""

        # Update original staffList date, so the query will try to get today's and miss
        date_in_3_days = datetime.today() + timedelta(days=3)
        self.staff.date = date_in_3_days
        self.staff.save()

        url = self.build_url()
        response = self.client.put(url, **self.headers, data={'available': True})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        with freeze_time(date_in_3_days):
            url = self.build_url()
            response = self.client.put(url, **self.headers, data={'available': True})
            self.assertEqual(response.status_code, status.HTTP_200_OK)


class TestTrackerStaffList(TrackerAuthBaseSetUp):
    def setUp(self):
        super().setUp()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            active=True,
            order=5,
        )

        self.resource_wh = baker.make(
            ResourceHours,
            business_id=self.business.id,
            resource_id=self.resource.id,
            **dict.fromkeys(
                ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
                [(time(9), time(17))],
            ),
        )
        business_today = self.business.tznow.date()

        self.time_off = baker.make(
            ResourceTimeOff,
            business=self.business,
            reason_code=STAFF_TIME_OFF_REASONS_CODES[0],
            resource=self.resource,
            date_range=DateRange(business_today, business_today + timedelta(days=1)),
            hour_from=time(12),
            hour_till=time(13),
        )
        self.tracker_settings = baker.make(
            TrackerSettings, business_id=self.business.id, enabled=True
        )

    def build_url(self, business_id=None):
        return reverse(
            'turntracker_staffers',
            args=(business_id or self.business.id,),
        )

    def test_get_staff_list(self):
        tz_now = datetime.combine(
            self.business.tznow,
            time(8, 59),
        )
        with freeze_time(tz_now):
            response = self.client.get(self.build_url(), **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        tt_staff = TrackerStaffList.objects.get(business_id=self.business.id, date=tz_now.date())
        staffer = TrackerStaffer.objects.get(staff_list=tt_staff, resource_id=self.resource.id)

        response_json = response.json()
        self.assertEqual(len(response_json['tt_staffers']), 1)
        tt_staffer = response_json['tt_staffers'][0]
        self.assertEqual(tt_staffer.pop('resource_id'), staffer.resource_id)
        self.assertEqual(tt_staffer.pop('revenue'), 0)
        self.assertEqual(tt_staffer.pop('turns'), 0)
        self.assertEqual(
            tt_staffer.pop('working_hours'),
            [
                {'from': '09:00:00', 'to': '12:00:00'},
                {'from': '13:00:00', 'to': '17:00:00'},
            ],
        )
        self.assertFalse(tt_staffer.pop('available'))
        self.assertFalse(tt_staffer)

    def test_get_staff_list_staffer_not_working_today(self):
        self.resource_wh.delete()
        response = self.client.get(self.build_url(), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.json()['tt_staffers'][0]['working_hours'])

    def test_get_staff_list_creates_staff_once(self):
        """Ensure that API only once creates new TT Staff for today,
        if none exists"""
        additional_staffer = resource_recipe.make(
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            active=True,
            order=4,
        )
        self.assertFalse(
            TrackerStaffList.objects.filter(
                business_id=self.business.id, date=self.business.tznow.date()
            ).exists()
        )

        url = self.build_url()

        for _ in range(2):
            response = self.client.get(url, **self.headers)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            json = response.json()['tt_staffers']
            self.assertEqual(json[0]['resource_id'], additional_staffer.id)
            self.assertEqual(json[1]['resource_id'], self.resource.id)

            tt_staff = TrackerStaffList.objects.get(
                business_id=self.business.id, date=self.business.tznow.date()
            )
            self.assertTrue(
                TrackerStaffer.objects.get(staff_list=tt_staff, resource_id=self.resource.id)
            )


class TestTrackerStaffOrderUpdate(TrackerAuthBaseSetUp):
    def setUp(self):
        super().setUp()
        self.resources = resource_recipe.make(
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            active=True,
            _quantity=4,
            order=lambda: randint(0, 100),
        )
        self.resources.append(
            resource_recipe.make(
                business=self.business,
                staff_user=self.user,
                type=Resource.STAFF,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_RECEPTION,
                active=True,
                order=lambda: randint(0, 100),
            )
        )
        self.tt_staff = TrackerStaffList.get_or_create_staff(
            self.business.id, self.business.tznow.date()
        )
        self.resource_ids = [
            r.id for r in sorted(self.resources, key=lambda res: (res.order, res.id))
        ]

        self.assertEqual(self.tt_staff.ordered_staffers_ids, self.resource_ids)

    def build_url(self, business_id=None):
        return reverse(
            'turntracker_staff_order_update',
            args=(business_id or self.business.id,),
        )

    def test_order_update_okay(self):
        url = self.build_url()
        new_order_of_resources = sample(self.resource_ids, len(self.resource_ids))
        resp = self.client.put(
            url, **self.headers, data=dict(ordered_staffers_ids=new_order_of_resources)
        )

        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertTrue(
            self.tt_staff.refresh_from_db()
            or self.tt_staff.ordered_staffers_ids == new_order_of_resources
        )

    def test_order_update_errors(self):
        url = self.build_url()
        new_order_of_resources = [24, 8, 2022]  # Invalid ids
        resp = self.client.put(
            url, **self.headers, data=dict(ordered_staffers_ids=new_order_of_resources)
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

        new_order_of_resources = self.resource_ids[:-1]  # Wrong number of ids
        resp = self.client.put(
            url, **self.headers, data=dict(ordered_staffers_ids=new_order_of_resources)
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)


@pytest.mark.django_db
def test_get_staffers_returns_order_of_staffers_from_stafflist_instance():
    business = utc_business_recipe.make()
    tt_settings_on_recipe.make(business_id=business.id)
    resources = resource_recipe.make(
        business=business,
        type=Resource.STAFF,
        active=True,
        _quantity=5,
        order=lambda: randint(0, 100),
    )
    tracker_staff = TrackerStaffList.get_or_create_staff(business.id, business.tznow.date())
    # Ensure that initial order of staffers derived from (resource.order, resource.id)
    assert tracker_staff.ordered_staffers_ids == [
        r.id for r in sorted(resources, key=lambda res: (res.order, res.id))
    ]
    assert tracker_staff.trackerstaffer_set.count() == 5

    shuffle(resources)

    tracker_staff.ordered_staffers_ids = [r.id for r in resources]
    tracker_staff.save() or tracker_staff.refresh_from_db()  # pylint: disable=expression-not-assigned

    # Ensure that the order of staffers is the same as the one stored in TrackerStaffList
    tt_staffers = get_tt_staffers(tracker_staff)
    assert [staffer['id'] for staffer in tt_staffers] == tracker_staff.ordered_staffers_ids


@pytest.mark.django_db
@pytest.mark.parametrize(
    'staffer_availability',
    [
        True,
        False,
    ],
)
def test_get_staffers_availability_is_set_manually(staffer_availability):
    """Test that staffers availability corresponds to latest toggled state."""
    business = utc_business_recipe.make()
    tt_settings_on_recipe.make(business_id=business.id)
    resource = resource_recipe.make(
        business=business,
        type=Resource.STAFF,
        active=True,
    )

    tracker_staff = TrackerStaffList.get_or_create_staff(business.id, business.tznow.date())
    staffer = TrackerStaffer.objects.get(resource_id=resource.id, staff_list=tracker_staff)
    staffer.available = staffer_availability
    staffer.save()

    tt_staffers = get_tt_staffers(tracker_staff)
    assert len(tt_staffers) == 1
    assert tt_staffers[0]['available'] == bool(staffer_availability)
    assert not tt_staffers[0]['working_hours']


@pytest.mark.django_db
def test_get_staffers_null_availability():
    business = utc_business_recipe.make()
    tt_settings_on_recipe.make(business_id=business.id)
    resource = resource_recipe.make(
        business=business,
        type=Resource.STAFF,
        active=True,
    )
    business_today = business.tznow.date()
    wh_start, wh_end = (time(9), time(17))

    tracker_staff = TrackerStaffList.get_or_create_staff(business.id, business_today)
    staffer = TrackerStaffer.objects.get(resource_id=resource.id, staff_list=tracker_staff)
    assert staffer.available is None

    # No working hours - not available
    tt_staffers = get_tt_staffers(tracker_staff)
    assert len(tt_staffers) == 1
    assert tt_staffers[0]['available'] is False
    assert not tt_staffers[0]['working_hours']

    with mock.patch.object(
        Business,
        'tznow',
        datetime.combine(business_today, time(9, 1), tzinfo=UTC),
    ):
        # Add Working Hours to resource
        baker.make(
            ResourceHours,
            business_id=business.id,
            resource_id=resource.id,
            **dict.fromkeys(
                ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
                [(wh_start, wh_end)],
            ),
        )
        tt_staffers = get_tt_staffers(tracker_staff)
        assert tt_staffers[0]['available'] is True
        assert tt_staffers[0]['working_hours']

    for out_current_time in [
        time(8, 59),
        time(17, 1),
    ]:
        with mock.patch.object(
            Business,
            'tznow',
            datetime.combine(business_today, out_current_time, tzinfo=UTC),
        ):
            tt_staffers = get_tt_staffers(tracker_staff)
            assert len(tt_staffers) == 1
            # Regardless of having Working Hours the staffer is unavailable
            assert tt_staffers[0]['available'] is False
            assert tt_staffers[0]['working_hours']


@pytest.mark.django_db
def test_get_all_staffers_from_grid():
    """Ensure all the staffers (available=True|False|None) have
    been passed to grid for staff_management."""
    business = utc_business_recipe.make()
    settings = tt_settings_on_recipe.make(business_id=business.id)
    resources = resource_recipe.make(
        business=business, type=Resource.STAFF, active=True, _quantity=5
    )

    staff_list = baker.make(
        TrackerStaffList,
        business_id=business.id,
        date=datetime.utcnow().date(),
    )
    tt_staffer_recipe.make(
        resource_id=(r.id for r in resources),
        staff_list=staff_list,
        available=(lambda: choice([True, False, None])),
    )

    staff_ids = [r.id for r in resources]
    staff_list.ordered_staffers_ids = staff_ids.copy()
    staff_list.save()
    staff_list.refresh_from_db()

    with (
        patch(
            'webapps.turntracker.grid.source.GridSourceBuilder._get_bookings', return_value=[]
        ) as get_bookings_mock,
        patch(
            'webapps.turntracker.grid.source.GridSourceBuilder._get_tiles', return_value=[]
        ) as get_tiles_mock,
    ):
        tt_staffers = get_tt_staffers(staff_list)
        assert len(tt_staffers) == 5
        get_bookings_mock.assert_called_once()
        get_tiles_mock.assert_called_once()
        assert get_bookings_mock.call_args.args == (staff_list.ordered_staffers_ids, settings)
        assert get_tiles_mock.call_args.args == (staff_list.ordered_staffers_ids,)


@pytest.mark.django_db
def test_sort_staffer_per_availability():
    """Sorts the list of staffers to group them considering their availability and previous order"""
    business = utc_business_recipe.make()
    tt_settings_on_recipe.make(business_id=business.id)
    resources = resource_recipe.make(
        business=business, type=Resource.STAFF, active=True, _quantity=8
    )

    tracker_staff = TrackerStaffList.get_or_create_staff(business.id, business.tznow.date())
    available_staffer_ids = (resources[3].id, resources[5].id, resources[6].id)
    unavailable_staffer_ids = (resources[2].id, resources[4].id)
    available_staffers = list(TrackerStaffer.objects.filter(resource_id__in=available_staffer_ids))
    unavailable_staffers = list(
        TrackerStaffer.objects.filter(resource_id__in=unavailable_staffer_ids)
    )
    for staffer in available_staffers:
        staffer.available = True
    for staffer in unavailable_staffers:
        staffer.available = False
    TrackerStaffer.objects.bulk_update(available_staffers + unavailable_staffers, ['available'])

    sort_turntracker_staffers(tracker_staff)

    assert list(
        TrackerStaffer.objects.filter(
            resource_id__in=tracker_staff.ordered_staffers_ids[:3]
        ).values_list('available', flat=True)
    ) == [True, True, True]
    assert list(
        TrackerStaffer.objects.filter(
            resource_id__in=tracker_staff.ordered_staffers_ids[3:5]
        ).values_list('available', flat=True)
    ) == [False, False]
    assert list(
        TrackerStaffer.objects.filter(
            resource_id__in=tracker_staff.ordered_staffers_ids[5:]
        ).values_list('available', flat=True)
    ) == [None, None, None]
