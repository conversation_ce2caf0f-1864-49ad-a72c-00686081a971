import pytest

from mock import Mock, call

from webapps.user.v2.application.services.validators import BusinessUserEmailValidator


class TestBusinessUserEmailValidator:

    @pytest.mark.parametrize('flag_value', [True, False])
    def test_account_validate(self, flag_value: bool):
        mocked_domain_service = Mock()
        application_service = BusinessUserEmailValidator(
            user_email_validator_service=mocked_domain_service
        )

        application_service.validate(email='<EMAIL>', verify_domain_disposable=flag_value)

        assert mocked_domain_service.method_calls == [
            call.user_exists(email='<EMAIL>', verify_domain_disposable=flag_value),
        ]
