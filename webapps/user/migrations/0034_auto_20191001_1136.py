# Generated by Django 2.0.13 on 2019-10-01 11:36
from django.conf import settings
from django.db import migrations, models
import webapps.user.models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0033_add_city_and_zipcode_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='businessaccountinvite',
            name='language',
            field=models.CharField(
                choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE[:2], max_length=5
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='language',
            field=models.CharField(
                choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE[:2], max_length=5
            ),
        ),
    ]
