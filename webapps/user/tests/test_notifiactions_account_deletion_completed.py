from django.test import override_settings, TestCase
from parameterized import parameterized

from country_config import Country

from webapps.notification.channels import EmailChannel
from webapps.user.baker_recipes import user_recipe
from webapps.user.notifications import UserDeletionCompletedNotification

country_email_mapping = [
    (Country.PL, '<EMAIL>'),
    (Country.GB, '<EMAIL>'),
    (Country.US, '<EMAIL>'),
    (Country.ES, '<EMAIL>'),
    (Country.IT, '<EMAIL>'),
]


class TestUserDeletionCompletedNotification(TestCase):
    @parameterized.expand(country_email_mapping)
    def test_content(self, country, expected_email):
        user = user_recipe.make()
        with override_settings(API_COUNTRY=country):
            notification = UserDeletionCompletedNotification(
                user, old_email='<EMAIL>', language='pl'
            )
            content = EmailChannel(notification).get_content()
            self.assertIn(expected_email, content)
            self.assertIn(f"[{country.upper()}]", content)
            self.assertIn(f"[{user.id}]", content)
