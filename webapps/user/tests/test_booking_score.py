#!/usr/bin/env python
from datetime import datetime, timedelta
from mock import patch

import pytest
from model_bakery import baker

from lib.datetime_utils import TZ_UTC
from lib.test_utils import create_subbooking
from webapps.booking.models import Appointment
from webapps.search_engine_tuning.models import UserTuning
from webapps.user.models import User
from webapps.user.tasks.booking_score import update_users_booking_score


@pytest.mark.django_db
def test_user_initial_status():
    user = baker.make(User)
    assert user.is_bad_user is False


@pytest.mark.django_db
def test_user_update_score_without_change():
    source = baker.make('booking.BookingSources')
    user = baker.make(User)
    business = baker.make('business.Business')
    bci = baker.make('business.BusinessCustomerInfo', business=business, user=user)
    # BCI with no bookings
    baker.make('business.BusinessCustomerInfo', business=baker.make('business.Business'), user=user)

    booking, *_ = create_subbooking(  # pylint: disable=unused-variable
        business=business,
        booking_kws=dict(
            source=source,
            updated_by=user,
            booked_for=bci,
            type=Appointment.TYPE.CUSTOMER,
        ),
    )
    user.refresh_from_db()
    assert user.booking_score == User.INITIAL_SCORE


BOOKED_DATE = datetime(2019, 3, 4, tzinfo=TZ_UTC)


@pytest.mark.parametrize(
    ('status', 'score', 'booked_from', 'status_changed'),
    (
        pytest.param(
            Appointment.STATUS.FINISHED,
            User.INITIAL_SCORE + User.FINISHED_SCORE,
            BOOKED_DATE,
            BOOKED_DATE,
            id='Finished booking',
        ),
        pytest.param(
            Appointment.STATUS.NOSHOW,
            User.INITIAL_SCORE - User.NOSHOW_PENALTY,
            BOOKED_DATE,
            BOOKED_DATE,
            id='No-show booking',
        ),
        pytest.param(
            Appointment.STATUS.CANCELED,
            User.INITIAL_SCORE - User.CANCELLATION_PENALTY,
            BOOKED_DATE,
            BOOKED_DATE - timedelta(hours=-1),
            id='SubBooking canceled post event',
        ),
        pytest.param(
            Appointment.STATUS.CANCELED,
            User.INITIAL_SCORE - User.CANCELLATION_PENALTY + 1,
            BOOKED_DATE,
            BOOKED_DATE - timedelta(minutes=30),
            id='SubBooking canceled just before event',
        ),
        pytest.param(
            Appointment.STATUS.CANCELED,
            User.INITIAL_SCORE,
            BOOKED_DATE,
            BOOKED_DATE - timedelta(days=3),
            id='SubBooking canceled early enough before event',
        ),
    ),
)
@pytest.mark.django_db
def test_user_update_score_with_change(status, score, booked_from, status_changed):
    user = baker.make_recipe('webapps.user.user_recipe')
    source = baker.make_recipe('webapps.booking.booking_source_recipe')
    with patch('django.utils.timezone.now', return_value=status_changed):
        create_subbooking(
            business=None,
            booking_kws=dict(
                booked_for__user=user,
                updated_by=user,
                status=status,
                type=Appointment.TYPE.CUSTOMER,
                source=source,
                booked_from=booked_from,
                _save_related=True,
            ),
        )
    user.update_booking_score()
    assert user.booking_score == score


@pytest.mark.django_db
def test_bad_user_score():
    source = baker.make('booking.BookingSources')
    user = baker.make(User)
    for _ in range(3):
        business = baker.make('business.Business')
        bci = baker.make('business.BusinessCustomerInfo', business=business, user=user)
        create_subbooking(
            business=business,
            booking_kws=dict(
                source=source,
                updated_by=user,
                booked_for=bci,
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.NOSHOW,
            ),
        )
    user.update_booking_score()
    assert user.is_bad_user is True


@pytest.mark.django_db
def test_update_users_booking_score__all_users():
    # delete all created UserTunings
    UserTuning.all_objects.all().delete()
    users = baker.make_recipe('webapps.user.user_recipe', _quantity=3)
    with patch.object(User, 'update_users_booking_score') as update_mock:
        update_users_booking_score.delay(update_all_users=True)

        update_mock.assert_called_once()
        user_ids_called = update_mock.call_args_list[0][0][0]
        assert {elem.id for elem in users} == set(user_ids_called)


@pytest.mark.parametrize(
    (
        'score',
        'booking_status',
        'expected',
    ),
    (
        pytest.param(
            -500,
            Appointment.STATUS.FINISHED,
            0,
            id='permanent_bad_user',
        ),
        pytest.param(
            -100,
            Appointment.STATUS.FINISHED,
            1,
            id='bad_user',
        ),
        pytest.param(
            100,
            Appointment.STATUS.FINISHED,
            1,
            id='no_status',
        ),
        pytest.param(
            500,
            Appointment.STATUS.FINISHED,
            0,
            id='good_user',
        ),
        pytest.param(
            500,
            Appointment.STATUS.NOSHOW,
            1,
            id='good_user_no_show',
        ),
    ),
)
@pytest.mark.django_db
def test_update_users_booking_score(score, booking_status, expected):
    with patch.object(User, 'append_to_booking_score_river') as update_mock:
        user = baker.make_recipe('webapps.user.user_recipe', booking_score=score)
        source = baker.make_recipe('webapps.booking.booking_source_recipe')
        booking, *_ = create_subbooking(  # pylint: disable=unused-variable
            business=None,
            booking_kws=dict(
                booked_for__user=user,
                updated_by=user,
                status=booking_status,
                source=source,
                _save_related=True,
            ),
        )
        booking.appointment.update_appointment()
        update_users_booking_score.delay()

        assert update_mock.call_count == expected
        if expected:
            update_mock.assert_called_with(user.id)
