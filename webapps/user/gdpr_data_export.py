import io
import logging

from django.utils.translation import gettext as _

from lib import jinja_renderer
from lib.email import send_email
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_customer_info.serializers.serializers import (
    BusinessCustomerInfoExportSerializer,
)
from webapps.notification.models import NotificationHistory
from webapps.user.models import UserProfile
from webapps.user.serializers import CustomerDataExportSerializer


log = logging.getLogger('booksy.gdpr_data_export')


class CustomerDataExporter:
    """Used for exporting customers' data for their request
    and sending it to them."""

    email_subject = _('Customer data info')

    def __init__(self, user_profile_ids):
        self.user_profile_ids = user_profile_ids

    def get_user_profiles(self):
        """Gets all UserProfile objects needed."""
        user_profiles = UserProfile.objects.filter(
            id__in=self.user_profile_ids,
            profile_type=UserProfile.Type.CUSTOMER,
        ).select_related(
            'photo',
            'region',
            'source',
            'user',
        )

        return user_profiles

    @staticmethod
    def get_business_customer_cards(auth_user_id):
        """
        Gets all info associated with business for given customer
        (BusinessCustomerInfo and Booking objects). 1 customer (UserProfile)
        can have multiple customer cards (BusinessCustomerInfo, 1 per business)
        """
        business_customer_cards = (
            BusinessCustomerInfo.objects.filter(user_id=auth_user_id)
            .select_related(
                'business',
                'photo',
                'region',
            )
            .prefetch_related(
                'appointments',
                'more_photos',
            )
        )
        return business_customer_cards

    @staticmethod
    def render_email_template(language):
        sjr = jinja_renderer.ScenariosJinjaRenderer()
        body = sjr.render(
            scenario_name='gdpr_export',
            template_name='user_profile_customer_export',
            language=language,
        )
        return body

    @staticmethod
    def get_history_data(user_id):
        # customer_id == user.User.id
        history_data = {
            'task_id': f'gdpr_export:customer_id={user_id}',
            'task_type': NotificationHistory.TASK_TYPE__GDPR_EXPORT,
            'customer_id': user_id,
        }
        return history_data

    def send_email(self, user_profile, attachment_body):
        try:
            send_email(
                to_addr=user_profile.user.email,
                subject=self.email_subject,
                body=self.render_email_template(user_profile.language),
                attachments=[
                    (
                        'customer_data_info.txt',
                        attachment_body.getvalue().encode(),
                        'text/plain',
                    ),
                ],
                history_data=self.get_history_data(user_profile.user_id),
            )
        except Exception as e:  # pylint: disable=broad-except
            log.exception(
                "Error while sending email in customer's data export." "User profile id: %s. %s",
                user_profile.id,
                e,
            )

    def send_data(self):
        """Main method"""
        user_profiles = self.get_user_profiles()
        sjr = jinja_renderer.ScenariosJinjaRenderer()

        for user_profile in user_profiles:
            user_data_serializer = CustomerDataExportSerializer(user_profile)
            b_customer_cards_serializer = BusinessCustomerInfoExportSerializer(
                self.get_business_customer_cards(user_profile.user_id),
                many=True,
            )
            customer_data_file = io.StringIO()

            content = sjr.render(
                scenario_name='gdpr_export',
                template_name='user_profile_customer_export_attachment',
                language=user_profile.language,
                template_args={
                    'user_profile': user_data_serializer.data,
                    'business_customer_cards': b_customer_cards_serializer.data,
                },
                extension='txt',
            )
            customer_data_file.write(content)
            self.send_email(user_profile, customer_data_file)
