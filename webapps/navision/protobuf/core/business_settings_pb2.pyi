from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class InvoicingAllowedRequest(_message.Message):
    __slots__ = ["business_id", "country_code", "invoicing_target", "sale_service"]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_CODE_FIELD_NUMBER: _ClassVar[int]
    INVOICING_TARGET_FIELD_NUMBER: _ClassVar[int]
    SALE_SERVICE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    country_code: str
    invoicing_target: int
    sale_service: str
    def __init__(self, country_code: _Optional[str] = ..., business_id: _Optional[int] = ..., sale_service: _Optional[str] = ..., invoicing_target: _Optional[int] = ...) -> None: ...

class InvoicingAllowedResponse(_message.Message):
    __slots__ = ["invoicing_allowed", "reason"]
    INVOICING_ALLOWED_FIELD_NUMBER: _ClassVar[int]
    REASON_FIELD_NUMBER: _ClassVar[int]
    invoicing_allowed: bool
    reason: str
    def __init__(self, invoicing_allowed: bool = ..., reason: _Optional[str] = ...) -> None: ...
