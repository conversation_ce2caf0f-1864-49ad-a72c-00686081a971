from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from unittest.mock import MagicMock, patch

from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from django.utils.timezone import make_aware
from freezegun import freeze_time
from model_bakery import baker

from lib.tools import tznow
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.tests.utils import create_boost_finished_visit, create_boost_transaction
from webapps.business.models import Business
from webapps.kill_switch.models import KillSwitch
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.enums import InvoicePaymentSourceType, InvoiceService
from webapps.navision.models import Invoice, InvoiceItem, InvoicingError, InvoicingSummary, Merchant
from webapps.navision.tasks.offline import create_offline_boost_invoices_task
from webapps.navision.tests.utils import get_or_create_invoice_mock
from webapps.purchase.models import Subscription, SubscriptionBuyer, SubscriptionListing
from webapps.user.models import User


@patch(
    'webapps.navision.api_clients.navision_v1.NavisionInvoiceAPIClient.get_or_create_invoice',
    new=MagicMock(side_effect=get_or_create_invoice_mock),
)
@override_settings(
    SUPPORTED_INVOICE_PAYMENT_METHODS={'O': {'Boost'}},
    NAVISION_BRAINTREE_BOOST_CYCLE_END_TO_INVOICE_DAYS=14,
)
class TestBoostOfflineMixin(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make()

    def setUp(self) -> None:
        super().setUp()
        self.business, self.merchant, self.buyer, self.product = self.create_business(False)

    @staticmethod
    def create_business(return_only_business=True):
        merchant = baker.make(Merchant)
        buyer = baker.make(
            SubscriptionBuyer,
            merchant=merchant,
        )
        business = boosted_business_recipe.make(
            boost_payment_source=Business.PaymentSource.OFFLINE,
            buyer=buyer,
            active=True,
        )
        product: SubscriptionListing = baker.make(
            SubscriptionListing,
            name='Some Product',
            active=True,
            price_amount=20.0,
            discount_amount=5.0,
            currency_code='USD',
        )
        if return_only_business:
            return business
        return business, merchant, buyer, product


@freeze_time(datetime(2022, 5, 15, 17, 45), tick=True)
class TestBoostInvoicing(TestBoostOfflineMixin):
    def create_transaction_row(self):
        now = tznow()
        subscription_start = now - timedelta(days=15)
        baker.make(
            Subscription,
            product=self.product,
            business=self.business,
            buyer=self.buyer,
            source=Business.PaymentSource.OFFLINE,
            start=subscription_start,
            expiry=subscription_start + relativedelta(months=2),
        )

        self.business.boost_payment_source = Business.PaymentSource.OFFLINE
        self.business.save(update_fields=['boost_payment_source'])

        create_boost_transaction(
            business=self.business,
            hard_frozen=True,
            boost_amount=100.0,
            booked_from=tznow().replace(day=1) - relativedelta(months=1, days=-1),
        )

    def test_boost_offline_invoice(self):
        self.create_transaction_row()

        create_offline_boost_invoices_task()
        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        self.assertEqual(Invoice.objects.count(), 1)
        invoice = Invoice.objects.first()

        self.assertEqual(invoice.source, Business.PaymentSource.OFFLINE)
        self.assertEqual(invoice.service, Invoice.Service.BOOST)

        self.assertEqual(invoice.items.count(), 1)
        item = invoice.items.first()

        self.assertEqual(item.service, Invoice.Service.BOOST)
        self.assertEqual(item.base_gross_value, Decimal('100.0'))
        self.assertEqual(item.quantity, 1)

    def test_boost_offline_is_not_double_invoiced(self):
        self.create_transaction_row()

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)

    def test_boost_offline_some_items_already_invoiced(self):
        self.create_transaction_row()

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        self.assertEqual(InvoiceItem.objects.count(), 1)

        item = InvoiceItem.objects.first()
        appt = item.invoiced_object.appointment

        with freeze_time(tznow() + relativedelta(days=31)):
            appt.booked_till = appt.booked_till + relativedelta(days=31)
            appt.save()
            self.create_transaction_row()
            self.create_transaction_row()
            create_offline_boost_invoices_task()

        self.assertEqual(2, Invoice.objects.count())
        self.assertEqual(3, InvoiceItem.objects.count())

    def test_operator(self):
        operator = baker.make(User)
        create_offline_boost_invoices_task.delay(operator_id=operator.id)
        self.assertEqual(operator.id, InvoicingSummary.objects.first().operator.id)

    def test_no_operator(self):
        create_offline_boost_invoices_task.delay()
        self.assertEqual(1, InvoicingSummary.objects.count())
        self.assertIsNone(InvoicingSummary.objects.first().operator)

    def test_invalid_operator(self):
        operator = baker.make(User)
        create_offline_boost_invoices_task.delay(operator_id=operator.id + 1)
        self.assertEqual(1, InvoicingSummary.objects.count())
        self.assertIsNone(InvoicingSummary.objects.first().operator)


@freeze_time(datetime(2022, 5, 15, 17, 45), tick=True)
class TestBoostOfflineInvoicingSpecialCases(TestBoostOfflineMixin):
    def tearDown(self) -> None:
        self.assertEqual(
            InvoicingError.objects.count(),
            0,
            ', '.join(e.error for e in InvoicingError.objects.all()),
        )

        return super().tearDown()

    def test_invoice_business_without_boost_appointments_do_nothing(self):
        create_offline_boost_invoices_task.delay()

        self.assertEqual(Invoice.objects.count(), 0)

    def test_invoice_business_with_single_boost_appointment_last_month(self):
        create_boost_finished_visit(
            business=self.business,
            subbookings=[
                {
                    'booked_from': make_aware(datetime(2022, 4, 1)),
                }
            ],
            hard_frozen=True,
            booked_till=make_aware(datetime(2022, 4, 2)),
        )

        create_offline_boost_invoices_task.delay()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()

        self.assertEqual(invoice_item.base_gross_value, Decimal('1.00'))
        self.assertIsNone(invoice_item.charge_completed)

    @freeze_time(datetime(2022, 5, 3, 12, 45))
    def test_invoicing_before_15th(self):
        create_boost_finished_visit(
            business=self.business,
            subbookings=[{'booked_from': make_aware(datetime(2022, 4, 4))}],
            hard_frozen=True,
            booked_till=make_aware(datetime(2022, 4, 5)),
        )

        create_offline_boost_invoices_task.delay()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()

        self.assertEqual(invoice_item.base_gross_value, Decimal('1.00'))
        self.assertIsNone(invoice_item.charge_completed)


@freeze_time(datetime(2022, 5, 15, 17, 45), tick=True)
class TestBoostOfflineAfterOnlineMigration(TestBoostOfflineMixin):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        KillSwitch.objects.get_or_create(
            name=KillSwitch.System.BILLING_OFFLINE_MIGRATION,
            defaults={'is_killed': False},
        )

    def create_appt(self, booked_from: datetime = make_aware(datetime(2022, 4, 5))):
        create_boost_finished_visit(
            business=self.business,
            subbookings=[{'booked_from': booked_from}],
            hard_frozen=True,
        )

    def test_migration_on_first_day_of_the_month(self):
        self.create_appt(make_aware(datetime(2022, 4, 1, 11, 20)))

        baker.make(
            'billing.BillingOfflineMigration',
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            agreed_at=make_aware(datetime(2022, 4, 1, 20, 40)),
        )

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()

        self.assertEqual(invoice_item.billing_cycle_end, make_aware(datetime(2022, 4, 1, 0, 0)))

    def test_no_migration(self):
        self.create_appt()

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()

        self.assertEqual(invoice_item.billing_cycle_end, make_aware(datetime(2022, 4, 30, 0, 0)))

    def test_migration_20_days_ago(self):
        self.create_appt()

        baker.make(
            'billing.BillingOfflineMigration',
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            agreed_at=make_aware(datetime(2022, 4, 25, 14, 40)),
        )

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()

        self.assertEqual(invoice_item.billing_cycle_end, make_aware(datetime(2022, 4, 24, 0, 0)))

    def test_appointment_on_migration_day_before_migration(self):
        self.create_appt()

        migration_date = (2022, 4, 25)
        create_boost_finished_visit(
            business=self.business,
            subbookings=[{'booked_from': make_aware(datetime(*migration_date, 10, 10))}],
            hard_frozen=True,
        )
        baker.make(
            'billing.BillingOfflineMigration',
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            agreed_at=make_aware(datetime(*migration_date, 14, 40)),
        )
        create_boost_finished_visit(
            business=self.business,
            subbookings=[{'booked_from': make_aware(datetime(*migration_date, 20, 20))}],
            hard_frozen=True,
        )

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        invoice_item = InvoiceItem.objects.first()
        self.assertEqual(invoice_item.billing_cycle_end, make_aware(datetime(2022, 4, 24, 0, 0)))

    def test_no_boost_appointments_before_migration(self):
        self.create_appt()

        baker.make(
            'billing.BillingOfflineMigration',
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            agreed_at=make_aware(datetime(2022, 4, 3, 14, 40)),
        )

        create_offline_boost_invoices_task()

        self.assertEqual(Invoice.objects.count(), 0)
        self.assertEqual(InvoiceItem.objects.count(), 0)

    def test_one_biz_after_migration_one_offline(self):
        self.create_appt()

        self.business_2 = self.create_business()
        create_boost_finished_visit(
            business=self.business_2,
            subbookings=[{'booked_from': make_aware(datetime(2022, 4, 4, 15))}],
            hard_frozen=True,
        )
        baker.make(
            'billing.BillingOfflineMigration',
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            agreed_at=make_aware(datetime(2022, 4, 25, 14, 40)),
        )

        create_offline_boost_invoices_task()

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.BOOST)
        self.assertEqual(summary.source, InvoicePaymentSourceType.OFFLINE)

        self.assertEqual(Invoice.objects.count(), 2)
        self.assertEqual(InvoiceItem.objects.count(), 2)
        self.assertTrue(
            InvoiceItem.objects.filter(
                billing_cycle_end=make_aware(datetime(2022, 4, 24)),
            ).exists()
        )
        self.assertTrue(
            InvoiceItem.objects.filter(
                billing_cycle_end=make_aware(datetime(2022, 4, 30)),
            ).exists()
        )
