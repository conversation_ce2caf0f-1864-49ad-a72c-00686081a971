import typing as t
from datetime import date
from collections import defaultdict, OrderedDict
from itertools import chain

from django.conf import settings
from django.db.models.query_utils import Q
from django.utils.functional import cached_property

from service.business.serializers import (
    MonthlyBookingsResponseSerializer,
    MonthlyBookingsRequestSerializer,
)
from service.tools import RequestHand<PERSON>, session
from webapps.booking.models import Appointment
from webapps.booking.models import SubBooking
from webapps.booking.serializers.utils import get_business_calendar_resources
from webapps.business.models import Resource, Business
from webapps.schedule.legacy_serializers import LegacyScheduleSerializer
from webapps.schedule.tools import WorkingHours


class MonthlyBookingTimeOffUpdater:
    """Extracts time off information for business resources
    in given date range.
    """

    def __init__(
        self,
        business: Business,
        start_date: date,
        end_date: date,
        resource_ids: t.Optional[list] = None,
    ):
        self.business = business
        self.start_date = start_date
        self.end_date = end_date
        self.resource_ids = resource_ids or []

    @cached_property
    def is_single_resource(self):
        return len(self.resource_ids) == 1

    @staticmethod
    def as_date(value: date) -> str:
        """Return date as formatted string."""
        return value.strftime(settings.DATE_FORMAT)

    @property
    def working_hours(self) -> WorkingHours:
        return WorkingHours(
            business=self.business,
            start_date=self.start_date,
            end_date=self.end_date,
            resource_ids=self.resource_ids,
        )

    @cached_property
    def schedule_data(self):
        return LegacyScheduleSerializer(
            instance=self.working_hours.schedule(with_free_hours=True),
        ).data

    @cached_property
    def business_time_off(self) -> dict:
        """Time off days for business."""
        business_data = self.schedule_data['business']
        return {
            day['date']: {'time_off': True}
            for day in business_data['schedule']
            if day['hours'] == []
        }

    @cached_property
    def resources_schedule(self) -> dict:
        """Resources schedules"""
        result = defaultdict(lambda: {})

        for resource_data in self.schedule_data['resources']:
            resource_id = resource_data['resource']['id']
            for day_data in resource_data.get('schedule', []):
                date_ = day_data['date']

                if day_data['time_off']:
                    try:
                        result[date_]['time_offs'].append(resource_id)
                    except KeyError:
                        result[date_]['time_offs'] = [resource_id]

                if not self.is_single_resource:
                    continue

                result[date_]['working_hours'] = {
                    resource_id: day_data['hours'],
                }
                result[date_]['free_hours'] = {
                    resource_id: day_data['free_hours'],
                }

        return result

    def merge_bookings_with_time_offs(self, bookings: dict) -> OrderedDict:
        """Merges serialized bookings with business and resources time_offs."""
        all_dicts = (
            bookings,
            self.business_time_off,
            self.resources_schedule,
        )
        keys_iterable = chain.from_iterable(all_dicts)

        result = OrderedDict.fromkeys(sorted(set(keys_iterable)))

        for key in result:
            result[key] = {
                **bookings.get(key, {'counter': 0, 'bookings': []}),
                'business': self.business_time_off.get(key, {}),
                'resources': self.resources_schedule.get(key, {}),
            }

        return result


class MonthlyBookingsHandler(RequestHandler):

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Return first X bookings per day
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: True
                - name: start_date
                  description: Start date
                  type: string
                  format: date
                  paramType: query
                  required: True
                - name: end_date
                  description: End date
                  type: string
                  format: date
                  paramType: query
                  required: True
                - name: include_unconfirmed
                  description: Include UNCONFIRMED and MODIFIED bookings
                  type: boolean
                  paramType: query
                  default: False
                - name: status
                  description: one or more statuses (&status=F&status=G)
                  type: string
                  required: False
                  paramType: query
                - name: resource_id
                  description: one or more resource_id
                               (&resource_id=123&resource_id=321)
                  type: integer
                  required: False
                  paramType: query
                - name: customer_id
                  description: customer id
                  type: integer
                  required: False
                  paramType: query
            type: MonthlyBookingsJson
        :swagger
        swaggerModels:
            MonthlyBookingsJson:
                id: MonthlyBookingsJson
                properties:
                    "[some_date]":
                        type: MonthlyBookingsDay
            MonthlyBookingsDay:
                id: MonthlyBookingsDay
                properties:
                    counter:
                        type: integer
                    bookings:
                        type: array
                        items:
                            type: MonthlyBookings
                    business:
                        type: MonthlyBusinessTimeOff
                        description: Monthly business time off dates
                    resources:
                        type: MonthlyResourcesSchedule
                        description: Resources schedule
            MonthlyBusinessTimeOff:
                id: MonthlyBusinessTimeOff
                properties:
                    time_off:
                        type: bool
                        description: Business has time off
            MonthlyResourcesSchedule:
                id: MonthlyResourcesSchedule
                properties:
                    "[resource_id]":
                        type: MonthlyResourceSchedule
                        description: Monthly resource schedule
            MonthlyResourceSchedule:
                id: MonthlyResourceSchedule
                properties:
                    working_hours:
                        type: array
                        description: Optional list of resource working hours
                        items:
                            type: HoursRange
                    free_hours:
                        type: array
                        description: Optional info about resource free hours
                        items:
                            type: HoursRange
                    time_offs:
                        type: array
                        description: Optional list of resource ids with
                        items:
                            type: integer
            HoursRange:
                id: HoursRange
                properties:
                    hour_from:
                        type: string
                        format: time
                    hour_till:
                        type: str
                        format: time
            MonthlyBookings:
                id: MonthlyBookings
                properties:
                    id:
                        type: string
                    booked_till:
                        type: string
                    booked_from:
                        type: string
                    service:
                        type: MonthlyBookingsService
                    type:
                        type: string
                    _editable:
                        type: string
                    status:
                        type: string
                    multibooking:
                        type: MonthlyBookingsMultibooking
                    _version:
                        type: string
            MonthlyBookingsService:
                id: MonthlyBookingsService
                properties:
                    id:
                        type: string
                    color:
                        type: string
                    name:
                        type: string
            MonthlyBookingsMultibooking:
                id: MonthlyBookingsMultibooking
                properties:
                    id:
                        type: string
                    _version:
                        type: string
        :swaggerModels
        """
        business = self.business_with_staffer(business=business_id)
        data = self._prepare_get_arguments()
        serializer = MonthlyBookingsRequestSerializer(
            data=data,
            context={'business': business},
        )
        self.validate_serializer(serializer)

        # fetch resources
        resource_ids = serializer.validated_data.get('resource_id')
        if not resource_ids and not serializer.initial_data.get('resource_id'):
            resources = get_business_calendar_resources(
                user=self.user,
                business=business,
                access_level=self.access_level,
            )
            resource_ids = [resource['id'] for resource in resources]

        status_list = serializer.validated_data.get('status')
        if not status_list:
            include_unconfirmed = serializer.validated_data['include_unconfirmed']
            status_list = [
                Appointment.STATUS.ACCEPTED,
                Appointment.STATUS.FINISHED,
                Appointment.STATUS.NOSHOW,
                Appointment.STATUS.PROPOSED,
            ] + (
                [
                    Appointment.STATUS.UNCONFIRMED,
                    Appointment.STATUS.MODIFIED,
                ]
                if include_unconfirmed
                else []
            )

        additional_filters = []
        customer_id = serializer.validated_data.get('customer_id')
        if customer_id:
            additional_filters.append(Q(appointment__booked_for__id=customer_id))

        all_bookings_booked_till_and_id = (
            SubBooking.objects.filter(
                appointment__deleted__isnull=True,
                appointment__business=business,
                appointment__status__in=status_list,
                deleted__isnull=True,
                resources__in=resource_ids,
                booked_from__lte=serializer.validated_data['end_datetime'],
                booked_till__gte=serializer.validated_data['start_datetime'],
                *additional_filters,
            )
            .order_by('booked_till')
            .values_list('booked_till', 'id')
        )

        ids_by_day = {}
        days_booking_limit = serializer.validated_data['bookings_per_day']
        tz = business.get_timezone()
        for booking in all_bookings_booked_till_and_id.iterator():
            date_ = booking[0].astimezone(tz).date()
            booking_id = booking[1]
            ids_by_day.setdefault(date_, set()).add(booking_id)

        ids, counters_by_day = self.get_booking_ids_with_limits_and_counters(
            ids_by_day=ids_by_day,
            limit=days_booking_limit,
        )

        response_serializer = MonthlyBookingsResponseSerializer(
            instance=ids_by_day.items(),
            many=True,
            context={
                'business': business,
                'staffer': self.user_staffer,
                'counters_by_day': counters_by_day,
                'ids_by_day': ids_by_day,
                'bookings': SubBooking.objects.filter(
                    id__in=ids,
                ).structured_values(
                    'appointment__booked_for__first_name',
                    'appointment__booked_for__last_name',
                    'appointment__booked_for__cell_phone',
                    'appointment__booked_for__email',
                    'appointment__booked_for__visible_in_business',
                    'appointment__booked_for__photo__image_url',
                    'appointment__booked_for__photo_id',
                    'appointment__booked_for__user__first_name',
                    'appointment__booked_for__user__last_name',
                    'appointment__booked_for__user__cell_phone',
                    'appointment__booked_for__user__email',
                    'appointment__booked_for__user_id',
                    'appointment__booked_for_id',
                    'booked_from',
                    'booked_till',
                    'appointment__business_note',
                    'appointment__customer_name',
                    'id',
                    'appointment__status',
                    'appointment__type',
                    'appointment__updated',
                    'appointment_id',
                    'service_name',
                    'service_variant__service__color',
                    'service_variant__service__name',
                    'service_variant__service__service_category_id',
                    'service_variant__service_id',
                    'service_variant_id',
                    'updated',
                    prefetches=[
                        {
                            'queryset': Resource.objects.all(),
                            'fields': [
                                'id',
                                'name',
                                'type',
                            ],
                            'to_field': 'resources',
                            'lookup_key': 'subbookings__in',
                            'backref_field': 'subbookings__id',
                            'prefetches': [],
                        },
                    ],
                ),
            },
        )

        time_off_updater = MonthlyBookingTimeOffUpdater(
            business=business,
            start_date=serializer.validated_data['start_date'],
            end_date=serializer.validated_data['end_date'],
            resource_ids=resource_ids,
        )
        result = time_off_updater.merge_bookings_with_time_offs(
            bookings=response_serializer.data,
        )

        self.finish_with_json(200, result)

    @staticmethod
    def get_booking_ids_with_limits_and_counters(ids_by_day, limit):
        selected_bookings_ids = []
        counts_by_day = {}
        for date_, ids in ids_by_day.items():
            selected_bookings_ids.extend(list(ids)[:limit])
            counts_by_day[date_] = len(ids)
        return selected_bookings_ids, counts_by_day

    def _prepare_get_arguments(self, list_values=None):
        result = super()._prepare_get_arguments(list_values)
        resource_id = result.get('resource_id')
        if isinstance(resource_id, str):
            result['resource_id'] = resource_id.split(',')
        status = result.get('status')
        if isinstance(status, str):
            result['status'] = status.split(',')
        return result
