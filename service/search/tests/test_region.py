import pytest
from rest_framework import status

from lib.baker_utils import get_or_create_booking_source
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import BookingSources
from webapps.consts import ANDROID, WEB
from webapps.structure.models import get_operating_country


@pytest.mark.django_db
class RegionInfoHandlerTest(BaseAsyncHTTPTest):
    url = '/location/region_info/'

    def setUp(self):
        super().setUp()
        self.country = get_operating_country()
        self.country.reindex(refresh_index=True)

    def get_customer_booking_source(self):
        return get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
            api_key='customer_key',
        )

    def test_get_region_by_id_mobile(self):
        self.customer_booking_src.name = ANDROID
        self.customer_booking_src.save()

        response = self._get_response()
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertNotIn('label', response.json[0])
        self.assertNotIn('formatted_address', response.json[0])
        self.assertNotIn('full_label', response.json[0])

    def test_get_region_by_id_web(self):
        response = self._get_response()
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json[0]['label'], 'USA')
        self.assertEqual(response.json[0]['formatted_address'], None)
        self.assertEqual(response.json[0]['full_label'], 'USA')

    def _get_response(self):
        headers = self.get_headers(self.url)
        headers['X-API-KEY'] = self.customer_booking_src.api_key
        return self.fetch(self.url, args={'id': self.country.id}, headers=headers)
