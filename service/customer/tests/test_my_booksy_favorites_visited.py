from datetime import timed<PERSON><PERSON>

import mock
import pytest
from model_bakery import baker

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import get_by_id
from lib.feature_flag.feature.customer import (
    VistedLikedSelected4UFlag,
    VistedLikedS4URandomizationFlag,
)
from lib.feature_flag.experiment.customer import VisitedLikedS4URandomizationExperiment
from lib.feature_flag.enums import ExperimentVariants
from lib.test_utils import create_subbooking
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    category_recipe,
    resource_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.images.models import Image
from webapps.reviews.models import Review
from webapps.search_engine_tuning.models import UserTuning
from webapps.user.enums import AuthOriginEnum


@pytest.fixture(scope='function')
def disable_annoying_task_or_signals_():
    """overwrite fixture which disabled assign_treatments_task"""


@pytest.mark.django_db
@mock.patch(
    'service.customer.my_booksy.is_in_experiment',
    lambda *a, **kw: False,
)
class MyBooksyHandlerTestVisitedCase(BaseAsyncHTTPTest):
    url = '/customer_api/my_booksy/'

    def setUp(self):
        super().setUp()
        self.category = category_recipe.make()
        self._prepare_business(self.business, primary_category=self.category)
        self.bci = bci_recipe.make(business=self.business)

    def _prepare_business(self, business, primary_category, promoted=False):
        business.active = True
        business.visible = True
        business.categories.add(self.category)
        business.primary_category = primary_category
        business.save()
        if promoted:
            type(business).objects.filter(id=business.id).update(
                boost_status=business.BoostStatus.ENABLED
            )
            business.refresh_from_db()
            assert (
                business.boosted == promoted
            ), "The logic in the save method can overwrite boost_status"

        service = service_recipe.make(
            business=business,
            active=True,
            is_available_for_customer_booking=True,
            treatment=treatment_recipe.make(parent=primary_category),
            service_category=service_category_recipe.make(business=business),
        )
        service_variant_recipe.make(
            service=service,
            active=True,
        )
        # add staffer to assure that business visible in elasticsearch
        service.add_staffers([staffer_recipe.make(business=business)])
        service.save()

        # add one image that business is searchable see
        # business.BusinessTermsWithSingleImagesSearchable
        # in service/customer/my_booksy.py:415
        baker.make(
            Image,
            business=business,
            is_cover_photo=True,
        )
        resource_recipe.make(business=business)
        business.reindex(refresh_index=True)

    def _create_booking(self, bci, delta=None, service_variant_id=None):
        now = tznow()
        if delta is None:
            delta = timedelta(days=3)
        booking, *_ = create_subbooking(
            business=bci.business,
            booking_kws=dict(
                booked_for=bci,
                status=Appointment.STATUS.FINISHED,
                source=self.customer_booking_src,
                booked_from=now - delta - timedelta(minutes=60),
                booked_till=now - delta,
                autoassign=True,
                updated_by=self.user,
            ),
        )
        if service_variant_id is not None:
            type(booking).objects.filter(id=booking.id).update(
                service_variant_id=service_variant_id
            )
        return booking

    def _get_response(self, user):
        # ensure that business is visible
        doc = get_by_id(self.business.id, ESDocType.BUSINESS)
        assert doc is not None
        # mock property _is_new_user to not create UserTuning
        # fetch response
        with mock.patch(
            'service.customer.my_booksy.MyBooksy._is_new_user',
            return_value=False,
        ):
            self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
            response = self.fetch(self.url)
        assert response.code == 200
        return response

    def _create_review(self, booking, rank):
        baker.make(
            Review,
            user=self.bci.user,
            business=self.business,
            subbooking=booking,
            rank=rank,
        )
        self.business.reviews_rank_avg = 4
        self.business.save()

    def _add_negative_review(self):
        booking = self._create_booking(self.bci)
        self._create_review(booking, 1)
        response = self._get_response(self.bci.user)
        visited = response.json.get('favorites_visited')
        assert visited is not None
        # asert business with  bad review is not response
        assert len(visited) == 0

    def test_add_negative_review(self):
        self._add_negative_review()

    def test_add_negative_then_positive(self):
        self._add_negative_review()
        # add positive review
        user = self.bci.user
        booking = self._create_booking(self.bci, timedelta(days=1))
        self._create_review(booking, 5)
        # get response
        response = self._get_response(user)
        visited = response.json.get('favorites_visited')
        assert len(visited) == 1
        assert visited[0]['business']['id'] == self.business.id

    def test_selected_for_you_with_appointment(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user)
        self._create_booking(self.bci, timedelta(days=2))
        business_2 = bci_2.business
        self._prepare_business(business_2, self.category)
        service_variant_id = business_2.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(bci_2, timedelta(days=1), service_variant_id=service_variant_id)
        self._set_user_data_in_elastic(user)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u, self.category)

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 3
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {visited[0]['business']['id'], visited[2]['business']['id']} == {
            self.business.id,
            business_2.id,
        }
        assert visited[1]['business']['id'] == business_selected4u.id
        assert visited[1]['business']['is_selected_for_you'] is True

    def test_selected_for_you_only_liked(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user, bookmarked=True)
        business_2 = bci_2.business
        self._prepare_business(business_2, self.category)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u, self.category)
        self._set_user_data_in_elastic(user)
        lat, lon = business_selected4u.latitude, business_selected4u.longitude
        self.url += f'?geo_location={lat},{lon}'

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 2
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert visited[0]['business']['id'] == business_2.id
        assert visited[1]['business']['id'] == business_selected4u.id
        assert visited[1]['business']['is_selected_for_you'] is True

    def test_selected_for_you_only_liked_with_boost_business(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user, bookmarked=True)
        business_2 = bci_2.business
        self._prepare_business(
            business_2,
            primary_category=self.category,
            promoted=True,
        )
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u, primary_category=self.category, promoted=True)
        self._set_user_data_in_elastic(user)
        lat, lon = business_selected4u.latitude, business_selected4u.longitude
        self.url += f'?geo_location={lat},{lon}'

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 1  # boost business is not allowed
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert visited[0]['business']['id'] == business_2.id

    @staticmethod
    def _set_user_data_in_elastic(user):
        UserTuning.update_multiple_tunings([user.id])
        user.reindex(refresh_index=True)

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.VARIANT_A,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_v2_variant_a_priority_appointments(self):
        user = self.bci.user
        service_variant_id_1 = self.business.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(self.bci, timedelta(days=2), service_variant_id=service_variant_id_1)

        bci_2 = bci_recipe.make(user=user)
        business_2 = bci_2.business
        category_2 = category_recipe.make()
        self._prepare_business(business_2, primary_category=category_2)
        service_variant_id_2 = business_2.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(bci_2, timedelta(days=1), service_variant_id=service_variant_id_2)

        bci_3 = bci_recipe.make(user=user, bookmarked=True)
        business_3 = bci_3.business
        category_3_liked = category_recipe.make()
        self._prepare_business(business_3, category_3_liked)

        self._set_user_data_in_elastic(user)
        business_appointment_category_1 = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_appointment_category_1, primary_category=self.category)

        business_appointment_category_2 = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_appointment_category_2, primary_category=category_2)

        business_in_liked_category = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_in_liked_category, primary_category=category_3_liked)

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 4
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {
            visited[0]['business']['id'],
            visited[2]['business']['id'],
            visited[3]['business']['id'],
        } == {
            self.business.id,
            business_2.id,
            business_3.id,
        }
        assert visited[1]['business']['id'] in [
            business_appointment_category_1.id,
            business_appointment_category_2.id,
        ]
        assert visited[1]['business']['primary_category'] in [self.category.id, category_2.id]
        assert visited[1]['business']['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.VARIANT_A,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_v2_variant_a_no_appointments(self):
        user = self.bci.user
        bci_liked = bci_recipe.make(user=user, bookmarked=True)
        business_liked = bci_liked.business
        category_liked = category_recipe.make()
        self._prepare_business(business_liked, category_liked)

        bci_liked_2 = bci_recipe.make(user=user, bookmarked=True)
        business_liked_2 = bci_liked_2.business
        category_liked_2 = category_recipe.make()
        self._prepare_business(business_liked_2, category_liked_2)

        self._set_user_data_in_elastic(user)
        business_liked_category_1 = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_liked_category_1, primary_category=category_liked)

        lat, lon = business_liked_category_1.latitude, business_liked_category_1.longitude

        business_liked_category_2 = business_recipe.make(
            active_from=tznow(), latitude=lat, longitude=lon
        )
        self._prepare_business(business_liked_category_2, primary_category=category_liked_2)

        business_in_another_category = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_in_another_category, primary_category=self.category)

        self.url += f'?geo_location={lat},{lon}'
        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 3
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {visited[0]['business']['id'], visited[2]['business']['id']} == {
            business_liked.id,
            business_liked_2.id,
        }
        assert visited[1]['business']['id'] in [
            business_liked_category_1.id,
            business_liked_category_2.id,
        ]
        assert visited[1]['business']['primary_category'] in [
            category_liked.id,
            category_liked_2.id,
        ]
        assert visited[1]['business']['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.VARIANT_B,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_v2_variant_b_priority_bookmarked(self):
        user = self.bci.user
        business_1 = self.bci.business
        service_variant_id_1 = business_1.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(self.bci, timedelta(days=2), service_variant_id=service_variant_id_1)

        bci_2 = bci_recipe.make(user=user, bookmarked=True)
        business_2 = bci_2.business
        category_2_liked = category_recipe.make()
        self._prepare_business(business_2, category_2_liked)

        bci_3 = bci_recipe.make(user=user, bookmarked=True)
        business_3 = bci_3.business
        category_3_liked = category_recipe.make()
        self._prepare_business(business_3, category_3_liked)

        self._set_user_data_in_elastic(user)
        business_appointment_category = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_appointment_category, primary_category=self.category)

        business_in_liked_category_ = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_in_liked_category_, primary_category=category_2_liked)

        lat, lon = business_in_liked_category_.latitude, business_in_liked_category_.longitude

        business_in_liked_category_2 = business_recipe.make(
            active_from=tznow(), latitude=lat, longitude=lon
        )
        self._prepare_business(business_in_liked_category_2, primary_category=category_3_liked)

        self.url += f'?geo_location={lat},{lon}'
        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 4
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {
            visited[0]['business']['id'],
            visited[2]['business']['id'],
            visited[3]['business']['id'],
        } == {
            self.business.id,
            business_2.id,
            business_3.id,
        }
        assert visited[1]['business']['id'] in [
            business_in_liked_category_.id,
            business_in_liked_category_2.id,
        ]
        assert visited[1]['business']['primary_category'] in [
            category_2_liked.id,
            category_3_liked.id,
        ]
        assert visited[1]['business']['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.VARIANT_B,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_v2_variant_b_no_liked(self):
        user = self.bci.user
        service_variant_id_1 = self.business.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(self.bci, timedelta(days=2), service_variant_id=service_variant_id_1)

        bci_2 = bci_recipe.make(user=user)
        business_2 = bci_2.business
        category_2 = category_recipe.make()
        self._prepare_business(business_2, primary_category=category_2)
        service_variant_id_2 = business_2.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(bci_2, timedelta(days=1), service_variant_id=service_variant_id_2)

        self._set_user_data_in_elastic(user)
        business_appointment_category_1 = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_appointment_category_1, primary_category=self.category)

        business_appointment_category_2 = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_appointment_category_2, primary_category=category_2)

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 3
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {visited[0]['business']['id'], visited[2]['business']['id']} == {
            self.business.id,
            business_2.id,
        }
        assert visited[1]['business']['id'] in [
            business_appointment_category_1.id,
            business_appointment_category_2.id,
        ]
        assert visited[1]['business']['primary_category'] in [self.category.id, category_2.id]
        assert visited[1]['business']['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.CONTROL,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_control_group_v1_behaviour(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user)
        self._create_booking(self.bci, timedelta(days=2))
        business_2 = bci_2.business
        self._prepare_business(business_2, self.category)
        service_variant_id = business_2.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(bci_2, timedelta(days=1), service_variant_id=service_variant_id)
        self._set_user_data_in_elastic(user)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u, self.category)

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 3
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {visited[0]['business']['id'], visited[2]['business']['id']} == {
            self.business.id,
            business_2.id,
        }
        assert visited[1]['business']['id'] == business_selected4u.id
        assert visited[1]['business']['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            VisitedLikedS4URandomizationExperiment.flag_name: ExperimentVariants.VARIANT_B,
            VistedLikedSelected4UFlag.flag_name: {},
            VistedLikedS4URandomizationFlag.flag_name: {'iPhone': '1.0.0'},
        }
    )
    def test_selected_for_you_v2_variant_b_liked_business_no_primary_category(self):
        user = self.bci.user

        business_no_primary = business_recipe.make(
            primary_category=None, categories=[self.category]
        )
        bci_recipe.make(user=user, business=business_no_primary, bookmarked=True)
        self._prepare_business(business=business_no_primary, primary_category=None)

        self._set_user_data_in_elastic(user)

        business_new = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_new, primary_category=self.category)

        lat, lon = business_new.latitude, business_new.longitude

        self.url += f'?geo_location={lat},{lon}'
        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 1
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert visited[0]['business']['id'] == business_no_primary.id
        assert visited[0]['business']['primary_category'] is None
