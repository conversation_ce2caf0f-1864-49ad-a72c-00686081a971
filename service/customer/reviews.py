from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Iterable, List

from bo_obs.datadog.enums import BooksyTeams
from django.db.models import Avg, Count, Prefetch, Q, Subquery
from django.utils.translation import gettext as _
from rest_framework import status

from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.swagger.serializer_fields import get_fields_from_docs
from lib.tools import sasrt, tznow
from service.customer.serializers import ReviewRequestSerializer
from service.exceptions import ServiceError
from service.images.helpers import BasePhotoUploadHandler
from service.reviews import ReviewsHandler
from service.tools import RequestHandler, json_request, session
from webapps.booking.models import Appointment
from webapps.business_customer_info.bci_merge import ClaimMixin
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.serializers import BusinessInReviewSerializer
from webapps.business_related.enums import ClaimLogReasons
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.notification.scenarios import start_scenario
from webapps.profile_completeness.events import step_collect_reviews
from webapps.reviews.forms import CustomerReviewFeedbackForm
from webapps.reviews.models import Review, ReviewPhoto
from webapps.reviews.serializers import (
    CustomerReviewSerializer,
    ModerateCustomerReviewSerializerForBusiness,
    SimpleCustomerReviewSerializer,
    fetch_feedbacks,
    format_review_with_feedback,
)
from webapps.reviews.tasks import post_customer_review_task
from webapps.reviews.tools import rank_to_stars
from webapps.segment.tasks import analytics_review_completed_task
from webapps.user.models import User


class CustomerReviewBaseHandler(ReviewsHandler):  # pylint: disable=too-many-ancestors

    def _format_reviews(
        self, formatted_reviews: List[Dict], reviews_count: int, reviews_rank: float
    ) -> Dict:
        """Accepted serialized reviews list and add feedbacks to it"""
        feedbacks = fetch_feedbacks(
            [review['id'] for review in formatted_reviews],
            self.user,
            self.fingerprint,
        )
        format_review_with_feedback(
            formatted_reviews,
            feedbacks,
        )
        return {
            'reviews': formatted_reviews,
            'reviews_count': reviews_count,
            'reviews_rank': reviews_rank,
            'reviews_stars': rank_to_stars(reviews_rank),
            'reviews_page': self.reviews_page,
            'reviews_per_page': self.reviews_per_page,
        }

    @staticmethod
    def _get_reviews_awaiting(user_id):
        """Static method to be able to test only this method"""
        # there is '29 days 23 hours 50 minutes' not '30 days' because
        # we want user to have some time (10 minutes) to enter a review
        bci_in_visible_businesses = (
            BusinessCustomerInfo.objects.filter(
                user_id=user_id,
                business__active=True,
                business__visible=True,
            )
            .distinct('business_id')
            .only('id')
        )
        latest_user_appointment = list(
            Appointment.objects.filter(
                booked_for_id__in=Subquery(bci_in_visible_businesses),
                booked_till__gte=(tznow() - timedelta(days=29, hours=23, minutes=50)),
                status=Appointment.STATUS.FINISHED,
            )
            .order_by(
                'business_id',
                '-booked_till',
            )
            .distinct(
                'business_id',
            )
            .values_list('id', flat=True)
        )

        businesses = (
            Business.objects.filter(
                appointments__in=latest_user_appointment,
                appointments__bookings__review=None,
            )
            .select_related(
                'seo_region',
                'region',
                'primary_category',
            )
            .prefetch_related(
                Prefetch(
                    'images',
                    queryset=Image.objects.filter(
                        category=ImageTypeEnum.LOGO,
                    ).only('image_url', 'image'),
                    to_attr='prefetched_logo_qs',
                ),
            )
            .only(
                'id',
                'name',
                'reviews_count',
                'reviews_rank_avg',  # reviews_stars
                'address',
                'zipcode',
                'city',
                # prefetched
                'seo_region',
                'region',
                'primary_category',
            )
        )
        return businesses

    def get_reviews_awaiting(self):
        businesses = self._get_reviews_awaiting(self.user.id)
        businesses = list(businesses[self.offset : self.limit])
        return (BusinessInReviewSerializer(businesses, many=True).data, len(businesses))

    def format_reviews(self, reviews: Iterable, reviews_count: int, reviews_rank: float) -> Dict:
        """Serialize reviews with CustomerReviewSerializer reviews
        returns ReviewResponseTypedDict
        """
        formatted_reviews = CustomerReviewSerializer(reviews, many=True).data
        return self._format_reviews(
            formatted_reviews,
            reviews_count,
            reviews_rank,
        )


class CustomerReviewsHandler(CustomerReviewBaseHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self):
        """
        swagger:
            summary: Get logged customer reviews.
            type: ReviewResponseListWithPagination
            parameters:
                - name: reviews_page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: reviews_per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.REVIEWS_PER_PAGE
                - name: with_awaiting
                  description: at the beginning list businesses awaiting review
                  paramType: query
                  required: false
                  type: integer
                  defaultValue: 0
        """

        self.parse_page_values_from_get()
        offset = self.offset
        limit = self.limit

        awaiting = []
        awaiting_count = 0
        args = self._prepare_get_arguments()
        if args.get('with_awaiting'):
            awaiting, awaiting_count = self.get_reviews_awaiting()
            awaiting = [{'business': b} for b in awaiting]
            offset -= awaiting_count
            offset = max(offset, 0)
            limit -= awaiting_count

        exclude = Q(
            business__active=False,
            business__b_listing__active=False,
        ) | Q(
            business__status=Business.Status.BLOCKED,
            business__b_listing__isnull=True,
        )
        if limit > offset:
            reviews = list(
                self.user.reviews.select_related(
                    # 'user',  -- self.user.reviews handles it
                    'business',
                )
                .prefetch_related(
                    Prefetch(
                        'review_photos',
                        queryset=ReviewPhoto.objects.all().select_related('photo'),
                    ),
                )
                .exclude(exclude)[offset:limit]
            )
        else:
            reviews = []

        # 50023 - need to change reviews business into BListing
        for review in reviews:
            if review.business.b_listing and review.business.b_listing.active:
                review.business = review.business.b_listing

        aggregate = self.user.reviews.exclude(exclude).aggregate(Avg('rank'), Count('rank'))

        ret = self.format_reviews(
            reviews=reviews,
            reviews_count=aggregate['rank__count'],
            reviews_rank=aggregate['rank__avg'],
        )
        ret['reviews_with_awaiting_count'] = aggregate['rank__count'] + awaiting_count
        ret['reviews'] = awaiting + ret['reviews']
        self.finish(ret)


class CustomersReviewsHandler(CustomerReviewBaseHandler):
    # pylint: disable=too-many-ancestors
    @session()
    def get(self, customer_id):
        """
        swagger:
            summary: Get selected customer reviews.
            type: ReviewResponseListWithPagination
            parameters:
                - name: reviews_page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: reviews_per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.REVIEWS_PER_PAGE
                - name: customer_id
                  description:
                    Customer id if empty then logged user id will be used
                  type: integer
                  paramType: path
                  required: True
        """
        self.parse_page_values_from_get()
        self.user = None
        customer = User.objects.get(id=customer_id)
        reviews = list(
            customer.reviews.select_related(
                'business',
            )
            .prefetch_related(
                Prefetch(
                    'review_photos',
                    queryset=ReviewPhoto.objects.all().select_related('photo'),
                ),
            )
            .exclude(
                business__active=False,
            )[self.offset : self.limit]
        )
        aggregate = customer.reviews.aggregate(Avg('rank'), Count('rank'))

        ret = self.format_reviews(
            reviews=reviews,
            reviews_count=aggregate['rank__count'],
            reviews_rank=aggregate['rank__avg'],
        )

        self.finish(ret)


class CustomerReviewsAwaitingHandler(CustomerReviewBaseHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self):
        """
        swagger:
            summary: Get logged customers' businesses awaiting for reviews
            type: AwaitingReviewsListing
            parameters:
                - name: page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.PER_PAGE
        :swagger
        swaggerModels:
            AwaitingReviewsListing:
                id: AwaitingReviewsListing
                required:
                    - page
                    - per_page
                    - count
                    - businesses
                properties:
                    page:
                        type: integer
                    per_page:
                        type: integer
                    count:
                        type: integer
                    businesses:
                        type: array
                        items:
                            type: BizIdNameAndThumb
        """

        page, per_page = self.parse_page_values_from_get()

        businesses, count = self.get_reviews_awaiting()

        result = {
            'page': page,
            'per_page': per_page,
            'count': count,
            'businesses': businesses,
        }

        self.finish_with_json(200, result)


class CustomerSpecificReviewHandler(CustomerReviewBaseHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def put(self, business_id, review_id):
        """
        swagger:
            summary: Update customer review for business.
            type: ReviewResponse
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: review_id
                  description: id of customer's review
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: JSON with ReviewRequest
                  type: ReviewRequest
                  paramType: body
        """

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )

        is_b_listing = business.is_b_listing()
        is_b_listing_with_source = False
        source = business.get_source()
        if source:
            is_b_listing_with_source = True
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            user=self.user,
            business=business,
        )
        previous_rank = review.rank
        serializer = SimpleCustomerReviewSerializer(
            data=self.data,
            fields=get_fields_from_docs('ReviewRequest'),
            instance=review,
            partial=True,
        )
        self.validate_serializer(serializer)
        review = serializer.save()

        if is_b_listing_with_source:
            review.business = business.b_listing
        analytics_review_completed_task.delay(
            review_id=review.id,
            context={
                'session_user_id': review.user_id,
            },
        )
        resp = CustomerReviewSerializer(review).data
        # <editor-fold desc="early_finish">
        if is_b_listing:
            self.finish(resp)
            return

        start_scenario('review_added', review_id=review.id, business_id=business_id)
        review.refresh_from_db()
        # downgrade
        previous_bookmarked = previous_rank >= Review.THRESHOLD_BOOKMARKED_BUSINESS
        bci = BusinessCustomerInfo.objects.filter(
            business_id=business_id,
            user=self.user,
        ).first()
        if previous_bookmarked and review.rank < Review.THRESHOLD_BOOKMARKED_BUSINESS:
            # downgrade
            if bci:
                bci.bookmarked = False
                bci.save(update_fields=['bookmarked'])
        elif not previous_bookmarked and review.rank >= Review.THRESHOLD_BOOKMARKED_BUSINESS:
            # upgrade
            if bci:
                bci.bookmarked = True
                bci.save(update_fields=['bookmarked'])
        # </editor-fold>
        self.finish(resp)

    @session(login_required=False)
    def get(self, business_id, review_id):
        """
        swagger:
            summary: Get customer review for business having given review_id.
            type: ReviewResponse
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: review_id
                  description: id of customer's review
                  type: integer
                  paramType: path
                  required: true
        """

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )
        is_b_listing_with_source = False
        source = business.get_source()
        if source:
            is_b_listing_with_source = True
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            business=business,
        )
        # 50023 - need to change review business into BListing
        if is_b_listing_with_source:
            review.business = business.b_listing

        self.finish(CustomerReviewSerializer(review).data)

    @session(login_required=True)
    def delete(self, business_id, review_id):
        """
        swagger:
            summary: Delete specified review
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: review_id
                  description: id of customer's review
                  type: integer
                  paramType: path
                  required: true
        """

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )
        source = business.get_source()
        if source:
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            user=self.user,
            business=business,
        )

        review.review_photos.all().delete()
        review.delete()
        self.finish_with_json(200, {})


class CustomerSpecificReviewFeedbackHandler(RequestHandler):
    """
    swagger:
        summary: Feedback for customer's review
        notes: |
            Header <tt>X-ACCESS-TOKEN</tt> is optional.
            If given then Feedback is tied to account with this token.
            If omitted then Feedback is tied to device with hash in
            <tt>X-FINGERPRINT</tt>.
        parameters:
            - name: business_id
              description: id of business
              type: integer
              paramType: path
              required: true
            - name: review_id
              description: id of customer's review
              type: integer
              paramType: path
              required: true
        type: ReviewFeedbackInformation
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    def get_review_feedbacks(self, business_id, review_id):
        sasrt(
            self.user is not None or self.fingerprint,
            403,
            [
                {
                    'code': 'unauthorized',
                    'field': 'access_token',
                    'description': _('Unauthorized access attempt.'),
                }
            ],
        )

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )

        source = business.get_source()
        if source:
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            business=business,
        )

        if self.user is not None:
            review_feedbacks = review.review_feedbacks.filter(user=self.user)
        else:
            # this user=None is big deal
            # thanks for it we allow logged user add new Feedback,
            # even if there is already Feedback from this same fingerprint
            # is better to allow double voting than punish users with
            # accidentally the same fingerprints
            review_feedbacks = review.review_feedbacks.filter(
                user=None,
                fingerprint=self.fingerprint,
            )

        return review_feedbacks

    def save_review_feedback(self, review_id, review_feedback):
        form = CustomerReviewFeedbackForm(self.data)
        self.raise_error_if_form_not_valid(form)

        if review_feedback is None:
            review_feedback = form.save(commit=False)
        else:
            review_feedback.feedback = form.save(commit=False).feedback

        review_feedback.user = self.user
        review_feedback.review_id = review_id
        review_feedback.fingerprint = self.fingerprint
        review_feedback.save()

        return review_feedback

    @session(optional_login=True)
    @json_request
    def post(self, business_id, review_id):
        """
        swagger:
            summary: Add current's user feedback for review
            parameters:
              - name: body
                paramType: body
                type: ReviewFeedbackWrapper
                description: Feedback for Review
                required: true
        """
        business_id = int(business_id)
        review_id = int(review_id)

        review_feedbacks = list(self.get_review_feedbacks(business_id, review_id))

        self.quick_assert(
            len(review_feedbacks) == 0,
            ('already_exists', 'validation', 'feedback'),
            _("Feedback already present for this review"),
        )

        self.save_review_feedback(review_id, None)
        feedback = fetch_feedbacks([review_id])[review_id]
        self.finish(feedback)

    @session(optional_login=True)
    @json_request
    def put(self, business_id, review_id):
        """
        swagger:
            summary: Set current's user feedback for review
            parameters:
              - name: body
                paramType: body
                type: ReviewFeedbackWrapper
                description: Feedback for Review
                required: true
        """

        business_id = int(business_id)
        review_id = int(review_id)

        review_feedbacks = list(self.get_review_feedbacks(business_id, review_id))
        self.save_review_feedback(review_id, review_feedbacks[0] if review_feedbacks else None)
        feedback = fetch_feedbacks([review_id])[review_id]
        self.finish(feedback)

    @session(optional_login=True)
    def delete(self, business_id, review_id):
        """
        swagger:
            summary: Delete current's user feedback for review
        """

        business_id = int(business_id)
        review_id = int(review_id)

        self.get_review_feedbacks(business_id, review_id).delete()
        feedback = fetch_feedbacks([review_id])[review_id]
        self.finish(feedback)


# pylint: disable=too-many-ancestors
class CustomerSpecificReviewPhotosHandler(BasePhotoUploadHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    def post(self, business_id, review_id):
        """
        swagger:
            summary: Upload review photo (multipart/form-data)
            consumes: multipart/form-data
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: review_id
                  description: id of customer's review
                  type: integer
                  paramType: path
                  required: true
                - name: photo
                  description: Photo content
                  type: File
                  paramType: form
                  required: true
        """
        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )
        source = business.get_source()
        if source:
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            user=self.user,
            business=business,
        )
        context = {
            'business_id': business_id,
            'image_type': ImageTypeEnum.REVIEW,
        }
        serializer = self.save_photo(context)
        review_photo = ReviewPhoto(review=review, photo=serializer.instance)
        review_photo.save()
        review_photo.migrate_photo_to_s3(business_id)

        self.finish(
            {
                'id': review_photo.id,
                'url': review_photo.photo.image_url,
            }
        )


class CustomerSpecificReviewPhotoHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    def delete(self, business_id, review_id, photo_id):
        """
        swagger:
            summary: Delete review photo
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: review_id
                  description: id of customer's review
                  type: integer
                  paramType: path
                  required: true
                - name: photo_id
                  description: Id of review photo to remove
                  type: integer
                  paramType: path
                  required: true
        """

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )

        source = business.get_source()
        if source:
            business = source

        review = self.get_object_or_404(
            ('reviews', 'Review'),
            id=review_id,
            user=self.user,
            business=business,
        )
        review_photo = self.get_object_or_404(
            ('reviews', 'ReviewPhoto'),
            id=photo_id,
            review=review,
        )
        review_photo.delete()
        self.finish_with_json(200, {})


class CustomerReviewsPerBusinessHandler(ClaimMixin, CustomerReviewBaseHandler):
    """Moved from service/business/reviews.py"""

    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    ERROR_DESCRIPTION_BY_STATUS = {
        BusinessCustomerInfo.REVIEW_ADD_STATUS_MISSING: _(
            "Make an appointment and post your review once you're finished."
        ),
        BusinessCustomerInfo.REVIEW_ADD_STATUS_SCHEDULED: _(
            "Sorry, you can only post a review once the apointment is finished."
        ),
        BusinessCustomerInfo.REVIEW_ADD_STATUS_EXPIRED: _("You only had 30 days to post a review."),
        BusinessCustomerInfo.REVIEW_ADD_BLOCKED_NO_SHOW: _(
            "You can't add review to no-show appointment"
        ),
        BusinessCustomerInfo.REVIEW_ADD_ALREADY_EXIST: _("You can't add second review"),
    }

    @session(optional_login=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id):
        """
        swagger:
            summary: Get customers' reviews for business.
            notes: |
                This endpoint is specific.
                If is given (optional) header <tt>X-ACCESS-TOKEN</tt>
                then additional field <tt>feedback</tt> per Review is returned.
            type: ReviewRatingsResponseListWithPagination
            parameters:
                - name: business_id
                  description: ID of business
                  type: integer
                  paramType: path
                  required: True
                - name: service_id
                  description: Service id to filter reviews by
                  paramType: query
                  required: False
                  type: integer
                - name: reviews_page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: reviews_per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.REVIEWS_PER_PAGE
        """

        args = self._prepare_get_arguments()
        service_id = args.get('service_id')
        if service_id is not None:
            try:
                service_id = int(service_id)
            except (ValueError, TypeError) as error:
                raise ServiceError(
                    status.HTTP_400_BAD_REQUEST,
                    errors=[
                        {
                            'code': 'invalid',
                            'description': 'Invalid service ID',
                        },
                    ],
                ) from error

        reviews_inner = self._get_sorted_business_reviews(
            business_id,
            service_id=service_id,
        )
        business = reviews_inner.business
        reviews = ModerateCustomerReviewSerializerForBusiness(
            reviews_inner.reviews,
            many=True,
            context={
                'business': business,
            },
        ).data
        result = self._format_reviews(
            reviews,
            reviews_inner.reviews_count,
            business.reviews_rank_avg,
        )
        result['num_reviews_per_rank'] = business.get_num_reviews_per_rank()
        self.finish(result)

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create customer review for business.
            type: ReviewResponse
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: JSON with ReviewRequest
                  type: ReviewRequest
                  paramType: body
        """

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )
        request_serializer = ReviewRequestSerializer(
            data=self.data,
            context={
                'business': business,
            },
        )
        validated_data = self.validate_serializer(request_serializer)
        is_b_listing = business.is_b_listing()
        is_b_listing_with_source = False
        customer_info = None
        appointment_secret = validated_data.get('secret')

        if is_b_listing:
            source = business.get_source()
            if source:
                is_b_listing_with_source = True
                business = source
        else:
            if appointment_secret:
                customer_info = business.business_customer_infos.filter(
                    appointments__secret=appointment_secret,
                    business=business,
                ).first()
            else:
                customer_info = business.business_customer_infos.filter(
                    user=self.user,
                ).first()

        review_status = self.get_review_status(business, is_b_listing, customer_info)
        self.handle_errors(business, review_status)

        review_data = self.fill_data(
            business,
            dict(validated_data),
            customer_info,
            is_b_listing,
        )
        serializer = SimpleCustomerReviewSerializer(data=review_data)
        self.validate_serializer(serializer)
        if appointment_secret:
            appointment = Appointment.objects.filter(
                secret=appointment_secret,
                business=business,
            ).last()
            self.claim_appointment_or_merge_bci(
                logged_in_user=self.user,
                appointment=appointment,
                merge_reason=ClaimLogReasons.REVIEW,
            )
        review = serializer.save()

        # profile completeness
        step_collect_reviews.send(business)
        if is_b_listing_with_source:
            review.business = business.b_listing

        if not is_b_listing:
            post_customer_review_task.delay(
                user_id=self.user.id,
                business_id=business_id,
                review_id=review.id,
            )
        self.finish_with_json(200, CustomerReviewSerializer(review).data)

    def get_review_status(self, business, is_b_listing, customer_info):
        review_status = ''
        review_added = self.user.reviews.filter(business=business).exists()
        if is_b_listing and review_added:
            review_status = BusinessCustomerInfo.REVIEW_ADD_ALREADY_EXIST
        if not is_b_listing:
            review_status = BusinessCustomerInfo.REVIEW_ADD_STATUS_MISSING
            if customer_info:
                review_status = customer_info.customer_review_status

        return review_status

    def handle_errors(self, business, review_status):
        description = self.ERROR_DESCRIPTION_BY_STATUS.get(review_status, '')
        self.quick_assert(
            review_status not in self.ERROR_DESCRIPTION_BY_STATUS,
            ('invalid', 'validation', 'user'),
            description % {'business_name': business.name},
        )

    def fill_data(self, business, review_data, customer_info, is_b_listing):
        if is_b_listing:
            review_data['verified'] = False
        else:
            subbooking = customer_info.latest_booking
            if subbooking:
                review_data['staff'], review_data['services'] = Review.get_staff_and_services(
                    subbooking
                )
                review_data['subbooking'] = subbooking.id
        review_data['business'] = business.id
        review_data['user'] = self.user.id
        return review_data
