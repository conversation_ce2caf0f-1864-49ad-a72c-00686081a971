from rest_framework import status

from service.tools import Re<PERSON><PERSON><PERSON><PERSON>, json_request, session
from webapps.business_related.models import Amenities
from webapps.business_related.serializers import AmenitiesSerializer


class BusinessAmenitiesHandler(RequestHandler):
    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """swagger:
        summary: Get Amenities for business.
        type: BusinessAmenitiesResponse
        parameters:
          - name: business_id
            type: integer
            paramType: path
        :swagger

        swaggerModels:
          Amenity:
            id: Amenity
            required:
              - key
              - value
              - label
            properties:
              key:
                type: string
                description: Amenity identifier
                enum_from_const: webapps.business_related.enums.AmenityEnum
              value:
                type: boolean
                description: Amenity value
              label:
                type: string
                description: Translated amenity label

          BusinessAmenitiesUpdateBody:
            id: BusinessAmenitiesUpdateBody
            required:
              - key
              - value
            properties:
              key:
                type: string
                description: Amenity identifier
                enum_from_const: webapps.business_related.enums.AmenityEnum
              value:
               type: boolean
               description: Amenity value

          BusinessAmenitiesResponse:
            id: BusinessAmenitiesResponse
            properties:
              amenities:
                type: array
                items:
                  type: Amenity

          BusinessAmenitiesRequest:
            id: BusinessAmenitiesResponse
            properties:
              amenities:
                type: array
                items:
                  type: BusinessAmenitiesUpdateBody

        :swaggerModels
        """
        self.business_with_advanced_staffer(business_id)
        instance, _ = Amenities.objects.get_or_create(business_id=business_id)

        self.finish_with_json(
            status.HTTP_200_OK, {'amenities': AmenitiesSerializer(instance=instance).data_as_list}
        )

    @session(login_required=True, api_key_required=True)
    @json_request
    def put(self, business_id):
        """swagger:
        summary: Update Amenities for business
        type: BusinessAmenitiesResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
            - name: body
              type: BusinessAmenitiesRequest
              paramType: body
              required: true
        :swagger
        """
        self.business_with_reception(business_id)
        instance = self.get_object_or_404(Amenities, business_id=business_id)

        serializer = AmenitiesSerializer.from_request(
            instance=instance,
            data=self.data,
        )
        self.validate_serializer(serializer, flatten=True)
        serializer.save()

        self.finish_with_json(status.HTTP_200_OK, {'amenities': serializer.data_as_list})
