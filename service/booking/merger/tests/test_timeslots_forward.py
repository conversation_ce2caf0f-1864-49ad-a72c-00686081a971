# pylint: disable=too-many-arguments,unused-argument

from datetime import date, datetime, timedelta
from unittest.mock import Mock

import pytest
from dateutil.tz import gettz
from django.utils.timezone import override
from rest_framework.status import HTTP_200_OK

from lib.test_utils import create_subbooking, timezone_for_tests
from merger_grpc.proto.appointment_pb2 import TimeSlotsQueryRequest, TimeSlotsQueryResponse
from service.customer.time_slots_forward import TimeSlotsForward
from service.customer.time_slots_utils import legacy_slots_from_booksy_slots
from webapps.booking.models import Appointment


def stamp(dtime, delta):
    return int((dtime + timedelta(minutes=delta)).timestamp())


def humanize_stamp(_stamp: int):
    return _stamp // 60, _stamp % 60


def test_convert_partner_slots():
    # TODO: currently it doesnt work with ZoneInfo
    tz = gettz(timezone_for_tests())

    expected_29th_hours = [
        (10, 0),
        (10, 20),
        (10, 40),
        (11, 0),
        (11, 20),
        (11, 40),
        (12, 0),
        (12, 15),
        (12, 20),
        (12, 30),
        (12, 40),
        (12, 45),
        (13, 0),
        (13, 15),
        (13, 30),
        (13, 45),
        (14, 0),
    ]
    expected_30th_hours = [
        (11, 0),
        (12, 0),
        (13, 0),
        (14, 0),
        (14, 15),
        (14, 30),
        (14, 45),
        (15, 0),
        (15, 15),
        (15, 30),
        (15, 45),
        (16, 0),
    ]

    partner_slots = TimeSlotsQueryResponse(
        business_id=10,
        staffers_availability=[
            dict(
                staffer_id=110,
                slots=[
                    stamp(datetime(2021, 3, 29, 12, tzinfo=tz), i * 15)
                    for i in range(2 * 4 + 1)  # 12:00 till 14:00 every 15min
                ]
                + [
                    stamp(datetime(2021, 3, 30, 11, tzinfo=tz), i * 60)
                    for i in range(4 * 1 + 1)  # 11:00 till 15:00 every 60min
                ],
            ),
            dict(
                staffer_id=111,
                slots=[
                    stamp(datetime(2021, 3, 29, 10, tzinfo=tz), i * 20)
                    for i in range(3 * 3 + 1)  # 10:00 till 13:00 every 20min
                ]
                + [
                    stamp(datetime(2021, 3, 30, 14, tzinfo=tz), i * 15)
                    for i in range(2 * 4 + 1)  # 14:00 till 16:00 every 15min
                ],
            ),
        ],
    )
    with override(tz):
        booksy_slots = TimeSlotsForward.booksy_slots_from_partner_slots(
            partner_slots,
            None,
        )

    merged_slots = booksy_slots.merged_slots
    assert merged_slots.index.tolist() == [date(2021, 3, 29), date(2021, 3, 30)]
    # 10:00 - 20:00
    actual_29th_slots = merged_slots.loc[date(2021, 3, 29)].slots
    assert list(map(humanize_stamp, actual_29th_slots)) == expected_29th_hours
    # 11:00 - 19:00
    actual_30th_slots = merged_slots.loc[date(2021, 3, 30)].slots
    assert list(map(humanize_stamp, actual_30th_slots)) == expected_30th_hours

    assert legacy_slots_from_booksy_slots(booksy_slots, interval=15)


def test_convert_partner_slots__empty():
    partner_slots = TimeSlotsQueryResponse(
        business_id=10,
        staffers_availability=[
            dict(
                staffer_id=110,
                slots=[],
            ),
            dict(
                staffer_id=111,
                slots=[],
            ),
        ],
    )
    booksy_slots = TimeSlotsForward.booksy_slots_from_partner_slots(
        partner_slots,
        None,
    )
    assert not booksy_slots.staff_slots
    assert not booksy_slots.merged_slots


@pytest.mark.freeze_time('2021-03-22')  # monday
def test_legacy_timeslots_handler(api_client, channel, business, staffer, partner, customer):
    service = business.services.first()
    service_variant = service.service_variants.first()
    service.add_staffers([staffer])
    tz = business.get_timezone()

    subbooking, *__ = create_subbooking(
        business=business,
        booking_kws=dict(
            status=Appointment.STATUS.UNCONFIRMED,
            booked_from=datetime(2021, 3, 29, 12, tzinfo=tz),
            booked_till=datetime(2021, 3, 29, 13, tzinfo=tz),
            service_variant=service_variant,
        ),
    )

    request = dict(
        service_variant_id=service_variant.id,
        start_date='2021-03-29',  # monday
        end_date='2021-03-29',
        booking_id=subbooking.id,
    )
    partner_response = TimeSlotsQueryResponse(
        business_id=business.id,
        staffers_availability=[
            dict(
                staffer_id=staffer.id,
                slots=[
                    stamp(datetime(2021, 3, 29, 10, tzinfo=tz), i * 15)
                    for i in range(10 * 4 + 1)  # 10:00 till 20:00
                ],
            )
        ],
    )

    grpc_mock = Mock(return_value=partner_response)
    channel.unary_unary.side_effect = None
    channel.unary_unary.return_value = grpc_mock

    url = '/customer_api/me/bookings/time_slots/'
    resp = api_client.fetch(url, args=request)

    grpc_mock.assert_called_once()
    called_with = grpc_mock.call_args.args[0]
    assert isinstance(called_with, TimeSlotsQueryRequest)

    expected_request_data = dict(
        business_id=business.id,
        subbookings=[
            TimeSlotsQueryRequest.SubBooking(
                service_variant_id=service_variant.id,
                staffer_id=[staffer.id],
            ),
        ],
        start_date=int(datetime(2021, 3, 29, tzinfo=gettz('UTC')).timestamp()),
        end_date=int(datetime(2021, 3, 29, tzinfo=gettz('UTC')).timestamp()),
    )

    if subbooking.appointment.import_uid:
        expected_request_data['omit_appointment_import_id'] = subbooking.appointment.import_uid

    assert called_with == TimeSlotsQueryRequest(**expected_request_data)

    assert resp.code == HTTP_200_OK
    assert resp.json['time_slots'][0] == dict(
        date='2021-03-29',
        time_slots=[{'from': '10:00', 'to': '20:00', 'interval': 15}],
    )


@pytest.mark.freeze_time('2021-03-22')  # monday
def test_appointment_timeslots_handler(api_client, channel, business, staffer, partner, customer):
    service = business.services.first()
    service_variant = service.service_variants.first()
    service.add_staffers([staffer])
    tz = business.get_timezone()

    subbooking, *__ = create_subbooking(
        business=business,
        booking_kws=dict(
            status=Appointment.STATUS.UNCONFIRMED,
            booked_from=datetime(2021, 3, 29, 12, tzinfo=tz),
            booked_till=datetime(2021, 3, 29, 13, tzinfo=tz),
            service_variant=service_variant,
        ),
    )
    appointment_id = subbooking.appointment_id

    request = dict(
        start_date='2021-03-29',  # monday
        end_date='2021-03-29',
        subbookings=[
            dict(service_variant_id=service_variant.id),
            dict(service_variant_id=service_variant.id, staffer_id=staffer.id),
        ],
    )
    partner_response = TimeSlotsQueryResponse(
        business_id=business.id,
        staffers_availability=[
            dict(
                staffer_id=staffer.id,
                slots=[
                    stamp(datetime(2021, 3, 29, 10, tzinfo=tz), i * 15)
                    for i in range(10 * 4 + 1)  # 10:00 till 20:00
                ],
            )
        ],
    )

    grpc_mock = Mock(return_value=partner_response)
    channel.unary_unary.side_effect = None
    channel.unary_unary.return_value = grpc_mock
    url = f'/customer_api/me/businesses/{business.id}/appointments/{appointment_id}/time_slots/'
    resp = api_client.fetch(url, method='POST', body=request)

    grpc_mock.assert_called_once()
    called_with = grpc_mock.call_args.args[0]
    assert isinstance(called_with, TimeSlotsQueryRequest)

    expected_request_data = dict(
        business_id=business.id,
        subbookings=[
            TimeSlotsQueryRequest.SubBooking(service_variant_id=service_variant.id, staffer_id=[]),
            TimeSlotsQueryRequest.SubBooking(
                service_variant_id=service_variant.id,
                staffer_id=[staffer.id],
            ),
        ],
        start_date=int(datetime(2021, 3, 29, tzinfo=gettz('UTC')).timestamp()),
        end_date=int(datetime(2021, 3, 29, tzinfo=gettz('UTC')).timestamp()),
    )

    if subbooking.appointment.import_uid:
        expected_request_data['omit_appointment_import_id'] = subbooking.appointment.import_uid

    assert called_with == TimeSlotsQueryRequest(**expected_request_data)

    assert resp.code == HTTP_200_OK
    assert resp.json['time_slots'][0]['date'] == '2021-03-29'
    assert resp.json['time_slots'][0]['slots'][0] == {'t': '10:00', 'p': ''}
