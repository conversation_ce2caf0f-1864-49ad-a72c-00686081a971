import pytest
from mock import patch
from model_bakery import baker
from segment.analytics import <PERSON><PERSON>

from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.user.models import EmailToken, UnsubscribedEmail, User


@pytest.mark.django_db
class TestUnsubscribe(BaseAsyncHTTPTest):

    def setUp(self):
        self.url = '/customer_api/unsubscribe/'
        self.user = baker.make(
            User,
            first_name="<PERSON>",
            last_name="Testowy",
            email='<EMAIL>',
            cell_phone='123456789',
        )

        return super().setUp()

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_valid_email_unsubscribe(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        token = EmailToken.create_token(self.user.email)
        data = {'token': token}

        assert UnsubscribedEmail.can_send_email(self.user.email)

        response = self.fetch(self.url, method='POST', body=data)
        self.assertEqual(response.code, 201)

        assert not UnsubscribedEmail.can_send_email(self.user.email)
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Contact_Preferencess_Updated',
                'properties': {
                    'email': self.user.email,
                    'user_unsubscribedemail': False,
                    'marketing_agreement': False,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'email': self.user.email,
                    'user_role': 'Owner',
                    'user_unsubscribedemail': False,
                    'marketing_agreement': False,
                },
            },
        )
