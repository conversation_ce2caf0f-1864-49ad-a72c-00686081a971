import datetime
from datetime import date, time

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core import mail
from django.utils.translation import gettext as _
from django.test import override_settings
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from rest_framework import status
from segment.analytics import Client

from country_config import Country
from lib.feature_flag.feature.business import (
    ResourceVisibleValidationFlag,
    PreventResetCurrentStafferServicesFlag,
)

from lib.tests.utils import override_feature_flag, override_eppo_feature_flag
from lib.time_24_hour import time24hour
from lib.tools import id_to_external_api
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.tests.utils import create_appointment
from webapps.session.ports import get_session
from webapps.business.baker_recipes import (
    appliance_recipe,
    combo_service_recipe,
    service_recipe,
    service_variant_recipe,
    service_with_variant_recipe,
    staffer_recipe,
)
from webapps.business.enums import ComboType, StaffAccessLevels
from webapps.business.models import ComboMembership, Resource, Service, ServiceVariant
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.french_certification.models import JET
from webapps.images.baker_recipes import fake_image_url
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.photo.models import Photo
from webapps.schedule.models import ResourceHours
from webapps.schedule.ports import (
    get_business_default_hours,
    get_resource_default_hours,
)
from webapps.segment.consts import UserRoleEnum
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserSessionCache

INVITATION_SUBJECT = 'Invitation to Booksy'
TIME_TO_FREEZE = datetime.datetime(2021, 6, 29, 21, 24, tzinfo=UTC)


@pytest.mark.django_db
class ResourceRichHandlerTestCase(BaseAsyncHTTPTest):

    def test_get_valid(self):
        # pylint: disable=use-dict-literal
        biz = self.business
        res_0 = self.owner
        res_1 = baker.make(Resource, business=biz, staff_cell_phone='555')
        res_2 = baker.make(Resource, business=biz, position='')
        url = f'/business_api/me/businesses/{biz.id}/resources/'

        response = self.fetch(url, method='GET')

        exp_resp = dict(
            resources=[
                dict(
                    id=res_0.id,
                    name=res_0.name,
                    type=res_0.type,
                    description=res_0.description,
                    visible=res_0.visible,
                    staff_email=res_0.staff_email,
                    staff_cell_phone='',
                    staff_cell_phone_with_prefix='',
                    staff_has_push=False,
                    staff_access_level=res_0.staff_access_level,
                    is_current_user=True,
                    staff_user_exists=True,
                    position='Senior Barber',
                    is_invited=False,
                    partner_app_data={},
                    has_store_available=False,
                ),
                dict(
                    id=res_1.id,
                    name=res_1.name,
                    type=res_1.type,
                    description=res_1.description,
                    visible=res_1.visible,
                    staff_email=res_1.staff_email,
                    staff_cell_phone='555',
                    staff_cell_phone_with_prefix='555',
                    staff_has_push=False,
                    staff_access_level=res_1.staff_access_level,
                    is_current_user=False,
                    staff_user_exists=False,
                    position='',
                    is_invited=False,
                    partner_app_data={},
                    has_store_available=False,
                ),
                dict(
                    id=res_2.id,
                    name=res_2.name,
                    type=res_2.type,
                    description=res_2.description,
                    visible=res_2.visible,
                    staff_email=res_2.staff_email,
                    staff_cell_phone='',
                    staff_cell_phone_with_prefix='',
                    staff_has_push=False,
                    staff_access_level=res_2.staff_access_level,
                    is_current_user=False,
                    staff_user_exists=False,
                    position='',
                    is_invited=False,
                    partner_app_data={},
                    has_store_available=False,
                ),
            ],
            resources_per_page=settings.RESOURCES_PER_PAGE,
            resources_count=3,
        )
        assert response.code == 200
        assert response.json == exp_resp

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_validin_ecommerce_allowed_country(self):
        biz = self.business
        baker.make(Resource, business=biz, staff_cell_phone='555')
        baker.make(Resource, business=biz, position='')
        url = f'/business_api/me/businesses/{biz.id}/resources/'

        response = self.fetch(url, method='GET')

        assert response.code == 200
        expected_statuses = [True, False, False]
        for i, expected_status in enumerate(expected_statuses):
            assert response.json['resources'][i]['has_store_available'] == expected_status

    def test_get_staff_cell_phone_with_prefix(self):
        baker.make(Resource, business=self.business, staff_cell_phone='(*************')

        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        response = self.fetch(url, method='GET')
        assert response.code == 200
        assert response.json['resources'][1]['staff_cell_phone_with_prefix'] == '*************'

    def test_get_without_resources(self):
        self.owner.delete()
        url = f'/business_api/me/businesses/{self.business.id}/resources/'

        response = self.fetch(url, method='GET')

        # pylint: disable=use-dict-literal
        exp_resp = dict(
            resources=[],
            resources_per_page=settings.RESOURCES_PER_PAGE,
            resources_count=0,
        )
        assert response.code == 200
        assert response.json == exp_resp

    def test_get_business_invalid(self):
        self.owner.delete()
        url = f'/business_api/me/businesses/{self.business.id + 1}/resources/'

        response = self.fetch(url, method='GET')

        assert response.code == 404
        assert response.json == {
            'errors': [
                {
                    'code': 'not_found',
                    'description': 'Requested object not found',
                    'type': 'invalid',
                },
            ],
        }

    def test_photo(self):
        photo = baker.make('photo.Photo', image_url=fake_image_url())

        staffer_recipe.make(business=self.business, photo=photo, position='')
        staffer_recipe.make(business=self.business)

        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        response = self.fetch(url, method='GET')
        assert response.code == 200

        resource_with_photo = response.json['resources'][1]
        resource_no_photo = response.json['resources'][2]

        assert resource_with_photo['photo'] == photo.id
        assert resource_with_photo['photo_url'] == photo.full_url

        assert 'photo' not in resource_no_photo.keys()
        assert 'photo_url' not in resource_no_photo.keys()

    def test_post_business_invalid(self):
        url = f'/business_api/me/businesses/{self.business.id + 1}/resources/'

        response = self.fetch(url, method='POST', body={})

        assert response.code == 404
        assert response.json == {
            'errors': [
                {
                    'code': 'not_found',
                    'description': 'Requested object not found',
                    'type': 'invalid',
                },
            ],
        }

    def test_post_empty_body(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'

        response = self.fetch(url, method='POST', body={})

        assert response.code == 400
        json = response.json
        json['errors'].sort(key=_sort_key)
        assert json == {
            'errors': sorted(
                [
                    {
                        'description': 'This field is required.',
                        'code': 'required',
                        'field': 'type',
                    },
                    {
                        'description': 'This field is required.',
                        'code': 'required',
                        'field': 'name',
                    },
                ],
                key=_sort_key,
            ),
        }

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_post_create_resource_staff(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'services': [service.id],
            'description': 'desc',
            'time_slots': [
                {
                    'day_of_week': 1,
                    'hour_from': '10:00',
                    'hour_till': '19:00',
                },
                {
                    'day_of_week': 2,
                    'hour_from': '11:00',
                    'hour_till': '18:00',
                },
            ],
            'type': 'S',
            'visible': True,
            'staff_access_level': 'staff',
            'staff_user_exists': False,
            'staff_cell_phone': '(*************',
            'staff_email': '<EMAIL>',
            'name': 'SomeName',
            'position': 'Barber',
        }

        body_working_hours = [
            {'day_of_week': 0, 'hours': []},
            {
                'day_of_week': 1,
                'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}],
            },
            {
                'day_of_week': 2,
                'hours': [{'hour_from': '11:00', 'hour_till': '18:00'}],
            },
            {'day_of_week': 3, 'hours': []},
            {'day_of_week': 4, 'hours': []},
            {'day_of_week': 5, 'hours': []},
            {'day_of_week': 6, 'hours': []},
        ]
        response = self.fetch(url, method='POST', body=body)

        assert response.code == 201
        resources = list(Resource.objects.filter(business=self.business).all())
        assert len(resources) == 2  # first resource is business owner
        resource = resources[1]
        services = [service.id for service in resource.active_services]
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        assert resource.name == body['name']
        assert resource.description == body['description']
        assert resource.visible == body['visible']
        assert resource.staff_access_level == body['staff_access_level']
        assert resource.staff_cell_phone == body['staff_cell_phone']
        assert resource.staff_email == body['staff_email']
        assert resource.type == body['type']
        assert services == [service.id]
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        assert working_hrs[2] == [(time24hour(11, 0), time24hour(18, 0))]
        assert response.json == {
            'message': '',
            'resource': {
                'active': True,
                'description': body['description'],
                'id': resource.id,
                'is_current_user': False,
                'name': body['name'],
                'position': body['position'],
                'services': [service.id],
                'staff_access_level': body['staff_access_level'],
                'staff_cell_phone': body['staff_cell_phone'],
                'staff_cell_phone_with_prefix': '*************',
                'staff_email': body['staff_email'],
                'staff_user_exists': body['staff_user_exists'],
                'time_offs': [],
                'time_slots': body['time_slots'],
                'working_hours': body_working_hours,
                'hours_apply_from': None,
                'type': body['type'],
                'unhandled_services': [],
                'visible': body['visible'],
                'visible_on_calendar': True,
                'is_invited': False,
                'has_store_available': False,
            },
        }
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'event': 'Staffer_Created',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'resource_id': resource.id,
                    'staff_name': 'SomeName',
                    'staff_phone': '*************',
                    'staff_email': '<EMAIL>',
                    'has_been_invited': False,
                    'service_id': [service.id],
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'traits': {
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_staffers_count': 2,
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_post_create_resource_staff_staff_user_exists(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'services': [service.id],
            'description': 'desc',
            'time_slots': [
                {
                    'day_of_week': 1,
                    'hour_from': '10:00',
                    'hour_till': '19:00',
                },
                {
                    'day_of_week': 2,
                    'hour_from': '11:00',
                    'hour_till': '18:00',
                },
            ],
            'type': 'S',
            'visible': True,
            'staff_access_level': 'staff',
            'staff_user_exists': True,
            'staff_cell_phone': '(*************',
            'staff_email': '<EMAIL>',
            'name': 'SomeName',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 201
        resources = list(Resource.objects.filter(business=self.business).all())
        assert len(resources) == 2  # first resource is business owner
        resource = resources[1]
        services = resource.active_services_ids
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        assert resource.name == body['name']
        assert resource.description == body['description']
        assert resource.visible == body['visible']
        assert resource.staff_access_level == body['staff_access_level']
        assert resource.staff_cell_phone == body['staff_cell_phone']
        assert resource.staff_email == body['staff_email']
        assert resource.type == body['type']
        assert services == [service.id]
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        assert working_hrs[2] == [(time24hour(11, 0), time24hour(18, 0))]
        dict_assert(
            response.json,
            {
                'message': '',
                'resource': {
                    'active': True,
                    'description': body['description'],
                    'id': resource.id,
                    'is_current_user': False,
                    'name': body['name'],
                    'services': [service.id],
                    'staff_access_level': body['staff_access_level'],
                    'staff_cell_phone': body['staff_cell_phone'],
                    'staff_email': body['staff_email'],
                    'staff_user_exists': True,
                    'time_offs': [],
                    'time_slots': body['time_slots'],
                    'type': body['type'],
                    'unhandled_services': [],
                    'visible': body['visible'],
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'event': 'Staffer_Created',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'resource_id': resource.id,
                    'staff_name': 'SomeName',
                    'staff_phone': '*************',
                    'staff_email': '<EMAIL>',
                    'has_been_invited': True,
                    'service_id': [service.id],
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'traits': {
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_staffers_count': 2,
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_post_create_resource_staff_during_business_create(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'time_slots': [
                {
                    'day_of_week': 1,
                    'hour_from': '10:00',
                    'hour_till': '19:00',
                },
                {
                    'day_of_week': 2,
                    'hour_from': '11:00',
                    'hour_till': '18:00',
                },
            ],
            'type': 'S',
            'staff_access_level': 'staff',
            'staff_user_exists': True,
            'send_invitation': True,
            'name': 'SomeName',
            'staff_email': '<EMAIL>',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 201
        resources = list(Resource.objects.filter(business=self.business).all())
        assert len(resources) == 2  # first resource is business owner
        resource = resources[1]
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        assert resource.name == body['name']
        assert resource.staff_access_level == body['staff_access_level']
        assert resource.staff_email == body['staff_email']
        assert resource.type == body['type']
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        assert working_hrs[2] == [(time24hour(11, 0), time24hour(18, 0))]
        dict_assert(
            response.json,
            {
                'message': '',
                'resource': {
                    'active': True,
                    'id': resource.id,
                    'is_current_user': False,
                    'name': body['name'],
                    'staff_access_level': body['staff_access_level'],
                    'staff_email': body['staff_email'],
                    'staff_user_exists': True,
                    'time_offs': [],
                    'time_slots': body['time_slots'],
                    'type': body['type'],
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'event': 'Staffer_Created',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'resource_id': resource.id,
                    'staff_name': 'SomeName',
                    'staff_phone': '',
                    'staff_email': '<EMAIL>',
                    'has_been_invited': True,
                    'service_id': [],
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'traits': {
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_staffers_count': 2,
                },
            },
        )

    def test_post_invalid_service(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'services': [999],
            'type': 'S',
            'name': 'SomeName',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'description': 'Invalid pk "999" - object does not exist.',
                    'code': 'does_not_exist',
                    'field': 'services',
                },
            ],
        }

    def test_post_invalid_type(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'X',
            'name': 'SomeName',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'invalid_choice',
                    'description': '"X" is not a valid choice.',
                    'field': 'type',
                },
            ],
        }

    def test_post_invalid_staff_access_level(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': Resource.STAFF,
            'staff_access_level': 'X',
            'name': 'SomeName',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        dict_assert(
            response.json['errors'][0],
            {
                'code': 'invalid',
                'description': "Value 'X' is not a valid choice.",
                'field': 'staff_access_level',
            },
        )

    def test_post_empty_name(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'R',
            'name': '',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'blank',
                    'description': 'This field may not be blank.',
                    'field': 'name',
                },
            ],
        }

    def test_post_invalid_phone(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_cell_phone': '567',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'description': 'Phone number is not valid',
                    'code': 'invalid',
                    'field': 'staff_cell_phone',
                },
            ],
        }

    def test_post_email_exists(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'conflict',
                    'description': ('Staff member with this email already exists.'),
                    'field': 'staff_email',
                },
            ],
        }

    def test_post_staff_user_exists(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_user_exists': True,
            'staff_email': '<EMAIL>',
            'position': 'Barber',
        }

        tzinfo = self.business.get_timezone()
        time_to_freeze = datetime.datetime(2021, 6, 29, 21, 24, tzinfo=tzinfo)
        with freeze_time(time_to_freeze):
            response = self.fetch(url, method='POST', body=body)

            assert response.code == 201
            resources = list(Resource.objects.all())
            assert len(resources) == 2
            resource = resources[1]  # first resource is business owner
            assert response.json == {
                'message': '',
                'resource': {
                    'active': True,
                    'description': None,
                    'id': resource.id,
                    'is_current_user': False,
                    'name': body['name'],
                    'position': body['position'],
                    'services': [],
                    'staff_access_level': 'staff',
                    'staff_cell_phone': None,
                    'staff_cell_phone_with_prefix': '',
                    'staff_email': body['staff_email'],
                    'staff_user_exists': body['staff_user_exists'],
                    'time_offs': [],
                    'time_slots': [],
                    'working_hours': [],
                    'hours_apply_from': None,
                    'type': body['type'],
                    'unhandled_services': [],
                    'visible': True,
                    'visible_on_calendar': True,
                    'is_invited': True,
                    'has_store_available': False,
                },
            }

            assert resource.name == body['name']
            assert resource.staff_user.first_name == body['name']
            assert resource.staff_email == body['staff_email']
            assert resource.staff_user.email == body['staff_email']

    def test_post_staff_user_exists_appliance(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'R',
            'name': 'SomeName',
            'staff_user_exists': True,
            'staff_email': '<EMAIL>',
            'business': 'read',
            'business_id': 'read',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 201
        resources = list(Resource.objects.all())
        assert len(resources) == 2
        resource = resources[1]  # first resource is business owner
        assert response.json == {
            'message': '',
            'resource': {
                'active': True,
                'description': None,
                'id': resource.id,
                'is_current_user': False,
                'name': body['name'],
                'position': '',
                'services': [],
                'staff_access_level': None,
                'staff_cell_phone': None,
                'staff_cell_phone_with_prefix': '',
                'staff_email': body['staff_email'],
                'staff_user_exists': False,
                'time_offs': [],
                'time_slots': [],
                'working_hours': [],
                'hours_apply_from': None,
                'type': body['type'],
                'unhandled_services': [],
                'visible': True,
                'visible_on_calendar': True,
                'is_invited': False,
                'has_store_available': False,
            },
        }

        assert resource.name == body['name']
        assert resource.staff_email == body['staff_email']
        assert resource.staff_user is None
        assert resource.business_id == self.business.id

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json == {
            'errors': [
                {
                    'code': 'unique',
                    'description': ('Appliance with this name already exists ' 'for this business'),
                    'field': 'non_field_errors',
                },
            ]
        }

    def test_post_staff_user_exists_no_email(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_user_exists': True,
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'required',
                    'description': 'Email is required for staff with account',
                    'field': 'staff_email',
                },
            ],
        }

    def test_post_business_invalid_time_slots(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'time_slots': 'invalid value',
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'not_a_list',
                    'description': ('Expected a list of items but got type "str".'),
                    'field': 'time_slots.non_field_errors',
                },
            ],
        }

        body['time_slots'] = [
            {
                'day_of_week': 6,
                'hour_from': '10_00',
                'hour_till': '29:00',
            }
        ]

        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'invalid',
                    'description': (
                        'Time has wrong format. '
                        'Use one of these formats instead: hh:mm[:ss[.uuuuuu]].'
                    ),
                    'field': 'time_slots.0.hour_from',
                },
                {
                    'code': 'invalid',
                    'description': (
                        'Time has wrong format. '
                        'Use one of these formats instead: hh:mm[:ss[.uuuuuu]].'
                    ),
                    'field': 'time_slots.0.hour_till',
                },
            ],
        }

    def test_post_resource_name_too_long(self):
        url = f'/business_api/me/businesses/{self.business.id}/resources/'

        body = {
            'type': Resource.APPLIANCE,
            'name': 'a' * 201,
        }
        response = self.fetch(url, method='POST', body=body)
        assert response.code == 201

        body['name'] = 'a' * 202
        response = self.fetch(url, method='POST', body=body)

        assert response.code == 400
        assert response.json['errors'][0] == {
            'field': 'name',
            'description': _('Resource name is too long'),
            'code': 'max_length',
        }


@pytest.mark.django_db
class ResourceHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/resources/{}/'.format

    def test_get_valid(self):
        res_0 = self.owner
        expected_response = {
            "resource": {
                "id": res_0.id,
                "type": res_0.type,
                "name": res_0.name,
                "active": res_0.active,
                "visible": res_0.visible,
                'visible_on_calendar': True,
                "description": res_0.description,
                "staff_user_exists": bool(res_0.staff_user_id),
                "staff_email": res_0.staff_email,
                "staff_cell_phone": res_0.staff_cell_phone or '',
                "staff_access_level": res_0.staff_access_level,
                "reviews_rank_avg": res_0.reviews_rank_avg,
                "services": [],
                "unhandled_services": [],
                "is_current_user": True,
                "time_slots": [],
                "working_hours": [],
                "hours_apply_from": None,
                "time_offs": [],
                "upcoming_time_offs": [],
                "position": 'Senior Barber',
                "is_invited": False,
                "partner_app_data": {},
                'has_store_available': False,
            }
        }

        response = self.fetch(self.url(res_0.id), method='GET')

        assert response.code == 200
        del response.json['resource']['time_off_reasons']
        assert response.json == expected_response

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_validin_ecommerce_allowed_country(self):
        response = self.fetch(self.url(self.owner.id), method='GET')

        assert response.code == 200
        assert response.json['resource']['has_store_available'] is True

    def test_put_empty_body(self):
        response = self.fetch(self.url(self.owner.id), method='PUT', body={})

        assert response.code == 400
        assert response.json == {
            'errors': [
                {'code': 'required', 'description': 'Type is required.', 'field': 'type'},
                {'code': 'required', 'description': 'Name is required.', 'field': 'name'},
            ]
        }

    def test_put_invalid_type(self):
        body = {
            'type': 'X',
            'name': 'SomeName',
            'staff_email': self.owner.staff_email,
        }

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'invalid_choice',
                    'description': '"X" is not a valid choice.',
                    'field': 'type',
                },
            ],
        }

    def test_put_deactive_all_resources(self):
        resource = self.owner
        old_name = resource.name
        body = {
            'type': 'S',
            'name': 'NewName',
            'staff_email': self.owner.staff_email,
            'active': False,
        }
        response = self.fetch(self.url(resource.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'invalid',
                    'field': '__all__',
                    'description': ('Active business require at least one ' 'staffer or resource.'),
                },
            ],
        }
        resource.refresh_from_db()
        assert resource.name == old_name

    def test_put_invalid_resource_id(self):
        response = self.fetch(self.url(self.owner.id + 1), method='PUT', body={})

        assert response.code == 404
        assert response.json == {
            'errors': [
                {
                    'type': 'invalid',
                    'code': 'not_found',
                    'description': 'Requested object not found',
                },
            ],
        }

    def test_put_valid_with_create_user(self):
        resource = baker.make(Resource, business=self.business, type=Resource.STAFF, active=True)
        service = baker.make(Service, business=self.business, active=True)
        baker.make(ServiceVariant, duration='0100', service=service, active=True)
        body = {
            'type': 'S',
            'name': 'NewName',
            'description': 'NewDesc',
            'staff_email': '<EMAIL>',
            'staff_cell_phone': '(*************',
            'staff_access_level': 'staff',
            'staff_user_exists': True,
            'time_slots': [
                {
                    'day_of_week': 1,
                    'hour_from': '10:00',
                    'hour_till': '19:00',
                },
                {
                    'day_of_week': 2,
                    'hour_from': '11:00',
                    'hour_till': '18:00',
                },
            ],
            'services': [service.id],
            'visible': False,
            'active': False,
            'unhandled_services': ['only to read'],
            'id': 'only to read',
            'time_offs': ['read_only'],
            'position': 'Some Staff',
        }
        b_opening_hrs = get_business_default_hours(business_id=self.business.id)
        users = set(User.objects.values_list('email', flat=True))

        response = self.fetch(self.url(resource.id), method='PUT', body=body)

        assert response.code == 200
        resource.refresh_from_db()
        services = resource.active_services_ids
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        self.business.refresh_from_db()
        users_after = set(User.objects.values_list('email', flat=True))
        assert users_after - users == {body['staff_email']}
        assert resource.name == body['name']
        assert resource.description == body['description']
        assert resource.visible == body['visible']
        assert resource.staff_access_level == body['staff_access_level']
        assert resource.staff_cell_phone == body['staff_cell_phone']
        assert resource.staff_user.cell_phone == body['staff_cell_phone']
        assert resource.staff_email == body['staff_email']
        assert resource.staff_user.email == body['staff_email']
        assert resource.type == body['type']
        assert resource.business_id == self.business.id
        assert services == [service.id]
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        assert working_hrs[2] == [(time24hour(11, 0), time24hour(18, 0))]
        assert working_hrs[3] == []
        assert get_business_default_hours(business_id=self.business.id) == b_opening_hrs
        dict_assert(
            response.json,
            {
                'resource': {
                    'id': resource.id,
                    'type': body['type'],
                    'name': body['name'],
                    'position': 'Some Staff',
                    'active': body['active'],
                    'visible': body['visible'],
                    'description': body['description'],
                    'staff_user_exists': body['staff_user_exists'],
                    'staff_email': body['staff_email'],
                    'staff_cell_phone': body['staff_cell_phone'],
                    'staff_access_level': body['staff_access_level'],
                    'services': body['services'],
                    'unhandled_services': [],
                    'is_current_user': False,
                    'time_offs': [],
                },
                'business_hours_update': None,
            },
        )

    def test_put_overwrite_biz_opening_hours(self):
        old_desc = 'OldDesc'
        self.owner.description = old_desc
        self.owner.save()
        body = {
            'type': 'S',
            'name': 'NewName',
            'staff_email': self.owner.staff_email,
            'time_slots': [
                {
                    'day_of_week': 1,
                    'hour_from': '10:00',
                    'hour_till': '19:00',
                },
            ],
            'overwrite_biz_opening_hours': True,
        }
        users = User.objects.count()

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 200
        self.owner.refresh_from_db()
        resource = self.owner
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        self.business.refresh_from_db()
        b_opening_hrs = get_business_default_hours(business_id=self.business.id)
        assert User.objects.count() == users
        assert resource.name == body['name']
        assert resource.description == old_desc
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        assert b_opening_hrs[1] == working_hrs[1]
        dict_assert(
            response.json,
            {
                'resource': {
                    'id': self.owner.id,
                    'type': body['type'],
                    'name': body['name'],
                    'active': True,
                    'visible': True,
                    'description': old_desc,
                    'staff_user_exists': True,
                    'staff_email': body['staff_email'],
                    'staff_cell_phone': '',
                    'staff_access_level': 'owner',
                    'services': [],
                    'unhandled_services': [],
                    'is_current_user': True,
                    'time_slots': body['time_slots'],
                    'time_offs': [],
                    'position': 'Senior Barber',
                },
                'business_hours_update': True,
            },
        )

    def test_put_overwrite_working_hours_all_days_closed(self):
        staffer_resource = baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
            name='Some Staffer',
            staff_email='<EMAIL>',
        )
        hours = ResourceHours.get_hours(
            business_id=self.business.id,
            resource_id=staffer_resource.id,
        )
        hours[2] = [(time(14), time(21))]
        ResourceHours.set_hours(
            business_id=self.business.id,
            resource_id=staffer_resource.id,
            hours=hours,
            tz=self.business.get_timezone(),
            apply_from=date(2021, 3, 1),
        )
        body = {
            'type': staffer_resource.type,
            'name': staffer_resource.name,
            'staff_email': staffer_resource.staff_email,
            'time_slots': [],
        }
        response = self.fetch(self.url(staffer_resource.id), method='PUT', body=body)
        self.assertEqual(status.HTTP_200_OK, response.code)

        staffer_resource.refresh_from_db()
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=staffer_resource.id,
        )

        for i in range(7):
            self.assertListEqual([], working_hrs[i])

    def test_put_working_hours(self):
        old_desc = 'OldDesc'
        self.owner.description = old_desc
        self.owner.save()
        body = {
            'type': 'S',
            'name': 'NewName',
            'staff_email': self.owner.staff_email,
            'working_hours': [
                {'day_of_week': 1, 'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}]}
            ],
            'overwrite_biz_opening_hours': True,
        }
        body_time_slots = [
            {
                'day_of_week': 1,
                'hour_from': '10:00',
                'hour_till': '19:00',
            },
        ]
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 200
        self.owner.refresh_from_db()
        resource = self.owner
        working_hrs = get_resource_default_hours(
            business_id=self.business.id,
            resource_id=resource.id,
        )
        assert working_hrs[2] == []
        assert working_hrs[1] == [(time24hour(10, 0), time24hour(19, 0))]
        dict_assert(
            response.json,
            {
                'resource': {
                    'id': self.owner.id,
                    'type': body['type'],
                    'name': body['name'],
                    'active': True,
                    'visible': True,
                    'description': old_desc,
                    'staff_user_exists': True,
                    'staff_email': body['staff_email'],
                    'staff_cell_phone': '',
                    'staff_access_level': 'owner',
                    'services': [],
                    'unhandled_services': [],
                    'is_current_user': True,
                    'time_slots': body_time_slots,
                    'time_offs': [],
                },
                'business_hours_update': True,
            },
        )

    def test_put_staff_user_without_email(self):
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_user_exists': True,
        }

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'required',
                    'field': 'staff_email',
                    'description': 'Email is required for staff with account',
                },
            ],
        }

    def test_put_staff_email_exists(self):
        res = baker.make(Resource, business=self.business)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
        }

        response = self.fetch(self.url(res.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'conflict',
                    'description': ('Staff member with this email already exists.'),
                    'field': 'staff_email',
                },
            ],
        }

    def test_put_user_email_exists(self):
        res = baker.make(Resource, business=self.business)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'staff_user_exists': True,
        }

        response = self.fetch(self.url(res.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'conflict',
                    'description': ('Staff member with this email already exists.'),
                    'field': 'staff_email',
                },
            ],
        }

    def test_put_abandoned_service(self):
        service = baker.make(Service, name='ServiceX', business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        self.owner.add_services([service.id])
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'services': [],
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'code': 'conflict',
                    'field': 'services',
                    'description': (
                        'At least one staff or resource is required ' 'to perform service ServiceX'
                    ),
                },
            ],
        }

    @override_feature_flag({ResourceVisibleValidationFlag.flag_name: True})
    def test_put_visible_and_single_service(self):
        service = baker.make(Service, name='ServiceX', business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        self.owner.add_services([service.id])
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': True,
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 200)

    @parameterized.expand(
        [
            (True,),
            (False,),
        ]
    )
    def test_put_reset_partially_performed_service_on_staffer(self, flag_value):
        service = baker.make(Service, name='ServiceX', business=self.business)
        performed_sv = baker.make(ServiceVariant, duration='0100', service=service)
        non_performed_sv = baker.make(ServiceVariant, duration='0200', service=service)

        self.owner.add_services([service.id])
        non_performed_sv.remove_staffers([self.owner])

        assert self.owner.service_variants.get().id == performed_sv.id

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': True,
            'services': [service.id],
        }

        with override_eppo_feature_flag(
            {PreventResetCurrentStafferServicesFlag.flag_name: flag_value}
        ):
            response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 200)

        if flag_value:
            # Preserve original variants
            assert self.owner.service_variants.get().id == performed_sv.id
        else:
            # Reset variants of current service
            assert [*self.owner.service_variants.order_by('id')] == [performed_sv, non_performed_sv]

    @override_feature_flag({ResourceVisibleValidationFlag.flag_name: True})
    def test_put_invisible_and_single_service(self):
        service = baker.make(Service, name='ServiceX', business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        self.owner.add_services([service.id])
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': False,
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 400)
        self.assertEqual(
            response.json,
            {
                'errors': [
                    {
                        'code': 'conflict',
                        'field': 'visible',
                        'description': (
                            'At least one staff or resource is required '
                            'to perform service ServiceX'
                        ),
                    },
                ],
            },
        )

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': False,
            'services': [service.id],
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 400)
        self.assertEqual(
            response.json,
            {
                'errors': [
                    {
                        'code': 'conflict',
                        'field': 'visible',
                        'description': (
                            'At least one staff or resource is required '
                            'to perform service ServiceX'
                        ),
                    },
                ],
            },
        )

    @override_feature_flag({ResourceVisibleValidationFlag.flag_name: True})
    def test_put_invisible_and_single_service_with_multiple_variants(self):
        resource = baker.make(Resource, business=self.business, type='S')
        service = baker.make(Service, name='ServiceX', business=self.business)
        variant1 = baker.make(ServiceVariant, duration='0100', service=service)
        variant2 = baker.make(ServiceVariant, duration='0200', service=service)
        resource.add_service_variants([variant1])
        self.owner.add_service_variants([variant2])
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': False,
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 400)
        self.assertEqual(
            response.json,
            {
                'errors': [
                    {
                        'code': 'conflict',
                        'field': 'visible',
                        'description': (
                            "At least one staff or resource is required "
                            "to perform service ServiceX, Don't show variant"
                        ),
                    },
                ],
            },
        )

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'visible': False,
            'services': [service.id],
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        self.assertEqual(response.code, 400)
        self.assertEqual(
            response.json,
            {
                'errors': [
                    {
                        'code': 'conflict',
                        'field': 'visible',
                        'description': (
                            "At least one staff or resource is required "
                            "to perform service ServiceX, Don't show variant"
                        ),
                    },
                ],
            },
        )

    def test_put_should_allow_update_if_legacy_service_staffer_relationship(self):
        service = baker.make(Service, name='ServiceX', business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        self.owner.add_services([service.id])

        staffer = staffer_recipe.make(business=self.business)
        service.resources.add(staffer)  # legacy, service is assigned to staffer by service.id

        response = self.fetch(self.url(staffer.id), method='GET')
        assert response.code == status.HTTP_200_OK
        assert not response.json['resource']['services']

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '',
            'services': [],
        }
        response = self.fetch(self.url(staffer.id), method='PUT', body=body)
        assert response.code == status.HTTP_200_OK

    def test_put_blank_service_should_not_clear_existing_services(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_with_variant_recipe.make(business=self.business)
        staffer.add_services([service.id])

        body_without_services = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '',
        }
        response = self.fetch(self.url(staffer.id), method='PUT', body=body_without_services)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn(service.id, response.json['resource']['services'])

    def test_get_should_not_return_inactive_services(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_with_variant_recipe.make(business=self.business)
        staffer.add_services([service.id])
        self.assertIn(service.id, staffer.active_services_ids)

        response = self.fetch(self.url(staffer.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn(service.id, response.json['resource']['services'])

        service.active = False  # could be changed through admin
        service.save()

        response = self.fetch(self.url(staffer.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertNotIn(service.id, response.json['resource']['services'])

    def test_put_should_should_not_clear_inactive_services(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_with_variant_recipe.make(business=self.business)
        staffer.add_services([service.id])
        self.assertIn(service.id, staffer.active_services_ids)

        service.active = False
        service.save()

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '',
            'services': [],
        }
        response = self.fetch(self.url(staffer.id), method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json['resource']['services'], [])

        self.assertNotIn(service.id, staffer.active_services_ids)
        self.assertTrue(staffer.service_variants.filter(service_id=service.id).exists())

        service.active = True
        service.save()

        response = self.fetch(self.url(staffer.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn(service.id, response.json['resource']['services'])

    def test_put_should_not_allow_update_with_inactive_service(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_with_variant_recipe.make(business=self.business, active=False)

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '',
            'services': [service.id],
        }
        response = self.fetch(self.url(staffer.id), method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

    def test_put_should_not_unassign_service_if_only_assigned_to_appliance(self):
        appliance = appliance_recipe.make(business=self.business)
        service = baker.make(Service, name='ServiceX', business=self.business)
        appliance.add_services([service.id])

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '',
            'services': [],
        }
        response = self.fetch(self.url(appliance.id), method='PUT', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json['errors'][0] == {
            'code': 'conflict',
            'description': 'At least one staff or resource is required to perform service ServiceX',
            'field': 'services',
        }

    def test_put_should_unassign_service_if_also_assigned_to_appliance(self):
        service = baker.make(Service, name='ServiceX', business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        appliance = appliance_recipe.make(business=self.business)
        appliance.add_services([service])
        self.owner.add_services([service])

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'services': [],
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)
        assert response.code == status.HTTP_200_OK

    def test_put_combo_service(self):
        child2 = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=20),
        )
        child1 = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=10),
        )
        combo = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE),
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child1, order=1, gap_time=relativedelta()
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child2, order=2, gap_time=relativedelta()
        )

        expected_service = service_recipe.make(
            business=self.business,
        )
        service_variant_recipe.make(
            service=expected_service,
            duration=relativedelta(minutes=10),
        )
        combo.service.add_staffers([self.owner])

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'services': [expected_service.id, combo.service.id],
        }
        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 200, response.json['errors']
        assert response.json['resource']['services'] == [expected_service.id]

    @patch('webapps.images.tasks.s3_client.upload_file', lambda *a, **kw: None)
    def test_put_add_photo(self):
        photo = baker.make(Photo, image_url='file_path.png')
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'photo': photo.id,
        }

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 200
        assert response.json == {
            'business_hours_update': None,
            'resource': {
                'active': True,
                'description': None,
                'id': self.owner.id,
                'is_current_user': True,
                'name': body['name'],
                'position': 'Senior Barber',
                'photo': body['photo'],
                'photo_url': 'https://img.booksy.pm/test-bucket/' 'file_path.png',
                'services': [],
                'staff_access_level': 'owner',
                'staff_cell_phone': '',
                'staff_cell_phone_with_prefix': '',
                'staff_email': '<EMAIL>',
                'staff_user_exists': True,
                'time_offs': [],
                'time_slots': [],
                'working_hours': [],
                'hours_apply_from': None,
                'type': 'S',
                'unhandled_services': [],
                'visible': True,
                'visible_on_calendar': True,
                'is_invited': False,
                'has_store_available': False,
            },
        }

    def test_put_add_photo_invalid(self):
        mail.outbox = []
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'photo': 100,
        }

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)

        assert response.code == 400
        assert response.json == {
            'errors': [
                {
                    'description': 'Invalid pk "100" - object does not exist.',
                    'code': 'does_not_exist',
                    'field': 'photo',
                },
            ],
        }

    def test_put_send_invitation_the_same_email_with_staff_user(self):
        mail.outbox = []
        url = self.url(self.owner.id)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_user.email,
            'staff_user_exists': True,
            'send_invitation': False,
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 0

        body['send_invitation'] = True
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 0

    @staticmethod
    def _get_user_session_cache_ids():
        return set(UserSessionCache.objects.values_list('id', flat=True))

    def test_put_send_invitation_the_same_email_without_staff_user(self):
        res = baker.make(Resource, business=self.business, staff_email='<EMAIL>')
        mail.outbox = []
        url = self.url(res.id)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': res.staff_email,
            'staff_user_exists': True,
            'send_invitation': False,
        }
        ids = self._get_user_session_cache_ids()

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 1
        assert ids == self._get_user_session_cache_ids()

        body['send_invitation'] = True
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 2
        assert ids == self._get_user_session_cache_ids()

        body['staff_user_exists'] = False
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 3
        assert ids == self._get_user_session_cache_ids()

        assert mail.outbox[0].subject == INVITATION_SUBJECT
        assert mail.outbox[1].subject == INVITATION_SUBJECT
        assert mail.outbox[2].subject == INVITATION_SUBJECT

    @staticmethod
    def _session_and_user_exist(session_key):
        session = get_session(session_key)
        user = session.get_user()
        return len(session.items()) > 0 and user is not None

    def test_put_send_invitation_new_email(self):
        mail.outbox = []
        resource_user = baker.make(User)
        resource = baker.make(Resource, staff_user=resource_user, business=self.business)
        resource_user_session = resource_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )

        url = self.url(resource.id)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': '<EMAIL>',
            'staff_user_exists': True,
            'send_invitation': False,
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 1
        assert not self._session_and_user_exist(resource_user_session.session_key)

        body['send_invitation'] = True
        body['staff_access_level'] = 'staff'
        resource_user_session = resource_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        assert len(mail.outbox) == 2
        assert mail.outbox[0].subject == INVITATION_SUBJECT
        assert mail.outbox[1].subject == INVITATION_SUBJECT
        assert self._session_and_user_exist(resource_user_session.session_key)

    def test_put_empty_values_not_required_staffer(self):
        resource = self.owner
        resource.staff_cell_phone = phone = '(*************'
        resource.description = desc = 'desc'
        resource.staff_access_level = level = 'owner'
        resource.save()
        url = self.url(resource.id)
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': resource.staff_email,
            'staff_access_level': None,
            'staff_cell_phone': None,
            'description': None,
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        resource.refresh_from_db()
        assert resource.staff_access_level == level
        assert resource.description == desc
        assert resource.staff_cell_phone == phone

        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': resource.staff_email,
            'staff_cell_phone': '',
            'description': '',
        }
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        resource.refresh_from_db()
        assert resource.staff_email == body['staff_email']
        assert resource.description == body['description']
        assert resource.staff_cell_phone == body['staff_cell_phone']

    def test_put_empty_values_not_required_appliance(self):
        resource = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            staff_cell_phone='(*************',
            description='desc',
            staff_access_level=None,
            business=self.business,
            staff_email='<EMAIL>',
        )
        level = resource.staff_access_level
        desc = resource.description
        phone = resource.staff_cell_phone
        staff_email = resource.staff_email
        url = self.url(resource.id)
        body = {
            'type': 'R',
            'name': 'SomeName',
            'staff_email': None,
            'staff_access_level': None,
            'staff_cell_phone': None,
            'description': None,
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        resource.refresh_from_db()
        assert resource.staff_email == staff_email
        assert resource.staff_access_level == level
        assert resource.description == desc
        assert resource.staff_cell_phone == phone

        body = {
            'type': 'R',
            'name': 'SomeName',
            'staff_email': '',
            'staff_cell_phone': '',
            'description': '',
        }
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200
        resource.refresh_from_db()
        assert resource.staff_email == body['staff_email']
        assert resource.description == body['description']
        assert resource.staff_cell_phone == body['staff_cell_phone']

    def test_put_last_available_resource(self):
        url = self.url(self.owner.id) + '?skip_extra_validation=false'
        body = {
            'type': 'S',
            'name': 'SomeName',
            'staff_email': self.owner.staff_email,
            'staff_user_exists': True,
            'visible': False,
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'description': 'This is last available staff member.',
                        'code': 'last_available_resource_error',
                    }
                ],
            },
        )
        self.owner.refresh_from_db()
        assert self.owner.deleted is None
        assert self.owner.visible is True
        assert self.owner.active is True

    def test_put_last_available_resource_with_invites(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        bci.invite()
        NotificationHistoryDocument.tasks_refresh()

        url = self.url(self.owner.id) + '?skip_extra_validation=0'
        body = {
            'type': self.owner.type,
            'name': self.owner.name,
            'staff_email': self.owner.staff_email,
            'visible': False,
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'description': 'Invites were sent during last 3 days.',
                        'code': 'invites_error',
                    },
                    {
                        'description': 'This is last available staff member.',
                        'code': 'last_available_resource_error',
                    },
                ],
            },
        )
        self.owner.refresh_from_db()
        assert self.owner.deleted is None
        assert self.owner.visible is True
        assert self.owner.active is True

    def test_put_should_not_allow_privilege_escalation_manager_to_owner(self):
        staffer = staffer_recipe.make(
            business=self.business,
            staff_access_level=StaffAccessLevels.MANAGER,
            staff_email='<EMAIL>',
            staff_user=baker.make(User),
        )
        self.session = staffer.staff_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )

        body = {
            'type': staffer.type,
            'name': staffer.name,
            'staff_email': staffer.staff_email,
            'staff_access_level': StaffAccessLevels.OWNER.value,
        }

        response = self.fetch(self.url(staffer.id), method='PUT', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST

        staffer.refresh_from_db()
        assert staffer.staff_access_level == StaffAccessLevels.MANAGER

    def test_put_should_not_allow_set_lower_permissions_for_owner_by_owner(self):
        body = {
            'type': self.owner.type,
            'name': self.owner.name,
            'staff_email': self.owner.staff_email,
            'staff_access_level': StaffAccessLevels.MANAGER.value,
        }

        response = self.fetch(self.url(self.owner.id), method='PUT', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST

        self.owner.refresh_from_db()
        assert self.owner.staff_access_level == StaffAccessLevels.OWNER

    def test_put_should_not_allow_change_staff_access_level_if_not_owner_or_manager(self):
        staffer = staffer_recipe.make(
            business=self.business,
            staff_access_level=StaffAccessLevels.ADVANCED,
            staff_email='<EMAIL>',
            staff_user=baker.make(User),
        )
        old_name = staffer.name
        url = self.url(staffer.id)
        self.session = staffer.staff_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )

        body = {
            'type': staffer.type,
            'name': 'new_name',
            'staff_email': staffer.staff_email,
            'staff_access_level': StaffAccessLevels.OWNER.value,
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == status.HTTP_404_NOT_FOUND

        staffer.refresh_from_db()
        assert staffer.staff_access_level == StaffAccessLevels.ADVANCED
        assert staffer.name == old_name

        body['staff_access_level'] = StaffAccessLevels.ADVANCED.value
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == status.HTTP_404_NOT_FOUND

        staffer.refresh_from_db()
        assert staffer.staff_access_level == StaffAccessLevels.ADVANCED
        assert staffer.name == old_name

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_should_allow_to_delete_resource_if_has_deleted_future_subbookings(self):
        staffer = staffer_recipe.make(business=self.business)
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business)
        )
        appointment = create_appointment(
            [
                {
                    'service_variant': service_variant,
                    'staffer': staffer,
                    'booked_from': datetime.datetime(2023, 2, 3, 12, tzinfo=UTC),
                    'booked_till': datetime.datetime(2023, 2, 3, 13, tzinfo=UTC),
                },
                {
                    'service_variant': service_variant,
                    'staffer': self.owner,
                    'booked_from': datetime.datetime(2023, 2, 3, 13, tzinfo=UTC),
                    'booked_till': datetime.datetime(2023, 2, 3, 14, tzinfo=UTC),
                },
            ],
            business=self.business,
        )

        booking = staffer.subbookings.first()
        appointment._delete_subbookings([booking.id])  # pylint: disable=protected-access
        booking.refresh_from_db()
        self.assertIsNotNone(booking.deleted)

        response = self.fetch(
            self.url(staffer.id),
            method='DELETE',
            args={'skip_extra_validation': False},
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_should_notify_on_delete_dont_skip_validation(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_recipe.make(business=self.business)
        service_variant_recipe.make(service=service)
        staffer.add_services([service])

        response = self.fetch(
            self.url(staffer.id),
            method='DELETE',
            args={'skip_extra_validation': False},
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse('error' in response.json)
        self.assertFalse('variants' in response.json)
        self.assertTrue('errors' in response.json)

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_should_notify_on_delete_dont_skip_validation_with_new_errors_format(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_recipe.make(business=self.business, name='Service1')
        service_variant_recipe.make(service=service)
        staffer.add_services([service])

        response = self.fetch(
            self.url(staffer.id),
            method='DELETE',
            args={'skip_extra_validation': False},
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse('error' in response.json)
        self.assertTrue('errors' in response.json)
        errors = response.json['errors']
        self.assertEqual('last_service_staffer_deletion_error', errors[0]['code'])
        self.assertEqual('non_field_error', errors[0]['field'])
        self.assertEqual(
            (
                'Staffer can not be deleted because variants belong to services: '
                'Service1 will not be performed by anyone'
            ),
            errors[0]['description'],
        )
        self.assertTrue('data' in errors[0])
        self.assertEqual(1, len(errors[0]['data']['variants']))
        self.assertEqual(0, len(errors[0]['data']['combo_services']))

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_should_delete_orphaning_staffer_skip_validation(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_recipe.make(business=self.business)
        service_variant = service_variant_recipe.make(service=service)
        staffer.add_services([service])
        service.refresh_from_db()
        staffer.refresh_from_db()
        service_variant.refresh_from_db()

        assert list(service_variant.resources.all()) == [staffer]
        assert list(staffer.service_variants.all()) == [service_variant]

        response = self.fetch(
            self.url(staffer.id) + '?skip_extra_validation=true',
            method='DELETE',
        )

        service.refresh_from_db()
        staffer.refresh_from_db()
        service_variant.refresh_from_db()

        assert list(service_variant.resources.all()) == [staffer]
        assert not staffer.service_variants.count()

        assert not service_variant.active
        assert service_variant.deleted is not None
        assert not service.active
        assert service.deleted is not None
        assert not staffer.active
        assert staffer.deleted is not None

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue('message' in response.json)
        self.assertTrue('variants' not in response.json)

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_skip_validation_keep_service_active(self):
        staffer, another_staffer = list(staffer_recipe.make(business=self.business, _quantity=2))
        service = service_recipe.make(business=self.business)
        service_variant1, service_variant2 = list(
            (service_variant_recipe.make(service=service, _quantity=2))
        )
        service_variant1.add_staffers([staffer])
        service_variant2.add_staffers([another_staffer])
        service.refresh_from_db()
        staffer.refresh_from_db()
        another_staffer.refresh_from_db()
        service_variant1.refresh_from_db()
        service_variant2.refresh_from_db()

        assert list(service_variant1.resources.all()) == [staffer]
        assert list(service_variant2.resources.all()) == [another_staffer]
        assert list(staffer.service_variants.all()) == [service_variant1]
        assert list(another_staffer.service_variants.all()) == [service_variant2]

        response = self.fetch(
            self.url(staffer.id) + '?skip_extra_validation=true',
            method='DELETE',
        )

        service.refresh_from_db()
        staffer.refresh_from_db()
        another_staffer.refresh_from_db()
        service_variant1.refresh_from_db()
        service_variant2.refresh_from_db()

        assert list(service_variant1.resources.all()) == [staffer]
        assert not staffer.service_variants.count()
        assert list(service_variant2.resources.all()) == [another_staffer]
        assert list(another_staffer.service_variants.all()) == [service_variant2]

        assert not service_variant1.active
        assert service_variant1.deleted is not None
        assert service.active
        assert service.deleted is None
        assert not staffer.active
        assert staffer.deleted is not None

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue('message' in response.json)
        self.assertTrue('variants' not in response.json)

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_skip_validation_keep_service_and_service_variant(self):
        staffer, another_staffer = list(staffer_recipe.make(business=self.business, _quantity=2))
        service = service_recipe.make(business=self.business)
        service_variant = service_variant_recipe.make(service=service)
        staffer.add_services([service])
        another_staffer.add_services([service])
        service.refresh_from_db()
        staffer.refresh_from_db()
        service_variant.refresh_from_db()
        another_staffer.refresh_from_db()

        assert set(service_variant.resources.all()) == {staffer, another_staffer}
        assert list(staffer.service_variants.all()) == [service_variant]
        assert list(another_staffer.service_variants.all()) == [service_variant]

        response = self.fetch(
            self.url(staffer.id) + '?skip_extra_validation=true',
            method='DELETE',
        )

        service.refresh_from_db()
        staffer.refresh_from_db()
        service_variant.refresh_from_db()
        another_staffer.refresh_from_db()

        assert set(service_variant.resources.all()) == {staffer, another_staffer}
        assert list(staffer.service_variants.all()) == [service_variant]
        assert list(another_staffer.service_variants.all()) == [service_variant]

        assert service_variant.active
        assert service_variant.deleted is None
        assert service.active
        assert service.deleted is None
        assert another_staffer.active
        assert another_staffer.deleted is None

        assert not staffer.active
        assert staffer.deleted is not None

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue('message' in response.json)
        self.assertTrue('variants' not in response.json)

    @freeze_time(datetime.datetime(2023, 2, 3, 10, tzinfo=UTC))
    def test_should_not_allow_to_delete_resource_if_has_future_subbookings(self):
        staffer = staffer_recipe.make(business=self.business)
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business)
        )
        create_appointment(
            [
                {
                    'service_variant': service_variant,
                    'staffer': staffer,
                    'booked_from': datetime.datetime(2023, 2, 3, 12, tzinfo=UTC),
                    'booked_till': datetime.datetime(2023, 2, 3, 13, tzinfo=UTC),
                },
            ],
            business=self.business,
        )

        response = self.fetch(
            self.url(staffer.id),
            method='DELETE',
            args={'skip_extra_validation': False},
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

    @french_certification_enabled(certification_enabled=True)
    def test_put_staff_access_level_change_triggers_jet_event(self):
        staffer = staffer_recipe.make(
            business=self.business,
            staff_access_level=StaffAccessLevels.STAFF,
            staff_email='<EMAIL>',
            staff_user=baker.make(User),
        )
        body = {
            'type': staffer.type,
            'name': staffer.name,
            'staff_email': staffer.staff_email,
            'staff_access_level': StaffAccessLevels.ADVANCED.value,
        }
        path = self.url(staffer.id)

        response = self.fetch(path, method='PUT', body=body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 130)
        self.assertEqual(jet.operator_id, self.user.id)
        self.assertIn(str(staffer.id), jet.additional_info)

    @french_certification_enabled(certification_enabled=True)
    def test_put_appliance_do_not_triggers_jet_event(self):
        appliance = appliance_recipe.make(business=self.business, name='old_name')

        body = {
            'type': 'R',
            'name': 'new_name',
            'staff_email': '',
            'services': [],
        }

        response = self.fetch(self.url(appliance.id), method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json['resource']['name'], 'new_name')
        self.assertEqual(JET.objects.count(), 0)

    def test_delete__not_enough_staffers(self):
        """
        Check removing staffer, when it won't be possible to perform parallel combo.
        """
        parallel_combo = service_variant_recipe.make(
            service=combo_service_recipe.make(
                business=self.business,
                combo_type=ComboType.PARALLEL,
            )
        )
        baker.make(
            ComboMembership,
            combo=parallel_combo,
            child=service_variant_recipe.make(
                service__business=self.business,
            ),
            _quantity=2,
        )
        staffers = list(
            staffer_recipe.make(
                business=self.business,
                _quantity=3 - self.business.number_of_staffers,
            )
        )

        url = self.url(staffers[0].id)
        response = self.fetch(
            url,
            method='DELETE',
            args={
                'skip_extra_validation': '0',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

        url = self.url(staffers[1].id)
        response = self.fetch(
            url,
            method='DELETE',
            args={
                'skip_extra_validation': '0',
            },
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.json,
            {
                'errors': [
                    {
                        'code': 'not_enough_staffers',
                        'description': _(
                            'You won\'t have enough staffers to perform some of the parallel '
                            'combo services.'
                        ),
                    },
                ],
            },
        )

        url = self.url(staffers[1].id)
        response = self.fetch(
            url,
            method='DELETE',
            args={
                'skip_extra_validation': '1',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)


def _sort_key(dct):
    return dct['description']
