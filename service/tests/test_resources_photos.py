import pytest

from service.tests import BaseAsyncHTTPTest, get_request


class ResourcePhotoHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/resources/{}/photo/'

    @pytest.mark.django_db
    def test_upload_without_file(self):
        url = self.url.format(self.owner.id)
        body = {
            'category': 'logo',
            'media': 20,
        }
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400
        field = response.json['errors'][0]
        assert field['description'] == 'This field may not be null.'
        assert field['field'] == 'photo'

    @pytest.mark.django_db
    def test_upload_photo(self):
        url = self.url.format(self.owner.id)
        request = get_request(url)
        self.content_type = request.headers.get('Content-Type')
        response = self.fetch(url, method='PUT', body=request.body)
        assert response.code == 201
        assert 'img.booksy.pm' in response.json['url']

    @pytest.mark.django_db
    def test_upload_invalid_photo(self):
        url = self.url.format(self.owner.id)
        request = get_request(url, valid=False)
        self.content_type = request.headers.get('Content-Type')
        response = self.fetch(url, method='PUT', body=request.body)
        assert response.code == 400
        assert (
            response.json['errors'][0]['description'] == 'Upload a valid image. '
            'The file you uploaded was either not an image or '
            'a corrupted image.'
        )


class UploadPhotoHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/resources/photo/'

    @pytest.mark.django_db
    def test_upload_photo(self):
        url = self.url
        request = get_request(url)
        self.content_type = request.headers.get('Content-Type')
        response = self.fetch(url, method='POST', body=request.body)
        assert response.code == 201
        assert 'img.booksy.pm' in response.json['url']
