from rest_framework import serializers

from lib.fields.phone_number import BooksyCellPhoneBaseField, EvoxPhoneNumberField
from service.notification.enums import EvoxWebhookEventTypes


class TwilioWebhookSerializer(serializers.Serializer):
    """
    Twilio webhook payload deserializer.
    """

    resource_sid = serializers.CharField()
    service_sid = serializers.CharField(allow_null=True)
    error_code = serializers.IntegerField()


class IterableWebhookSerializer(serializers.Serializer):
    """
    Iterable webhook payload deserializer.
    """

    email = serializers.CharField()
    eventName = serializers.CharField(allow_null=True)
    dataFields = serializers.DictField()


class TelnyxErrorSourceSerializer(serializers.Serializer):
    pointer = serializers.CharField(required=False)
    parameter = serializers.CharField(required=False)


class TelnyxErrorSerializer(serializers.Serializer):
    code = serializers.Char<PERSON>ield()
    title = serializers.Char<PERSON>ield()
    detail = serializers.CharField(required=False)
    source = TelnyxErrorSourceSerializer(required=False)
    meta = serializers.DictField(child=serializers.CharField(), required=False)


class TelnyxWebhookSMSSerializer(serializers.Serializer):
    phone_number = BooksyCellPhoneBaseField()
    status = serializers.CharField()


class TelnyxWebhookPayloadSerializer(serializers.Serializer):
    webhook_url = serializers.URLField
    to = TelnyxWebhookSMSSerializer(many=True)
    errors = TelnyxErrorSerializer(many=True)
    messaging_profile_id = serializers.UUIDField(required=False)
    organization_id = serializers.UUIDField(required=False)


class TelnyxWebhookDataSerializer(serializers.Serializer):
    event_type = serializers.CharField()
    payload = TelnyxWebhookPayloadSerializer()


class TelnyxWebhookSerializer(serializers.Serializer):
    """
    Telnyx webhook payload deserializer.
    """

    data = TelnyxWebhookDataSerializer()


class EvoxWebhookSerializer(serializers.Serializer):
    sender_country_code = serializers.CharField(required=True)
    sender = serializers.CharField(required=True)
    phone_number = EvoxPhoneNumberField()
    event_type = serializers.ChoiceField(choices=EvoxWebhookEventTypes.choices())
