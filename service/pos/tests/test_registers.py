import pytest
from model_bakery import baker
from rest_framework import status

from lib.test_utils import create_subbooking, increase_appointment_next_id
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import AppointmentTypeSM
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.enums.receipt_status import STATUS_TYPE__SUCCESS
from webapps.pos.models import POS, PaymentType, Transaction, Receipt
from webapps.register.models import Register, RegisterOperation


@pytest.mark.django_db
class RegistersHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/pos/registers/'

    def setUp(self):
        super().setUp()

        self.pos = baker.make(POS, business=self.business)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CASH)

    def test_get(self):
        open_register = baker.make(
            Register,
            pos=self.pos,
            is_open=True,
            opened_by=self.user,
        )
        closed_register = baker.make(
            Register,
            pos=self.pos,
            is_open=False,
            opened_by=self.user,
            closed_by=self.user,
        )

        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [r['id'] for r in resp.json['registers']]
        assert open_register.id in ids
        assert closed_register.id in ids

        resp = self.fetch(url + '?only_open=true', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [r['id'] for r in resp.json['registers']]
        assert open_register.id in ids
        assert closed_register.id not in ids

        resp = self.fetch(url + '?only_open=1', method='GET')
        assert resp.code == status.HTTP_200_OK
        ids = [r['id'] for r in resp.json['registers']]
        assert open_register.id in ids
        assert closed_register.id not in ids


@pytest.mark.django_db
class RegisterOperationsHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}' '/pos/registers/{register_id}/operations/'

    def test_get(self):
        increase_appointment_next_id()
        pos = baker.make(POS, business=self.business)
        payment_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        register = baker.make(
            Register,
            pos=pos,
            is_open=True,
            opened_by=self.user,
        )
        url = self.url.format(business_id=self.business.id, register_id=register.id)

        # check empty response
        resp = self.fetch(url)
        assert resp.code == 200
        assert resp.json == {'page': 1, 'per_page': 20, 'count': 0, 'operations': []}

        # create one register operations with appointment
        booking, *_ = create_subbooking(business=self.business)
        transaction = baker.make(
            Transaction,
            pos=pos,
            appointment_id=booking.appointment_id,
        )
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            status_code=receipt_status.PAYMENT_SUCCESS,
        )
        baker.make(
            RegisterOperation,
            register=register,
            payment_type=payment_type,
            operator=self.user,
            receipt=receipt,
        )

        resp = self.fetch(url)

        assert resp.code == 200
        assert len(resp.json['operations']) == 1
        operation = resp.json['operations'][0]
        assert set(operation.keys()).issuperset(
            {
                'id',
                'amount',
                'created',
                'payment_type_code',
                'customer',
                'appointment',
                'transaction',
                'note',
                'operator',
                'type',
                'type_display',
                'split_number',
                'cash_register_document',
            }
        )
        assert operation['receipt_status_type'] == 'Paid'
        assert operation['receipt_status_type_code'] == STATUS_TYPE__SUCCESS
        assert operation['appointment'] == {
            'appointment_id': booking.appointment_id,
            'appointment_type': AppointmentTypeSM.MULTI,
            'appointment_uid': booking.appointment_id,
        }
