from datetime import datetime
from decimal import Decimal

import pytest
from django.test import override_settings
from model_bakery import baker
from rest_framework import status

from country_config.enums import Country
from service.tests import BaseAsyncHTTPTest
from webapps.business.models import Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
    POS,
    Receipt,
    Transaction,
    TransactionRow,
    TransactionTip,
)
from webapps.printer_api.enums.printer_config import PrinterProtocol
from webapps.printer_api.enums.printout_status import PrinterStatusEnum
from webapps.printer_api.models import (
    PrinterConfig,
    Printout,
    PrintoutLog,
)
from webapps.printer_api.serializers import (
    PrintoutReceiptSerializer,
    PrintoutSerializer,
)
from webapps.user.models import User


# pylint: disable=too-many-instance-attributes
@pytest.mark.django_db
class BusinessTransactionPrintoutTestCase(BaseAsyncHTTPTest):
    url = (
        '/business_api/me/businesses/{business_id}'
        '/pos/transactions/{transaction_id}/print_receipt/'
    )

    def setUp(self):  # pylint: disable=W0221
        super().setUp()  # pylint: disable=W0221
        baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            name='Some staffer',
        )
        self.pos = baker.make(POS, business=self.business, active=True)
        self.payment_type = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.CASH, default=True
        )
        self.transaction = baker.make(Transaction, pos=self.pos, total=Decimal('10'))
        self.transaction_row = baker.make(
            TransactionRow,
            transaction=self.transaction,
            name_line_1='First item name',
            quantity=2,
            item_price=Decimal('10'),
            tax_rate=Decimal('23.00'),
            tax_type=POS.POS_TAX_MODE__INCLUDED,
        )
        self.receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            payment_type=self.payment_type,
            receipt_number='AAA/123',
            status_code=receipt_status.PAYMENT_SUCCESS,
        )
        self.payment_row = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_type,
            amount=Decimal('10'),
        )
        self.transaction.latest_receipt = self.receipt
        self.transaction.save()

        PrinterConfig.objects.create(
            business=self.business, protocol=PrinterProtocol.FILE, port='Com1'
        )

    def test_printout_set(self):
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_200_OK
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is not None
        assert printout.business == self.business
        assert printout.status_code == PrinterStatusEnum.IN_PRINT
        printout_log = PrintoutLog.objects.filter(printout=printout.id).first()
        assert printout_log is not None
        assert printout_log.status_code == printout.status_code

        # duplicate request
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_200_OK
        assert Printout.objects.filter(transaction=self.transaction).count() == 1

    def test_printout_serializer_printout_success(self):
        serializer = PrintoutSerializer(
            data={
                'business': self.business.id,
                'transaction': self.transaction.id,
                'operator': self.user.id,
            }
        )
        assert serializer.is_valid()
        serializer.save()

        printout = Printout.objects.get(transaction=self.transaction)
        serializer_instance = PrintoutSerializer(
            instance=printout,
            data={
                'status_code': 'S',
                'printer_response': 'printer_response',
                'receipt_no': '12/12',
            },
            partial=True,
        )
        assert not serializer_instance.is_valid()

        serializer_instance = PrintoutSerializer(
            instance=printout,
            data={
                'status_code': 'S',
                'printer_response': 'printer_response',
                'receipt_no': '12/12',
            },
            partial=True,
        )
        assert not serializer_instance.is_valid()

        serializer_instance = PrintoutSerializer(
            instance=printout,
            data={
                'status_code': 'S',
                'printer_response': 'printer_response',
                'receipt_no': '12/12',
                'receipt_date': datetime.now(),
            },
            partial=True,
        )
        assert serializer_instance.is_valid()
        serializer_instance.save()
        refreshed_printout = Printout.objects.filter(
            transaction=self.transaction
        ).first()  # refresh db object
        assert refreshed_printout is None

    def test_printout_serializer_printout_failure(self):
        serializer = PrintoutSerializer(
            data={
                'business': self.business.id,
                'transaction': self.transaction.id,
                'operator': self.user.id,
            }
        )
        assert serializer.is_valid()
        serializer.save()

        printout = Printout.objects.get(transaction=self.transaction)
        serializer_instance = PrintoutSerializer(
            instance=printout,
            data={'status_code': 'F', 'printer_response': 'printer_response'},
            partial=True,
        )
        assert serializer_instance.is_valid()
        serializer_instance.save()
        refreshed_printout = Printout.objects.filter(
            transaction=self.transaction
        ).first()  # refresh db object
        assert refreshed_printout is None

    def test_transaction_zero_total_value(self):
        self.transaction.total = Decimal('0')
        self.transaction.save()
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_transaction_zero_subtotal_value(self):
        self.transaction.total = Decimal('30')
        self.transaction.subtotal = Decimal('0')
        self.transaction.save()

        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_transaction_with_tip(self):
        baker.make(
            TransactionTip,
            transaction=self.transaction,
            amount=Decimal('3'),
        )
        self.transaction.total = Decimal('13')
        self.payment_row.amount = Decimal('13')

        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_200_OK

    def test_transaction_with_tip_too_big_too_fail(self):
        baker.make(
            TransactionTip,
            transaction=self.transaction,
            amount=Decimal('11'),
        )
        self.transaction.total = Decimal('21')
        self.payment_row.amount = Decimal('21')

        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_transaction_with_tip_and_split_payment(self):
        self.payment_type_2 = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.CREDIT_CARD,
            default=True,
        )
        self.payment_row_2 = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_type_2,
            amount=Decimal('10'),
        )
        baker.make(
            TransactionTip,
            transaction=self.transaction,
            amount=Decimal('3'),
        )
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_paid_with_non_valid_payment_type(self):
        self.cheque = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.CHEQUE, default=True
        )
        baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.cheque,
            amount=Decimal('10'),
        )
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'
        assert resp.json['errors'][0]['description'] == ('Payment type cannot be fiscalized')

    def test_transaction_not_finalized(self):
        transaction = baker.make(Transaction, pos=self.pos, total=Decimal('10'))
        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            payment_type=self.payment_type,
            receipt_number='AAA/125',
            status_code=receipt_status.CALL_FOR_PAYMENT,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json.get('errors')
        assert resp.json['errors'][0]['code'] == 'invalid'

    def test_transaction_wrong_receipt_details(self):
        self.transaction.total = Decimal('0')
        self.transaction.save()
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_400_BAD_REQUEST
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is None
        assert resp.json['errors']

    def test_transaction_0_item_price(self):
        self.transaction_row = baker.make(
            TransactionRow,
            transaction=self.transaction,
            name_line_1='Second item name',
            quantity=2,
            item_price=Decimal('0'),
            tax_rate=Decimal('23.00'),
            tax_type=POS.POS_TAX_MODE__INCLUDED,
        )
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_200_OK
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is not None

    def test_transaction_voucher_payment(self):
        self.payment_type2 = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.MEMBERSHIP, default=True
        )
        self.payment_row = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_type2,
            amount=Decimal('0'),
        )
        url = self.url.format(
            business_id=self.business.id,
            transaction_id=self.transaction.id,
        )
        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_200_OK
        printout = Printout.objects.filter(transaction=self.transaction).first()
        assert printout is not None

    def test_no_printer_config(self):
        PrinterConfig.objects.all().delete()
        serializer = PrintoutSerializer(
            data={
                'business': self.business.id,
                'transaction': self.transaction.id,
                'operator': self.user.id,
            }
        )
        with override_settings(API_COUNTRY=Country.PL):
            assert not serializer.is_valid()
            assert serializer.errors['non_field_errors'][0].code == 'invalid'
            assert (
                serializer.errors['non_field_errors'][0]
                == "Your fiscal printer is not set up yet. Please, contact "
                "Customer Success by using the 'HELP' button."
            )

        with override_settings(API_COUNTRY=Country.BR):
            assert not serializer.is_valid()

    def test_printout_with_too_big_tip_failure(self):
        baker.make(
            TransactionTip,
            transaction=self.transaction,
            amount=1000,
        )
        self.transaction.total = Decimal('1010')
        self.transaction.save()
        serializer = PrintoutSerializer(
            data={
                'business': self.business.id,
                'transaction': self.transaction.id,
                'operator': self.user.id,
            }
        )
        assert not serializer.is_valid()
        assert (
            serializer.errors['non_field_errors'][0]
            == 'Receipt contains disabled tip configuration'
        )


# pylint: disable=too-many-instance-attributes
@pytest.mark.django_db
class PrintoutReceiptSerializerTest(BaseAsyncHTTPTest):
    def setUp(self):  # pylint: disable=W0221
        super().setUp()  # pylint: disable=W0221
        baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            name='Some staffer',
        )
        self.pos = baker.make(POS, business=self.business, active=True)

        self.transaction = baker.make(Transaction, pos=self.pos, total=Decimal('100'))
        self.transaction_row1 = baker.make(
            TransactionRow,
            transaction=self.transaction,
            name_line_1='First item name',
            quantity=1,
            item_price=Decimal('100'),
            tax_rate=Decimal('23.00'),
            total=Decimal('100'),
            tax_type=POS.POS_TAX_MODE__INCLUDED,
        )
        self.transaction_row2 = baker.make(
            TransactionRow,
            transaction=self.transaction,
            name_line_1='Package item',
            quantity=1,
            item_price=Decimal('0'),
            tax_rate=Decimal('23.00'),
            total=Decimal('0'),
            tax_type=POS.POS_TAX_MODE__INCLUDED,
        )
        self.transaction_row3 = baker.make(
            TransactionRow,
            transaction=self.transaction,
            name_line_1='Membership item',
            quantity=1,
            item_price=Decimal('0'),
            total=Decimal('0'),
            tax_rate=Decimal('23.00'),
            tax_type=POS.POS_TAX_MODE__INCLUDED,
        )
        self.receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            receipt_number='AAA/123',
            status_code=receipt_status.PAYMENT_SUCCESS,
        )
        self.transaction.latest_receipt = self.receipt
        self.transaction.save()

        PrinterConfig.objects.create(
            business=self.business, protocol=PrinterProtocol.FILE, port='Com1'
        )

    def make_split_payments(self):
        self.payment_cash = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.CASH, default=True
        )
        self.payment_credit = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.CREDIT_CARD,
            default=True,
        )
        self.payment_package = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.PACKAGE, default=True
        )
        self.payment_egift = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.EGIFT_CARD, default=True
        )
        self.payment_membership = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.MEMBERSHIP, default=True
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_cash,
            amount=Decimal('40'),
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_credit,
            amount=Decimal('50'),
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_package,
            amount=Decimal('0'),
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_egift,
            amount=Decimal('10'),
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_membership,
            amount=Decimal('0'),
        )

    def test_serializer(self):
        self.make_split_payments()
        serializer = PrintoutSerializer(
            data={
                'business': self.business.id,
                'transaction': self.transaction.id,
                'operator': self.user.id,
            }
        )
        assert serializer.is_valid()
        serializer.save()

    def test_printout(self):
        self.make_split_payments()
        printout = PrintoutReceiptSerializer(instance=self.receipt)
        data = printout.data
        assert data['global_discount']['discount_amount'] == Decimal('10')
        assert len(data['lines']) == 1
        assert data['lines'][0]['name'] == self.transaction_row1.name_line_1
        assert data['lines'][0]['item_price'] == '100.00'
        assert data['lines'][0]['type'] == 'S'
        assert len(data['payment_lines']) == 2
        assert data['payment_lines'][0]['payment_type_code'] == 'cash'
        assert data['payment_lines'][0]['amount'] == Decimal('40.00')
        assert data['payment_lines'][1]['payment_type_code'] == 'credit_card'
        assert data['payment_lines'][1]['amount'] == Decimal('50.00')

    def test_tax_id(self):
        bci_user = baker.make(
            User,
            cell_phone='+***********',
        )
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=bci_user,
            tax_id='***********',
        )
        self.transaction.customer_card = bci
        printout = PrintoutReceiptSerializer(instance=self.receipt)
        assert printout.data['tax_id'] == '***********'

    def test_printout_with_tip(self):
        baker.make(
            TransactionTip,
            transaction=self.transaction,
            amount=10,
        )
        self.payment_cash = baker.make(
            PaymentType, pos=self.pos, code=PaymentTypeEnum.CASH, default=True
        )
        self.payment_rows = baker.make(
            PaymentRow,
            receipt=self.receipt,
            payment_type=self.payment_cash,
            amount=Decimal('110'),
        )
        printout = PrintoutReceiptSerializer(instance=self.receipt)
        assert len(printout.data['payment_lines']) == 1
        assert printout.data['payment_lines'][0]['amount'] == Decimal('100.00')
