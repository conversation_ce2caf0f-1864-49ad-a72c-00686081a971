from lib.segment_analytics.utils import get_booking_count
from service.tools import (
    json_request,
    RequestHandler,
    session,
)
from webapps.segment.models import SegmentCounter


class BookingCountersHandler(RequestHandler):
    @json_request
    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Business booking counters.
            parameters:
              - name: business_id
                description: Business ID
                type: integer
                paramType: path
            type: BookingCountersResponse

        :swagger

        swaggerModels:
            BookingCountersResponse:
                id: BookingCountersResponse
                properties:
                    total_booking_count:
                        type: integer
                    bb_booking_count:
                        type: integer
                    cb_booking_count:
                        type: integer
                    cb_google_booking_count:
                        type: integer
        :swaggerModels
        """

        business = self.business_with_staffer(business_id)
        booking_counts = get_booking_count(business)

        ret = {
            'total_booking_count': booking_counts.all,
            'bb_booking_count': booking_counts.bb,
            'cb_booking_count': booking_counts.cb,
            'cb_google_booking_count': booking_counts.cb_google,
        }
        self.finish_with_json(200, ret)


class CountersHandler(RequestHandler):

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Increment counters.
            parameters:
              - name: business_id
                description: Business ID
                type: integer
                paramType: path
              - name: body
                paramType: body
                type: CountersRequest
        :swagger

        swaggerModels:
            CountersRequest:
                id: CountersRequest
                required:
                  - bump_counters
                properties:
                    bump_counters:
                        type: array
                        items:
                          type: string
                        description: >
                          list of counters to increment. Available:
                          referralLinkCopiedCount,
                          referralShareButtonClickedCount,
                          referralSharedCount,
                          referralSharedCountFacebook,
                          referralSharedCountTwitter,
                          referralSharedCountWhatsapp,
                          referralSharedCountMessenger,
                          referralSharedCountTextMessage,
                          referralSharedCountEmail,
        :swaggerModels
        """
        self.business_with_staffer(business_id)
        counters = []
        try:
            for counter_name in self.data['bump_counters']:
                counters.append(SegmentCounter.bump(business_id, counter_name))
            self.set_status(201)
        except KeyError:
            self.set_status(400)

        self.finish()
