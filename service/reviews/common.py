from typing import Optional

from django.db.models import (
    Case,
    F,
    Func,
    IntegerField,
    Prefetch,
    Q,
    QuerySet,
    Value,
    When,
)
from django.db.models import Float<PERSON>ield
from django.db.models.expressions import RawSQL
from django.db.models.functions import Cast, Length

from lib.tools import tznow
from service.business.const import MAX_REVIEW_LENGTH, MAX_REVIEW_SCORE
from service.business.enums import BUSINESS_REVIEWS_ORDER_BY, ReviewOrderEnum
from service.mixins.paginator import PaginatorMixin
from service.reviews.responses import ReviewsInnerResponse
from service.tools import RequestHandler
from webapps.reviews.models import ReviewPhoto


class ReviewsHandler(PaginatorMixin, RequestHandler):
    page_name = 'reviews_page'
    per_page_name = 'reviews_per_page'
    default_per_page_setting = 'REVIEWS_PER_PAGE'

    def _get_sorted_business_reviews(
        self,
        business_id: int,
        order_by: Optional[tuple] = None,
        service_id: Optional[int] = None,
    ) -> ReviewsInnerResponse:
        self.parse_page_values_from_get()

        business = self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
            active=True,
        )

        is_b_listing_with_source = False
        source = business.get_source()
        if source:
            is_b_listing_with_source = True
            business = source

        reviews_rank = self.request.arguments.get('reviews_rank', [''])[0]
        reviews = self.get_business_sorted_reviews(
            business=business,
            service_id=service_id,
            reviews_rank=reviews_rank,
            order_by=order_by,
        )
        reviews_offset_n_limit = reviews[self.offset : self.limit]

        if reviews_rank:
            reviews_count = reviews.count()
        else:
            reviews_count = business.reviews_count or 0

        # 50023 - need to change reviews business into BListing
        if is_b_listing_with_source:
            for review in reviews_offset_n_limit:
                review.business = business.b_listing
        return ReviewsInnerResponse(
            reviews=reviews_offset_n_limit, reviews_count=reviews_count, business=business
        )

    @staticmethod
    def get_business_sorted_reviews(
        business: 'business.Business',
        algorithm: Optional[str] = None,
        service_id: Optional[int] = None,
        reviews_rank: Optional[int] = None,
        order_by: Optional[tuple] = None,
    ) -> QuerySet:
        """Moved from service.business.reviews.ReviewsPerBusinessMixin"""
        today = tznow()
        filters = {}

        if service_id:
            filters['services__contains'] = [{'id': service_id}]

        if reviews_rank:
            filters['rank'] = reviews_rank

        if algorithm == 'customer':
            reviews = (
                business.reviews.annotate(
                    review_len=Length('review'),
                    elapsed_days=Cast(
                        Func(
                            F('created') - today,
                            function='EXTRACT',
                            template="%(function)s('day' from %(expressions)s)",
                        ),
                        IntegerField(),
                    ),
                )
                .annotate(
                    # This sum is translated to explicit sum:
                    # ((0 + CASE ...) + CASE ...) ...
                    # calculated elementwise for each row.
                    score=sum(
                        [
                            Case(
                                When(
                                    review_len__gt=MAX_REVIEW_LENGTH, then=Value(MAX_REVIEW_SCORE)
                                ),
                                When(review_len=0, then=Value(-250.0)),
                                default=F('review_len') * 0.5,
                                output_field=FloatField(),
                            ),
                            Case(
                                When(rank=5, then=Value(50)),
                                When(rank=1, then=Value(-25)),
                                default=0,
                                output_field=FloatField(),
                            ),
                            Case(
                                When(elapsed_days__gte=-7, then=Value(20)),
                                default=0,
                                output_field=FloatField(),
                            ),
                            Case(
                                When(
                                    Q(review_photos__isnull=False) & Q(rank__in=[1, 4]),
                                    then=Value(100),
                                ),
                                When(Q(review_photos__isnull=False) & Q(rank=5), then=Value(200)),
                                default=0,
                                output_field=FloatField(),
                            ),
                            Cast(
                                F('elapsed_days') * 3,
                                output_field=FloatField(),
                            ),
                        ]
                    ),
                )
                .order_by('-score', '-created')
                .distinct()
            )
        else:
            order_by = order_by or BUSINESS_REVIEWS_ORDER_BY[ReviewOrderEnum.DEFAULT]
            reviews = business.reviews.order_by(
                RawSQL("reviews_review.review <> ''", ()).desc(),  # nosemgrep: avoid-raw-sql
                *order_by,
            )

        reviews = (
            reviews.filter(**filters)
            .select_related(
                'user',
                'subbooking',
            )
            .prefetch_related(
                Prefetch(
                    'review_photos',
                    queryset=ReviewPhoto.objects.all().select_related('photo'),
                ),
            )
        )
        return reviews
