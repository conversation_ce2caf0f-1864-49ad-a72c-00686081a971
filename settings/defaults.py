#!/usr/bin/env python
"""Default values to all Booksy API settings."""
import datetime
from decimal import Decimal
from itertools import chain

from country_config.enums import Country
from settings import API_DJANGO_ADMIN
from webapps.pos.enums import POSPlanPaymentTypeEnum
from webapps.booksy_pay.enums import BooksyPayCashbackType

REINDEX_FROM_MASTER = True
SERVICE_NAME = 'core'


###############################
########### SEARCH ############
###############################

DAYTIME_THRESHOLDS = {
    'morning': ['0:00', '12:00'],
    'afternoon': ['12:00', '17:00'],
    'evening': ['17:00', '23:59'],
}
MAX_HINTS_COUNT = 20
DEFAULT_SORT_ORDER = 'score'
SEARCH_MOD__WIDEN_SEARCH = True

BUSINESSES_PER_PAGE = 30
BUSINESS_DASHBOARD_BOOKINGS_PER_PAGE = 20
BUSINESS_LIST_RESULTS_PER_PAGE = 20
MAX_BUSINESSES_PER_PAGE = 100
BUSINESS_CUSTOMER_INFOS_PER_PAGE = 20
BUSINESS_CUSTOMER_INFOS_THIN_PER_PAGE = 1000
BUSINESS_CUSTOMER_BOOKINGS_PER_PAGE = 20
CATEGORIES_PER_PAGE = 50
CUSTOMER_BOOKINGS_PER_PAGE = 20
CUSTOMER_BOOKMARKS_PER_PAGE = 20
DASHBOARD_RECENT_SEARCHES_PER_PAGE = 20
LOCATION_HINTS_PER_PAGE = 20
QUERY_HINTS_PER_PAGE = 20
RESOURCES_PER_PAGE = 500
CALENDAR_RESOURCES_PER_PAGE = 100  # backward compat, use less in new apps
REVIEWS_PER_PAGE = 20
PER_PAGE = 20

SORT_ORDER_CHOICES = [
    'distance',
    'top_rated',
    'score',
    'name',
    'rating',
    'price',
    'newest',
    'popularity',
    'by_ids',
]
EXTBIZ_SORT_ORDER_CHOICES = ['name', 'newest', 'distance', 'score']
DASHBOARD_BOOKING_FILTERS = [
    'customer_bookings',
    'business_bookings',
    'archived',
    'active',
    'finished',
    'all',
]
BRAZIL_PHONE_COMPLETION = True

###############################
########### PHOTOS ############
###############################

PHOTO_THUMBNAIL_SIZES = [
    (306, 189),  # marketplace
    (250, 250),
    (100, 100),
    (50, 50),
]
EXTERNAL_BIZ_PHOTO_THUMBNAIL_SIZES = PHOTO_THUMBNAIL_SIZES + [
    (640, 427),
    (750, 500),
]
EXTERNAL_BIZ_PHOTO_THUMBNAIL_SIZES = {
    f'{x},{y}': {'size': (x, y)} for x, y in EXTERNAL_BIZ_PHOTO_THUMBNAIL_SIZES
}

###############################
####### SCENARIOS #######
###############################

SCENARIOS_GLOBAL_ENABLE_EMAIL = True
SCENARIOS_GLOBAL_ENABLE_PUSH = True
SCENARIOS_GLOBAL_ENABLE_SMS = True


# There are 2 ways of starting Scenario:
# 1) Adding it to NotificationSchedule and wainting for CeleryBeat to start it
#    in Celery
# 2) Sending task instantly to Celery (faster sending, no races, smaller load
#    on DB)
# Sadly 2) for some reason does not work all times (Celery problems?) so must
#          be disabled until solution for this will be found
SCENARIO_INSTANT_TASK = False


SCENARIOS_TRUNCATE_BUSINESS_NAME = {
    'total_length': 30,
    'subpart_length': None,
    'subpart_suffix': "",
}

EMAIL_CONFIRMATION_CODE_LIFETIME_MINUTES = 60

# characters is very important, reasons:
# - we omit characters difficult to distinguish from each other: "Il1" or "O0"
# - we use digits (common practice in banks and so on)
# - length MUST BE 4, otherwise the world will explode
SMS_REGISTRATION_CODE = {
    'characters': '********',
    'length': 4,
}
INTERNAL_SMS_CODE = '****'
# If registration SMSes will be sent in faster manner
# This costs 50% more but probably is worthy
SMS_REGISTRATION_FAST = True
# For how many seconds SMS code is:
# - valid (== can be used in creation of account)
# - limited by  SMS_REGISTRATION_LIMIT
SMS_REGISTRATION_PERIOD = 60 * 60
SMS_REGISTRATION_LIMIT = 4
# How long wait (in seconds) for SMS delivery
# If user asks for new code before this time has elapsed,
# then will get "Please wait. Text is on it's way" error
SMS_REGISTRATION_WAIT = 1 * 60
# Counteracting abuse attempts
# How many (ABUSE_LIMIT) SMSes we can send per device in ABUSE_PERIOD seconds
# Device is identified by X-FINGERPRINT header
# (and in close future also by IP address)
SMS_REGISTRATION_ABUSE_PERIOD = 6 * 60 * 60
SMS_REGISTRATION_ABUSE_LIMIT = 4
# This will be returned by GET /routing/config/
# later on (when every client will support sms_code)
# thil will be used in endpoint to make sms_code required field
SMS_REGISTRATION_CODE_REQUIRED = True
SMS_REGISTRATION_BLACKLIST = [
    '+48 575 907 457',
    '+48 517 190 097',
    '+48 609 905 420',
    '+48 698 143 578',
    '+34 645 57 02 50',  # #62866
    '+48 730 312 621',
]

STAFFER_LOCKED_LIMIT_HOURLY = 18
STAFFER_LOCKED_ACCESS_PER_PAGE = 100

# Borrowed from: https://en.wikipedia.org/wiki/Quotation_mark
QUOTATION_MARKS = {
    'da': ['»', '«'],
    'en': ['“', '”'],
    'fi': ['”', '”'],
    'fr': ['« ', ' »'],  # yes, spaces inside
    'de': ['„', '“'],
    'it': ['«', '»'],
    'no': ['«', '»'],
    'pl': ['„', '”'],
    'pt': ['“', '”'],
    'ru': ['«', '»'],
    'es': ['«', '»'],
    'uk': ['«', '»'],
    'zh': ['“', '”'],
}


###############################
###### OLD NOTIFICATIONS ######
###############################
NOTIFICATION_DEFINITIONS = {
    'C': {
        'E': {
            "booking_creation": {"default": True, "visible": False},
            "booking_status_change": {"default": True, "visible": True},
        },
        'P': {
            "booking_status_change": {"default": True, "visible": True},
        },
    },
    'B': {
        'E': {
            "booking_creation": {"default": True, "visible": False},
            "biz_booking_status_change": {"default": True, "visible": True},
            "per_region_activate_account": {"default": False, "visible": False},
        },
        'P': {
            "booking_creation": {"default": True, "visible": False},
            "biz_booking_status_change": {"default": True, "visible": True},
        },
    },
}
# For easier error checking later
NOTIFICATION_PROFILE_TYPES = {}
for profile_type, definitions in list(NOTIFICATION_DEFINITIONS.items()):
    for notification_type, definition in list(definitions.items()):
        noti = NOTIFICATION_PROFILE_TYPES.setdefault(notification_type, {})
        for notification_name in list(definition.keys()):
            noti.setdefault(notification_name, []).append(profile_type)


###############################
########### EMAILS ############
###############################

NO_REPLY_EMAIL = '<EMAIL>'
NO_REPLY_NAME = 'Booksy.com'
NO_REPLY_EMAIL_PRETTY = f'"{NO_REPLY_NAME}" <{NO_REPLY_EMAIL}>'
NO_REPLY_MB_NOTIFICATION_EMAIL = '<EMAIL>'
EXTERNAL_BIZ_NOTIFICATIONS_EMAIL = '<EMAIL>'
FRAUD_ALERT_EMAIL = '<EMAIL>'
SERVICES_CONCIERGE_EMAIL = '<EMAIL>'
ALLOW_SENDING_VIA_API = True

TEST_MAIL_SENDER = {}

# if owner shold be added to "Email notifications" during business creation
# and in result by default get all emails about booking changes
ADD_OWNER_TO_NOTIFICATIONS_RECIEVERS = False


###############################
########### LOCALE ############
###############################

LANG_TO_LOCALE_PER_COUNTRY = {
    ### {<language_code>: {'default': <locale>, <country_code>: <locale>}}
    'da': {
        'default': 'da_DK',
        'dk': 'da_DK',  # Denmark
    },
    'en': {
        'default': 'en_US',
        'au': 'en_AU',  # Australia
        #'bz': 'en_BZ',  # Belize
        'ca': 'en_CA',  # Canada
        'hk': 'en_HK',  # Hong Kong
        'in': 'en_IN',  # India
        #'jm': 'en_JM',  # Jamaica
        #'mt': 'en_MT',  # Malta
        'ng': 'en_NG',
        'nz': 'en_NZ',  # New Zealand
        'ph': 'en_PH',  # Philippines
        'sg': 'en_SG',  # Singapore
        'us': 'en_US',  # USA
        'za': 'en_ZA',  # Southern Africa
    },
    'en-gb': {
        'default': 'en_GB',  # Great Britain
        'ie': 'en_IE',  # Ireland
    },
    'zh': {
        'default': 'zh_CN',  # China
        'sg': 'zh_SG',  # Singapore
    },
    'es': {
        'default': 'es_MX',  # Mexico
        'mx': 'es_MX',  # Mexico
        'ar': 'es_AR',  # Argentina
        'bo': 'es_BO',  # Bolivia
        'cl': 'es_CL',  # Chile
        'co': 'es_CO',  # Colombia
        # 'cu': 'es_CU',  # Cuba
        'ec': 'es_EC',  # Ecuador
        'gt': 'es_GT',  # Guatemala
        'hn': 'es_HN',  # Honduras
        'ni': 'es_NI',  # Nicaragua
        'pa': 'es_PA',  # Panama
        'py': 'es_PY',  # Paraguay
        'pe': 'es_PE',  # Peru
        'uy': 'es_UY',  # Uruguay
        've': 'es_VE',  # Venezuela
    },
    'es-es': {
        'default': 'es_ES',
        'es': 'es_ES',  # Spain
    },
    'fi': {
        'default': 'fi_FI',
        'fi': 'fi_FI',  # Finland
    },
    'ms': {
        'default': 'ms_MY',
        'my': 'ms_MY',  # Malaysia
    },
    'nb': {
        'default': 'nb_NO',
        'no': 'nb_NO',  # Norway (Bokmål)
    },
    'pl': {
        'default': 'pl_PL',
        'pl': 'pl_PL',  # Poland
    },
    'sv': {
        'default': 'sv_SE',
        'se': 'sv_SE',  # Sweden
    },
    'pt': {
        'default': 'pt_PT',
        'br': 'pt_BR',  # Brazil
        'ar': 'pt_BR',  # Argentina
        'pt': 'pt_PT',  # Portugal
    },
    'de': {
        'default': 'de_DE',
        'de': 'de_DE',  # Germany
    },
    'fr': {
        'default': 'fr_FR',
        'fr': 'fr_FR',  # France
    },
    'nl': {
        'default': 'nl_NL',
        'nl': 'nl_NL',  # Netherlands
    },
    'it': {
        'default': 'it_IT',
        'it': 'it_IT',  # Italy
    },
    'ru': {
        'default': 'ru_RU',
        'ru': 'ru_RU',  # Russia
    },
    'ja': {
        'default': 'ja_JP',
        'jp': 'ja_JP',  # Japan
    },
    'uk': {
        'default': 'ru_RU',  # fallback to ru_RU until we have UA country supported
        # 'ua': 'uk_UA',  # Ukraine
    },
    'vi': {
        'default': 'en_US',  # fallback to es_US until we have proper Vietnam
        # 'vn': 'vi_VN',  # Vietnam
    },
}
# For now currency locale is our switcher for app locale
_generic_dmy_24h = {
    'long_date_ymwd': 'EEEE, dd MMMM yyyy',
    'long_date_ymd': 'dd MMMM yyyy',
    'long_date_mwd': 'EEEE, dd MMMM',
    'date_ymwd': 'E, dd/MM yyyy',
    'date_ymd': 'dd/MM/yyyy',
    'date_mwd': 'EEE dd MMM',
    'date_md': 'dd MMMM',
    'time_h': 'HH',
    'time_hm': 'HH:mm',
    'time_hms': 'HH:mm:ss',
    'time_format': '24',
}
_sweden = {
    'long_date_ymd': 'dd MMMM yyyy',
    'long_date_ymwd': 'EEEE, dd MMMM yyyy',
    'long_date_mwd': 'EEEE, dd MMMM',
    'date_ymwd': 'E, dd/MM yyyy',
    'date_ymd': 'yyyy-MM-dd',
    'date_mwd': 'EEE dd MMM',
    'date_md': 'dd MMMM',
    'time_h': 'HH',
    'time_hm': 'HH:mm',
    'time_hms': 'HH:mm:ss',
    'time_format': '24',
}
_great_britain = {
    'long_date_ymwd': 'EEEE, dd MMMM yyyy',
    'long_date_ymd': 'dd MMMM yyyy',
    'long_date_mwd': 'EEEE, dd MMMM',
    'date_ymwd': 'E, dd/MM yyyy',
    'date_ymd': 'dd/MM/yyyy',
    'date_mwd': 'EEE dd MMM',
    'date_md': 'dd MMMM',
    'time_h': 'hh a',
    'time_hm': 'hh:mm a',
    'time_hms': 'hh:mm:ss a',
    'time_format': '12',
}
_india = {
    'long_date_ymwd': 'EEEE, dd MMMM yyyy',
    'long_date_ymd': 'dd MMMM yyyy',
    'long_date_mwd': 'EEEE, dd MMMM',
    'date_ymwd': 'E, dd/MM yyyy',
    'date_ymd': 'dd/MM/yyyy',
    'date_mwd': 'EEE dd MMM',
    'date_md': 'dd MMMM',
    'time_h': 'hh a',
    'time_hm': 'hh:mm a',
    'time_hms': 'hh:mm:ss a',
    'time_format': '12',
}
_spain = {
    'long_date_ymwd': "EEEE, dd 'de' MMMM 'de' yyyy",
    'long_date_ymd': "dd 'de' MMMM 'de' yyyy",
    'long_date_mwd': 'EEEE, dd MMMM',
    'date_ymwd': 'E, dd/MM yyyy',
    'date_ymd': 'dd/MM/yyyy',
    'date_mwd': 'EEE dd MMM',
    'date_md': 'dd MMMM',
    'time_h': 'HH',
    'time_hm': 'HH:mm',
    'time_hms': 'HH:mm:ss',
    'time_format': '24',
}
_china = {
    # 'long_date_ymwd': u"yyyy年MMMM日ddddEEEE".encode('utf-8'),
    # 'long_date_ymd': u"yyyy年MMMM日dddd".encode('utf-8'),
    # 'long_date_mwd': u'MMMM日ddddEEEE'.encode('utf-8'),
    # 'date_ymwd': 'yyyy年M日dEEEE',
    # 'date_ymd': 'yyyy年M日d',
    # TODO: ugly 25986-singapore-date-format
    'long_date_ymwd': 'yyyy-MM-dd',
    'long_date_ymd': 'yyyy-MM-dd',
    'long_date_mwd': 'MM-dd',
    'date_ymwd': 'yyyy-MM-dd',
    'date_ymd': 'yyyy-MM-dd',
    'time_h': 'HH',
    'time_hm': 'HH:mm',
    'time_hms': 'HH:mm:ss',
    'time_format': '24',
}

# see ConfigView.locale_config
DATETIME_LOCALE = {
    # This is in Android format
    # http://developer.android.com/reference/java/text/SimpleDateFormat.html
    'default': {
        'long_date_y': 'yyyy',
        'long_date_ym': 'MMMM yyyy',
        'long_date_ymd': 'MMMM dd yyyy',
        'long_date_ymwd': 'EEEE, MMMM dd yyyy',
        'long_date_mwd': 'EEEE, MMMM dd',
        'short_date_md': 'MMM d',
        'date_ymwd': 'E, MMM dd yyyy',
        'date_ymd': 'MM/dd/yyyy',
        'date_mwd': 'EEE, MMM dd',
        'date_ym': 'MMM yyyy',
        'date_md': 'MMMM dd',
        'date_y': 'yyyy',
        'date_m': 'MMM',
        'date_d': 'dd',
        'date_w': 'EEE',
        'time_h': 'hh a',
        'time_hm': 'hh:mm a',
        'time_hms': 'hh:mm:ss a',
        'time_format': '12',
    },
    'en_US': {
        # en_US is default
    },
    'en_PH': {
        # en_US is default and Philippines uses it for now
    },
    'pl_PL': {
        'long_date_ymwd': 'EEEE, dd MMMM yyyy',
        'long_date_ymd': 'dd MMMM yyyy',
        'long_date_mwd': 'EEEE, dd MMMM',
        'date_ymwd': 'E, dd.MM.yyyy',
        'date_ymd': 'dd.MM.yyyy',
        'date_mwd': 'EEE dd MMM',
        'date_md': 'dd MMMM',
        'time_h': 'HH',
        'time_hm': 'HH:mm',
        'time_hms': 'HH:mm:ss',
        'time_format': '24',
    },
    ### SCANDINAVIA ###
    # se - Sweden
    'sv_SE': _sweden,
    # dk - Denmark
    'da_DK': _sweden,
    # fi - Finland
    'fi_FI': _sweden,
    # no - Norway (Bokmål)
    'nb_NO': _sweden,
    # COMMONWEALTH
    # gb - Great Britain
    'en_GB': _great_britain,
    # au - Australia
    'en_AU': _great_britain,
    # bz - Belize
    #'en_BZ': _great_britain,  # no such locale
    # ca - Canada
    'en_CA': dict(
        chain(
            list(_great_britain.items()),
            list(
                {
                    'date_ymd': 'yyyy-MM-dd',
                }.items()
            ),
        )
    ),
    # Hong Kong
    'en_HK': _great_britain,
    # ie - Ireland
    'en_IE': _great_britain,
    # in - India
    'en_IN': _india,
    # jm - Jamaica
    #'en_JM': _great_britain,  # no such locale
    # mt - Malta
    #'en_MT': _great_britain,  # no such locale
    # my - Malaysia
    'ms_MY': _great_britain,
    # ng - Nigeria
    'en_NG': _great_britain,
    # nz - New Zealand
    'en_NZ': _great_britain,
    # sg - Singapore
    'en_SG': _great_britain,
    # za - Southern Africa
    'en_ZA': _great_britain,
    # LATINO & PENINSULARES
    # es - Spain
    'es_ES': _spain,
    # ar - Argentina
    'es_AR': dict(
        chain(
            list(_spain.items()),
            list(
                {
                    # like Spain but 12-hour clock
                    'time_h': 'hh a',
                    'time_hm': 'hh:mm a',
                    'time_hms': 'hh:mm:ss a',
                    'time_format': '12',
                }.items()
            ),
        )
    ),
    # bo - Bolivia
    'es_BO': _spain,
    # cl - Chile
    'es_CL': _spain,
    # co - Colombia
    'es_CO': _spain,
    # cr - Costa Rica
    'es_CR': _spain,
    # cu - Cuba
    #'es_CU': _spain,  # no such locale
    # ec - Ecuador
    'es_EC': _spain,
    # sv - El Salvador
    'es_SV': _spain,
    # gt - Guatemala
    'es_GT': _spain,
    # hn - Honduras
    'es_HN': _spain,
    # mx - Mexico
    'es_MX': _spain,
    # ni - Nicaragua
    'es_NI': _spain,
    # pa - Panama
    'es_PA': _spain,
    # pe - Peru
    'es_PE': _spain,
    # py - Paraguay
    'es_PY': _spain,
    # uy - Uruguay
    'es_UY': _spain,
    # ve - Venezuela
    'es_VE': _spain,
    # 2015-07 update
    # br - Brasil
    'pt_BR': _generic_dmy_24h,
    # de - Germany
    'de_DE': _generic_dmy_24h,
    # fr - France
    'fr_FR': _generic_dmy_24h,
    # nl - Netherlands
    'nl_NL': _generic_dmy_24h,
    # it - Italy
    'it_IT': _generic_dmy_24h,
    # pt - Portugal
    'pt_PT': _generic_dmy_24h,
    # ru - Russia
    'ru_RU': _generic_dmy_24h,
    # jp - Japan
    'ja_JP': _generic_dmy_24h,
    # 2016-03 update
    # cn - China
    'zh_CN': _china,
    # sg - Singapore
    'zh_SG': _china,
}
# API RESPONSE DATETIME FORMATS
DATETIME_FORMAT = '%Y-%m-%dT%H:%M'
DATETIME_SECONDS_FORMAT = '%Y-%m-%dT%H:%M:%S'
DATE_FORMAT = '%Y-%m-%d'
TIME_FORMAT = '%H:%M'

#############################################
######### TRIALS AND STATUS FLOW ############
#############################################

STATUS_FLOW__TRIAL_DURATION = 7
STATUS_FLOW__TRIAL_EXTENSION = 7
STATUS_FLOW__BLOCKED_OVERDUE_AFTER_DAYS = 14
STATUS_FLOW__ACTIVE_TRIAL_BLOCKED_DAYS = 30
STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED = False

# Maximum days business can extend trial period
MAX_TRIAL_EXTENSION_DURATION = 14
TRIAL_EXTENSION_MIN_DAYS_AFTER_BUSINESS_ACTIVATION = 28
TRIAL_EXTENSION_MAX_DAYS_AFTER_BUSINESS_ACTIVATION = 90

# Number of days/hours etc remaining to next billing cycle when we cancel
# past due subscriptions
CHURN__CANCEL_SUBSCRIPTIONS_BEFORE = datetime.timedelta(days=2)

###############################
######### EXT. APIs ###########
###############################

PAYPAL_NVP_API_URL = "https://api-3t.sandbox.paypal.com/nvp"
# pylint: disable=line-too-long
PAYPAL_CHECKOUT_URL = "https://www.sandbox.paypal.com/cgi-bin/webscr?cmd=_express-checkout&token="
# pylint: enable=line-too-long
GOOGLE_API_KEY = 'AIzaSyDfEFUPv0wDMRkETJHl43K_wH7cRy-Mug8'
GOOGLE_PLACES_API_KEY = ''  # by default do not use a key

HERE_APP_ID = 'Wpxscr9RTmH2pUtehslO'
HERE_APP_CODE = 'l2UcRwfznnRzPLkOr2mSBQ'

# v7 ie. v1 authorization
HERE_API_KEY = 'y6u6_uhefcqqpHVRPHVEoW7m60hdlymjcfBR9gpgmBQ'
HERE_API_KEY_APP_ID = 'ZhtGJluwBO1H6pWbHkVZ'

###############################
########### SYSTEM ############
###############################

SERVICE_PORT = 9093

###############################
########## SERVICES ###########
###############################

SERVICE_VARIANT_DEFAULT_INTERVAL = 15

###############################
############ DEBUG ############
###############################

FULL_LOG_ENABLED = True
DEBUG_SWAGGER = False
DEBUG_LOGS = False
TEST_BIZ_NAME_SUFFIX = '#b108#'
CORS__ALLOW_ALL_ORIGINS = False

###############################
########## FEATURES ###########
###############################

POS = True
POS__PAY_BY_APP = False
POS__PREPAYMENTS = False
POS__REFUNDS = False
POS__GOOGLE_PAY = False
POS__APPLE_PAY = False
POS__SQUARE = False
POS__STRIPE_TERMINAL = False
POS__TAP_TO_PAY = False
POS__TAP_TO_PAY_MIN_IOS_VERSION = None  # min iOS version supported/required by Stripe
POS__KEYED_IN_PAYMENT = False
POS__BLIK = False
STRIPE_ALLOW_TIPS_WITHOUT_FEE = False
POS__FAST_PAYOUTS = False
POS__BSX = False
POS__BOOKSY_PAY = False
IAP_ANDROID = True
IAP_IOS = True
IAP_BRAINTREE = True
VOUCHERS_ENABLED = False
B2B_REFERRAL_ENABLED = False
ELEARNING_ENABLED = False
CONSENT_FORM_SMS_REQUEST = False
GROUP_PERMISSIONS_ENABLED = True
CAN_CREATE_UMBRELLA = False
FAMILY_AND_FRIENDS_ENABLED = True

# all values in local currency of country
# counting 1.0 as one
MINIMAL_POS_PAY_BY_APP_PAYMENT = {
    'us': 5.0,
    'pl': 10.0,
    'za': 35.0,
    'default': 5.0,
}


DISABLE_BOOKING_EDIT_AFTER = datetime.timedelta(days=1)
POS__CHARGE_TIME = datetime.timedelta(days=30)
BUSINESS_BOOKING_LOCK_MAX_TIME = 10


###############################
####### APPS VERSIONS #########
###############################

# those values can be overwritten by (higher priority on top):
# - Feature_ForceUpdateAppsOptions
# - in admin panel in view: /settings
# - on prod: yaml config stored: /config/live/*.yaml
# - on some dev: yaml config stored on s3
# - and those settings below
CURRENT_BUSINESS_IOS_VERSION = '4436'
CURRENT_BUSINESS_IOS_FORCE_UPDATE = True
CURRENT_BUSINESS_ANDROID_VERSION = '538'
CURRENT_BUSINESS_ANDROID_FORCE_UPDATE = True
CURRENT_CUSTOMER_IOS_VERSION = '7439'
CURRENT_CUSTOMER_IOS_FORCE_UPDATE = True
CURRENT_CUSTOMER_ANDROID_VERSION = '285'
CURRENT_CUSTOMER_ANDROID_FORCE_UPDATE = False

BUSINESSES_MULTI_CATEGORIES = True

IOS_MIN_SUPPORTED_VERSION = 14
ANDROID_MIN_SUPPORTED_VERSION = 5

API_VERSION = 'live-2023-01-01-abcd1234'

### Payment Push #47461
UNVERIFIED_PUSH_PAYMENTS = True
NOSHOW_MAIL = False
NOSHOW_FEATURE_EDU_MAIL = False


###############################
########## DEEPLINKS ##########
###############################

COUNTRY__SHOW_TRIAL_INFO = {
    'au',
    'dk',
    'fi',
    'fr',
    'es',
    'nl',
    'jp',
    'ca',
    'my',
    'mx',
    'de',
    'pl',
    'pt',
    'za',
    'ru',
    'us',
    've',
    'it',
    'br',
}

COUNTRY__STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED = {
    'pl',
}

#####################################################################
# branch.io keys
# test
BRANCH_IO_KEY_BIZ_DEV = 'key_test_lgdKOeVJYRcN7mPpdCRCefhltypxJGSm'
BRANCH_IO_KEY_CUS_DEV = 'key_test_mjiROmGAD9jA8cIDu9BuwbhizEgwLp5l'
BRANCH_IO_KEY_SAL_DEV = 'key_test_jbM0P4MDs7QFq2h6lEzcbnepEzh8IrIC'
# live (always to use, including booksy alfas)
BRANCH_IO_KEY_BIZ_PRD = 'key_live_kfpGPmGR5RfM1pQooAvZIgpgCshwQGv9'
BRANCH_IO_KEY_CUS_PRD = 'key_live_fkbTPjJsy6dB1cSDx7qFEaeauFgwGlPX'
BRANCH_IO_KEY_SAL_PRD = 'key_live_eoGYTZMzy3PqDZaXgFyijjadDDh6IxGV'
BRANCH_IO_SECRET_BIZ = 'secret_live_KzSKHZDFuNWDGEZi9KZf32F0BYFwBtyH'
BRANCH_IO_SECRET_CUS = 'secret_live_AZ1JFOtKfi24L6TaMPVZ0X1KcHr5jjvn'
BRANCH_IO_SECRET_SAL = 'secret_live_IdDDrSPeb0Wi08PrFjf5O97ElFOJr5Jm'

# pylint: disable=line-too-long
APPS_MARKET_URLS = {
    'biz': {
        'iphone': 'https://itunes.apple.com/us/app/booksybiz-smarter-way-to-find/id725335996',  # NOQA
        'android': 'https://play.google.com/store/apps/details?id=net.booksy.business',
    },
    'customer': {
        'iphone': 'https://itunes.apple.com/us/app/booksy-smarter-way-to-find/id723961236',
        'android': 'https://play.google.com/store/apps/details?id=net.booksy.customer',
    },
}
# pylint: enable=line-too-long

###############################
############ POS ##############
###############################

POS__DEFAULT_PAYMENT_PROVIDER = 'fake'
POS__DEFAULT_PAYMENT_PROVIDER_V2 = 'adyen'
MAX_TRANSACTIONS_PL = 500
# We need to maintain legacy POS__PLANS_PER_COUNTRY for 0154_create_posplans migration
POS__PLANS_PER_COUNTRY_LEGACY = {
    'us': [  # lower tiers first. IT'S FOKKEN IMPORTANT. LOWER. FIRST.
        {  # tier A us - flat
            'min_txn_num': 0,
            'provision': 0.0269,  # 2.69%
            'txn_fee': 0.3,
            'fee_currency': 'USD',
            'currency_locale': 'en_US',
        },
    ],
    'pl': [
        {  # tier A pl
            'min_txn_num': 0,
            'provision': 0.0189,  # 1.89%
            'txn_fee': 0.49,
            'fee_currency': 'PLN',
            'currency_locale': 'pl_PL',
        },
        {  # tier B pl
            'min_txn_num': 100,
            'provision': 0.0169,  # 1.69%
            'txn_fee': 0.39,
            'fee_currency': 'PLN',
            'currency_locale': 'pl_PL',
        },
        {  # tier C pl
            'min_txn_num': MAX_TRANSACTIONS_PL,
            'provision': 0.0149,  # 1.49%
            'txn_fee': 0.29,
            'fee_currency': 'PLN',
            'currency_locale': 'pl_PL',
        },
    ],
    'gb': [
        {  # tier A gb
            'min_txn_num': 0,
            'provision': 0.026,  # 2.6%
            'txn_fee': 0,
            'fee_currency': 'GBP',
            'currency_locale': 'en_GB',
        },
        {  # tier B gb
            'min_txn_num': 60,
            'provision': 0.025,  # 2.5%
            'txn_fee': 0,
            'fee_currency': 'GBP',
            'currency_locale': 'en_GB',
        },
        {  # tier C gb
            'min_txn_num': 120,
            'provision': 0.024,  # 2.4%
            'txn_fee': 0,
            'fee_currency': 'GBP',
            'currency_locale': 'en_GB',
        },
    ],
    'ie': [
        {  # tier A ie
            'min_txn_num': 0,
            'provision': 0.026,  # 2.6%
            'txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
        },
        {  # tier B ie
            'min_txn_num': 60,
            'provision': 0.025,  # 2.5%
            'txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
        },
        {  # tier C ie
            'min_txn_num': 120,
            'provision': 0.024,  # 2.4%
            'txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
        },
    ],
    'za': [
        {  # tier A za
            'min_txn_num': 0,
            'provision': 0.026,  # 2.6%
            'txn_fee': 0,
            'fee_currency': 'ZAR',
            'currency_locale': 'en_ZA',
        },
    ],
    'es': [
        {  # tier A es
            'min_txn_num': 0,
            'provision': 0.025,  # 2.5%
            'txn_fee': 0.15,
            'fee_currency': 'EUR',
            'currency_locale': 'es_ES',
        },
    ],
}
POS__PLANS_PER_COUNTRY = {
    'us': [  # lower tiers first. IT'S FOKKEN IMPORTANT. LOWER. FIRST.
        {  # tier A us - flat
            'min_txn_num': 0,
            'provision': 0.0269,  # 2.69%
            'txn_fee': 0.3,
            'refund_provision': 0.0269,
            'refund_txn_fee': 0.3,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 15,
            'fee_currency': 'USD',
            'currency_locale': 'en_US',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
        {
            'min_txn_num': 0,
            'provision': 0.0255,  # 2.55%
            'txn_fee': 0.1,
            'refund_provision': 0.0255,
            'refund_txn_fee': 0.1,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 15,
            'fee_currency': 'USD',
            'currency_locale': 'en_US',
            'plan_type': POSPlanPaymentTypeEnum.STRIPE_TERMINAL,
        },
        {
            'min_txn_num': 0,
            'provision': 0.02,
            'txn_fee': 3,
            'refund_provision': 0,
            'refund_txn_fee': 0,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 0,
            'fee_currency': 'USD',
            'currency_locale': 'en_US',
            'plan_type': POSPlanPaymentTypeEnum.FAST_PAYOUT,
        },
    ],
    'pl': [
        {  # tier A pl
            'min_txn_num': 0,
            'provision': 0.0189,  # 1.89%
            'txn_fee': 0.49,
            'refund_provision': 0.0189,
            'refund_txn_fee': 0.49,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 100,
            'fee_currency': 'PLN',
            'currency_locale': 'pl_PL',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
    ],
    'gb': [
        {
            'min_txn_num': 0,
            'provision': 0.0129,  # 1.29%
            'txn_fee': 0.2,
            'refund_provision': 0.0129,
            'refund_txn_fee': 0.2,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 20,
            'fee_currency': 'GBP',
            'currency_locale': 'en_GB',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
        {
            'min_txn_num': 0,
            'provision': 0.02,
            'txn_fee': 3,
            'refund_provision': 0,
            'refund_txn_fee': 0,
            'chargeback_provision': 0,
            'chargeback_txn_fee': 0,
            'fee_currency': 'GBP',
            'currency_locale': 'en_GB',
            'plan_type': POSPlanPaymentTypeEnum.FAST_PAYOUT,
        },
    ],
    'ie': [
        {
            'min_txn_num': 0,
            'provision': 0.026,  # 2.6%
            'txn_fee': 0,
            'refund_provision': 0.026,
            'refund_txn_fee': 0,
            'chargeback_provision': 0.026,
            'chargeback_txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
        {  # tier B ie
            'min_txn_num': 60,
            'provision': 0.025,  # 2.5%
            'txn_fee': 0,
            'refund_provision': 0.025,
            'refund_txn_fee': 0,
            'chargeback_provision': 0.025,
            'chargeback_txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
        {  # tier C ie
            'min_txn_num': 120,
            'provision': 0.024,  # 2.4%
            'txn_fee': 0,
            'refund_provision': 0.024,
            'refund_txn_fee': 0,
            'chargeback_provision': 0.024,
            'chargeback_txn_fee': 0,
            'fee_currency': 'EUR',
            'currency_locale': 'en_IE',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
    ],
    'za': [
        {  # tier A za
            'min_txn_num': 0,
            'provision': 0.026,  # 2.6%
            'txn_fee': 0,
            'fee_currency': 'ZAR',
            'currency_locale': 'en_ZA',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
    ],
    'es': [
        {  # tier A es
            'min_txn_num': 0,
            'provision': 0.025,  # 2.5%
            'txn_fee': 0.15,
            'fee_currency': 'EUR',
            'currency_locale': 'es_ES',
            'plan_type': POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        },
    ],
}
POS__REGISTERS_COUNTABLE_PAYMENT_TYPES = ['credit_card']


###############################
############ IAP ##############
###############################

BRAINTREE_TOKEN_WITH_CUSTOMER = False


###############################
############ ADYEN ############
###############################

ADYEN_DOMAIN = ''
ADYEN_ENABLED = False
ADYEN_API_USER = None
ADYEN_API_PASS = None
ADYEN_MERCHANT_ACCOUNT = None
ADYEN_MARKET_PAY_MERCHANT_ACCOUNT = None
ADYEN_FORM_KEY = None
ADYEN_FORM_LIB_ID = None
ADYEN_FORM_LIB_PATH = None
ADYEN_3DS_ENABLED = True

MARKET_PAY_ENABLED = False
MARKET_PAY_B2B_REFERRAL_ENABLED = False
MARKET_PAY_AVS_ENABLED = False
MARKET_PAY_DOMAIN = ''
ADYEN_NOTIFICATION_AUTH = None
MARKET_PAY_USER = None
MARKET_PAY_PASS = None


AVS_ZIPCODE_REGEXP = r'^[a-z0-9A-Z \-\/]{3,12}$'


################################
############ COLORS ############
################################

# SERVICE_COLORS is deprecated, to be removed 3 months from release of color palettes
SERVICE_COLORS = [
    ['00beb7', 'd9f6f5'],
    ['ff5e00', 'ffe7d9'],
    ['ff0000', 'ffd9d9'],
    ['c400ff', 'f6d9ff'],
    ['3df200', 'e2fdd9'],
    ['0055ff', 'd9e6ff'],
    ['00fffb', 'd9fffe'],
    ['0026ff', 'd9dfff'],
    ['00b2ff', 'd9f4ff'],
    ['6600ff', 'e8d9ff'],
    ['ff007f', 'ffd9ec'],
    ['00e68e', 'd9fbee'],
    ['c8ff00', 'f7ffd9'],
    ['ff2ee3', 'ffe0fb'],
    ['f200ff', 'fdd9ff'],
    ['0084ff', 'd9edff'],
    ['ff2f00', 'ffe0d9'],
    ['bd005e', 'f5d9e7'],
    ['bd0000', 'f5d9d9'],
    ['00d9ff', 'd9f9ff'],
    ['9500ff', 'efd9ff'],
    ['e00000', 'fad9d9'],
    ['00d118', 'd9f8dd'],
    ['6fff00', 'eaffd9'],
    ['ff00ae', 'ffd9f3'],
    ['e60073', 'fbd9ea'],
    ['ffea00', 'fffcd9'],
    ['ffbb00', 'fff5d9'],
    ['00f2c2', 'd9fdf6'],
    ['9dff00', 'f0ffd9'],
    ['0fe000', 'dbfad9'],
    ['ff8c00', 'ffeed9'],
    ['e5ff00', 'fbffd9'],
]

SERVICE_COLOR_PALETTES = {
    'pastel': [
        {'accent': '549587', 'background': 'e5efed', 'text': '151618'},
        {'accent': 'd55227', 'background': 'f9e5df', 'text': '151618'},
        {'accent': 'ec4a4a', 'background': 'fce4e4', 'text': '151618'},
        {'accent': 'ab9bd8', 'background': 'f2f0f9', 'text': '151618'},
        {'accent': '90f239', 'background': 'eefde1', 'text': '151618'},
        {'accent': '7b85c7', 'background': 'ebedf7', 'text': '151618'},
        {'accent': 'b4e8e6', 'background': 'f4fcfb', 'text': '151618'},
        {'accent': '8dafd8', 'background': 'eef3f9', 'text': '151618'},
        {'accent': '579ae2', 'background': 'e6f0fb', 'text': '151618'},
        {'accent': '4651b1', 'background': 'e3e5f3', 'text': '151618'},
        {'accent': '7a29a5', 'background': 'ebdff1', 'text': '151618'},
        {'accent': '6eb37b', 'background': 'e9f4eb', 'text': '151618'},
        {'accent': 'c1c945', 'background': 'f6f7e3', 'text': '151618'},
        {'accent': 'd36c19', 'background': 'f8e9dc', 'text': '151618'},
        {'accent': '70574a', 'background': 'e1dcd3', 'text': '151618'},
        {'accent': '5c83f0', 'background': 'e6ecfd', 'text': '151618'},
        {'accent': 'cd7a73', 'background': 'f7ebea', 'text': '151618'},
        {'accent': 'bd4580', 'background': 'f5e3ec', 'text': '151618'},
        {'accent': 'b60104', 'background': 'f4d9d9', 'text': '151618'},
        {'accent': 'c2ebfc', 'background': 'f4fcfb', 'text': '151618'},
        {'accent': '8f69ab', 'background': 'f2f0f9', 'text': '151618'},
        {'accent': 'ff7999', 'background': 'ffebf0', 'text': '151618'},
        {'accent': '8db14d', 'background': 'eef3e4', 'text': '151618'},
        {'accent': '176a7c', 'background': 'ddedf0', 'text': '151618'},
        {'accent': 'ffa1ea', 'background': 'fff1fc', 'text': '151618'},
        {'accent': 'b9205e', 'background': 'f4dde7', 'text': '151618'},
        {'accent': 'eeba2b', 'background': 'fcf5df', 'text': '151618'},
        {'accent': 'ffd702', 'background': 'fff9d9', 'text': '151618'},
        {'accent': 'aae4cb', 'background': 'f2fbf7', 'text': '151618'},
        {'accent': '955636', 'background': 'e7d8d1', 'text': '151618'},
        {'accent': '497f48', 'background': 'e4ece3', 'text': '151618'},
        {'accent': 'da9222', 'background': 'f9efde', 'text': '151618'},
        {'accent': 'dfeb6d', 'background': 'f4f7e1', 'text': '151618'},
    ],
    'vivid': [
        {'accent': '549587', 'background': '549587', 'text': 'FFFFFF'},
        {'accent': 'd55227', 'background': 'd55227', 'text': 'FFFFFF'},
        {'accent': 'ec4a4a', 'background': 'ec4a4a', 'text': 'FFFFFF'},
        {'accent': 'ab9bd8', 'background': 'ab9bd8', 'text': 'FFFFFF'},
        {'accent': '90f239', 'background': '90f239', 'text': '151618'},
        {'accent': '7b85c7', 'background': '7b85c7', 'text': 'FFFFFF'},
        {'accent': 'b4e8e6', 'background': 'b4e8e6', 'text': '151618'},
        {'accent': '8dafd8', 'background': '8dafd8', 'text': '151618'},
        {'accent': '579ae2', 'background': '579ae2', 'text': 'FFFFFF'},
        {'accent': '4651b1', 'background': '4651b1', 'text': 'FFFFFF'},
        {'accent': '7a29a5', 'background': '7a29a5', 'text': 'FFFFFF'},
        {'accent': '6eb37b', 'background': '6eb37b', 'text': 'FFFFFF'},
        {'accent': 'c1c945', 'background': 'c1c945', 'text': '151618'},
        {'accent': 'd36c19', 'background': 'd36c19', 'text': 'FFFFFF'},
        {'accent': '70574a', 'background': '70574a', 'text': 'FFFFFF'},
        {'accent': '5c83f0', 'background': '5c83f0', 'text': 'FFFFFF'},
        {'accent': 'cd7a73', 'background': 'cd7a73', 'text': 'FFFFFF'},
        {'accent': 'bd4580', 'background': 'bd4580', 'text': 'FFFFFF'},
        {'accent': 'b60104', 'background': 'b60104', 'text': 'FFFFFF'},
        {'accent': 'c2ebfc', 'background': 'c2ebfc', 'text': '151618'},
        {'accent': '8f69ab', 'background': '8f69ab', 'text': 'FFFFFF'},
        {'accent': 'ff7999', 'background': 'ff7999', 'text': '151618'},
        {'accent': '8db14d', 'background': '8db14d', 'text': '151618'},
        {'accent': '176a7c', 'background': '176a7c', 'text': 'FFFFFF'},
        {'accent': 'ffa1ea', 'background': 'ffa1ea', 'text': '151618'},
        {'accent': 'b9205e', 'background': 'b9205e', 'text': 'FFFFFF'},
        {'accent': 'eeba2b', 'background': 'eeba2b', 'text': '151618'},
        {'accent': 'ffd702', 'background': 'ffd702', 'text': '151618'},
        {'accent': 'aae4cb', 'background': 'aae4cb', 'text': '151618'},
        {'accent': '955636', 'background': '955636', 'text': 'FFFFFF'},
        {'accent': '497f48', 'background': '497f48', 'text': 'FFFFFF'},
        {'accent': 'da9222', 'background': 'da9222', 'text': 'FFFFFF'},
        {'accent': 'dfeb6d', 'background': 'dfeb6d', 'text': '151618'},
    ],
}

################################
############ ADMIN ############
################################
# ticket #37582
DATA_UPLOAD_MAX_NUMBER_FIELDS = 5000 if API_DJANGO_ADMIN else 2000

################################
########### ANTI_SPAM ##########
################################
ANTI_SPAM_LIST = {
    'gumtree',
    'ggumtree',
    'guumtree',
    'gummtree',
    'gumttree',
    'gumtrree',
    'gumtreee',
    'gumtre',
    'gum tree',
    'krrb',
    'kkrrb',
    'krrrb',
    'krrbb',
    'krb',
    'craigslist',
    'craigs',
    'craigs list',
    'craigslistt',
    'ccraigslist',
    'geebo',
    'gee bo',
    'gebo',
    'geeboo',
    'oodle',
    'odle',
    'oodlee',
    'ood le',
    'classifieds',
    'classified',
    'classifiedsgiant',
    'classifieds giant',
    'clasifiedsgiant',
    'clasifieds giant',
    'backpage',
    'back page',
    'backpagee',
    'bbackpagee',
    'classifiedads',
    'classified ads',
    'adpost',
    'addpost',
    'ad post',
    'adpostt',
    'add post',
    'recycler',
    're cycler',
    'recyclerr',
    'rrecycler',
    'recycller',
    'usfreeads',
    'us freeads',
    'usfreads',
    'usfree ads',
    'domesticsale',
    'domestic sale',
    'ddomesticsale',
    'domesticsalle',
    'yakaz',
    'yakazz',
    'yakkaz',
    'yaakaz',
    'yyakaz',
    'olx',
    'oolx',
    'ollx',
    'olxx',
    'o lx',
    'ol x',
    'groupon',
    'kolektiva',
    'bazos',
    'bbazos',
    'baz os',
    'bazzos',
    'bazoos',
    'bazoss',
    'milanuncios',
    'millanuncios',
    'mila nuncios',
    'segundamano',
    'ssegundamano',
    'mundoanuncio',
    'mundo anuncio',
    'habitamos',
    'habitamoss',
    'hhabitamos',
    'casinuevo',
    'anunciosdiarios',
    'tusanuncios',
    'close5',
    'locanto',
    'pennysaverusa',
    'penny saverusa',
    'adlandpro',
    'ad landpro',
    'ad land pro',
    'classifieds.usatoday',
    'classifiedsusatoday',
    'classifieds usa today',
    'classifieds usatoday',
    'adsglobe',
    'ads globe',
    'adsgglobe',
    'citynews',
    '10dayads',
    '10 dayads',
    '10 day ads',
    'ad to ad',
    'ad2ad',
    'adtoad',
    'vast',
    'trovit',
    'freeadstime',
    'free ads time',
    'free adstime',
    # PL specific
    'mamfryzjera',
    'nakiedy',
}

################################
########### REPEATING ##########
################################
MAX_REPEATING_YEAR = 1
MAX_REPEATING_DAY = 1

###############################
############ GDPR #############
###############################

BOOKSY_AGREEMENT_INFORMATION_TEMPLATE = 'booksy_agreement_information_partners_{}.pdf'
BOOKSY_AGREEMENT_INFORMATION_DEFAULT = BOOKSY_AGREEMENT_INFORMATION_TEMPLATE.format('default')

# Agreement names
PP_AGREEMENT = 'privacy_policy_agreement'
MA_AGREEMENT = 'marketing_agreement'
PM_AGREEMENT = 'partner_marketing_agreement'
# WC agreement, seriously ?
WB_AGREEMENT = 'web_communication_agreement'
# IB_AGREEMENT
DO_AGREEMENT = 'disclosure_obligation_agreement'
PC_AGREEMENT = 'processing_consent'
RM_AGREEMENT = 'receiving_messages_consent'

# order mater it used in serializer
# name agreement must be consistent with actual name sn db
# and with settings.REQUIRED_AGREEMENTS

# PLEASE REMEMBER to add new fields
# to UserPolicyAgreement, BusinessCustomerInfo and BusinessPolicyAgreement
REQUIRED_CUSTOMER_AGREEMENTS = {
    PP_AGREEMENT: True,
    MA_AGREEMENT: False,
    PM_AGREEMENT: False,
}


REQUIRED_BUSINESS_AGREEMENTS = {
    PP_AGREEMENT: True,
    MA_AGREEMENT: False,
    RM_AGREEMENT: False,
    PM_AGREEMENT: False,
}


REQUIRED_BUSINESS_CUSTOMER_AGREEMENTS = {
    WB_AGREEMENT: False,
    DO_AGREEMENT: False,
    PC_AGREEMENT: False,
}
# FALLBACK_KEY_GDPR
FALLBACK_KEY_GDPR = 'default'


DSA_COMPLAINT_VALUE = 'compliant'
DSA_SEMI_COMPLAINT_VALUE = 'semi-compliant'
DSA_NON_COMPLAINT_VALUE = 'non-compliant'
DSA_COMPLIANT = {
    # Austria
    'at': DSA_COMPLAINT_VALUE,
    # Belgium
    'be': DSA_COMPLAINT_VALUE,
    # Bulgaria
    'bg': DSA_COMPLAINT_VALUE,
    # Cyprus
    'cy': DSA_COMPLAINT_VALUE,
    # Czech Republic
    'cz': DSA_COMPLAINT_VALUE,
    # Denmark
    'dk': DSA_COMPLAINT_VALUE,
    # Germany
    'de': DSA_COMPLAINT_VALUE,
    # Estonia'
    'ee': DSA_COMPLAINT_VALUE,
    # Spain
    'es': DSA_COMPLAINT_VALUE,
    # Finland
    'fi': DSA_COMPLAINT_VALUE,
    # France
    'fr': DSA_COMPLAINT_VALUE,
    # Greece
    'gr': DSA_COMPLAINT_VALUE,
    # Croatia
    'hr': DSA_COMPLAINT_VALUE,
    # Hungary
    'hu': DSA_COMPLAINT_VALUE,
    # Ireland
    'ie': DSA_COMPLAINT_VALUE,
    # Italy
    'it': DSA_COMPLAINT_VALUE,
    # Luxembourg
    'lu': DSA_COMPLAINT_VALUE,
    # Malta
    'mt': DSA_COMPLAINT_VALUE,
    # Netherlands
    'nl': DSA_COMPLAINT_VALUE,
    # Poland
    'pl': DSA_COMPLAINT_VALUE,
    # Portugal
    'pt': DSA_COMPLAINT_VALUE,
    # Romania
    'ro': DSA_COMPLAINT_VALUE,
    # Sweden
    'se': DSA_COMPLAINT_VALUE,
    # Slovenia
    'si': DSA_COMPLAINT_VALUE,
    # Slovakia
    'sk': DSA_COMPLAINT_VALUE,
    # Great Britain
    'gb': DSA_SEMI_COMPLAINT_VALUE,
    # Default_fallback
    'default': DSA_NON_COMPLAINT_VALUE,
}


GDPR_COUNTRIES = {
    # USA
    'us': False,
    # BR
    'br': False,
    ##################
    # GDPR COUNTRIES #
    ##################
    # Albania
    'al': True,
    # Andorra
    'ad': False,
    # Austria
    'at': True,
    # Belarus
    'by': False,
    # Belgium
    'be': True,
    # Bosnia and Herzegovina
    'ba': True,
    # Bulgaria
    'bg': False,
    # Croatia
    'hr': True,
    # Cyprus
    'cy': True,
    # Czech Republic
    'cz': True,
    # Denmark
    'dk': True,
    # Estonia'
    'ee': True,
    # Faroe Islands
    'fo': True,
    # Finland
    'fi': True,
    # France
    'fr': True,
    # Germany
    'de': True,
    # Gibraltar
    'gi': True,
    # Greece
    'gr': True,
    # Hungary
    'hu': True,
    # Iceland
    'is': True,
    # Ireland
    'ie': True,
    # Isle of Man
    'im': True,
    # Italy
    'it': True,
    # Latvia
    'lv': True,
    # Liechtenstein
    'li': True,
    # Lithuania
    'lt': True,
    # Luxembourg
    'lu': True,
    # Macedonia
    'mk': True,
    # Malta
    'mt': True,
    # Moldova
    'md': False,
    # Monaco
    'mc': True,
    # Montenegro
    'me': True,
    # Netherlands
    'nl': True,
    # Norway
    'no': True,
    # Poland
    'pl': True,
    # Portugal
    'pt': True,
    # Romania
    'ro': True,
    # Russia
    'ru': False,
    # San Marino
    'sm': True,
    # Serbia
    'rs': True,
    # Slovakia
    'sk': True,
    # Slovenia
    'si': True,
    # Spain
    'es': True,
    # Sweden
    'se': True,
    # Switzerland
    'ch': True,
    # Ukraine
    'ua': False,
    # Great Britain (United Kingdom)
    'gb': True,
    # Vatican city
    'va': True,
    # Default_fallback
    'default': False,
}

# Countries in which Businesses have to sign GDPR Annex
GDPR_ANNEX_COUNTRIES = {
    'pl',
}

GDPR_ANNEX_SETTINGS = {
    'pl': {
        'email': '<EMAIL>',
        'annex_file': 'gdpr_annex_booksy__pl.pdf',
        'signed_by_default': True,
    },
    'default': {
        'email': '<EMAIL>',
        'annex_file': 'gdpr_annex_booksy__default.pdf',
        'signed_by_default': False,
    },
}

# Business defaults
COUNTRY__BIZ__SMS_NOTIFICATION_ENABLED = {
    Country.PL,
    Country.FR,
}
# pylint: disable=line-too-long
# for info why some sms_prefix are commented out, check:
# https://www.twilio.com/help/faq/sms/what-countries-does-twilio-support-alphanumeric-sender-id
# pylint: enable=line-too-long
MAX_MESSAGE_BLAST_PARTS_COUNTRY_SMS = 2

COUNTRY__BIZ__SMS_INVITATION_PERIOD_LIMIT_EXCLUDE = {
    'us',
    'ca',
}

# Countries in which sms invitation period limit is on
COUNTRY__BIZ__SMS_INVITATION_PERIOD_LIMIT = (
    Country.supported() - COUNTRY__BIZ__SMS_INVITATION_PERIOD_LIMIT_EXCLUDE
)

# All sms invitations are taken into account (per single customer ofc)
SMS_INVITATION_LIMIT_PERIOD = datetime.timedelta(days=30)

# Family and Friends invitations expiration limit
FAMILY_AND_FRIENDS_INVITATION_LIMIT_PERIOD = datetime.timedelta(days=7)

# Family and Friends reinvite blocked
FAMILY_AND_FRIENDS_REINVITE_BLOCKED_LIMIT_PERIOD = datetime.timedelta(days=1)

# For how many days after appointment user may add review
CAN_ADD_REVIEW_LIMIT_PERIOD = datetime.timedelta(days=30)
# For how many days after review was created user may edit review
CAN_EDIT_REVIEW_LIMIT_PERIOD = datetime.timedelta(days=30)

VERIFICATION_CODE_EMAIL_FROM = {
    'name': 'Booksy Inc',
    'address_line1': '171 N Aberdeen St, Suite 400',
    'address_city': 'Chicago',
    'address_zip': '60607',
    'address_state': 'IL',
}

# Which poll should we display to customer that wants to remove her account
ACCOUNT_DELETION_SURVEY_NAME = 'delete-account'

##################################
########### MARKETING ############
##################################

MIN_PROFIT = {
    'us': 100,
}
BASIC_PROFIT = {
    'us': 25,
}

##################################
############# OTHER ##############
##################################

FEEDS__CHECK_TRIAL_VERIFICATION = False
ANTI_FRAUD_COUNTRIES_LIST = ['us']
FEEDS__GOOGLE_SPLIT_MERCHANTS = 5000
FEEDS__GOOGLE_SPLIT_SERVICES = 5000
FEEDS__GOOGLE_SPLIT_AVAILABILITY = 70

WAITLIST_QUERY_TODAY_LIMIT = 1
WAITLIST_ANY_TODAY_LIMIT = 3
WAITLIST_QUERY_EVER_LIMIT = 2
WAITLIST_ENABLED = True

COUNTRY__INVOICE_DOWNLOAD_ENABLED = Country.supported() - {
    Country.PL,
}

MASS_VALUE__DECIMAL_LENGTH = 3


##################################
######### SUBSCRIPTIONS ##########
##################################

# BIL-597
COUNTRY__SUBSCRIPTION_TEST_BUSINESSES = {
    Country.US: [
        11355,  # Apple Inc.
        485970,  # Apple Inc.
    ],
}

# 56156
COUNTRY__SUBSCRIPTION_PURCHASE_LIMIT_STAFF_NUM = {
    'gb',
}

# Braintree only
COUNTRY__CHARGE_FOR_STAFFERS_ENABLED = {
    Country.US,
    Country.ES,
    Country.PL,
    Country.GB,
    Country.IE,
    Country.ZA,
    Country.BR,
    Country.PT,
    Country.AU,
    Country.AR,
    Country.MX,
    Country.CL,
}
# Currently turned off for all
COUNTRY__STAFFERS_PLAN_SWITCH_ENABLED = set()
# We apply changes at the end of Nth day after change in staff amount
CHARGE_FOR_STAFFERS_DELTA_DAYS = 1

# Braintree only
COUNTRY__DISCOUNT_AFTER_POB_ENABLED = {
    Country.US,
}

DISCOUNT_AFTER_POB_DAYS_PAST_DUE = 14

# Invoices
INVOICES_ENABLED = False

# onboarding
ONBOARDING_ENABLED = True

##################################
########### DONATES ##############
##################################

DEFAULT_DONATES_VALUES = {
    Country.US: [10, 20, 40],
    Country.GB: [5, 10, 15],
    Country.IE: [5, 10, 15],
    Country.PL: [20, 50, 100],
    Country.ES: [5, 10, 20],
    Country.BR: [5, 10, 20],
    Country.ZA: [10, 50, 100],
}

POPUP_NOTIFICATIONS_ENABLED = True
POPUP_PHASE2 = True
POPUP_PHASE3 = True
POPUP_PHASE4 = True

GOOGLE_RECAPTCHA_SECRET = '6LcOnZUbAAAAAPJc3r0f3HgzYtSKCW9heaeSnnhV'

SOFT_LIMIT_IN_KM_OF_BUSINESS_GEOLOCATION_CHANGE = 0.5

DELETE_ACCOUNT_AFTER_DAYS = 7
DONT_INFORM_USER_FOR_DELETE_REQUESTS_OLDER_THAN_DAYS = 30
ID_MD5_CHARS_PREFIX_LENGTH = 5
DELETE_ACCOUNT_TASK_MAX_NUMBER_OF_REQUEST = 100

COUNTRY_TO_EMAIL_MAP = {
    Country.PL: '<EMAIL>',
    Country.GB: '<EMAIL>',
    Country.US: '<EMAIL>',
    Country.ES: '<EMAIL>',
}

COUNTRIES_WITH_LEGAL_FOOTER_ENABLED = [
    Country.PL,
    Country.ES,
    Country.IE,
    Country.PT,
    Country.GB,
    Country.FR,
    Country.NL,
    Country.US,
]

# global payout methods change switch
BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED = True

BOOKSY_PAY_CASHBACK_PER_COUNTRY = {
    'pl': {
        # DEFAULT TO BE DEPRECATED AFTER THE EXPERIMENT STARTS
        BooksyPayCashbackType.DEFAULT: {
            'amount_type': 'fixed',
            'amount': Decimal('10.00'),
            'currency': 'PLN',
            'min_booking_amount': Decimal('50.00'),
            'required_num_of_txns': 1,
            'policy_url': 'https://booksy.com/pl-pl/p/cashback?content-only=1',
        },
        BooksyPayCashbackType.VARIANT_A: {
            'amount_type': 'fixed',
            'amount': Decimal('10.00'),
            'currency': 'PLN',
            'min_booking_amount': Decimal('50.00'),
            'required_num_of_txns': 1,
            'policy_url': 'https://booksy.com/pl-pl/p/cashback?variant=a&content-only=1',
        },
        BooksyPayCashbackType.VARIANT_B: {
            'amount_type': 'percentage',
            'amount': Decimal('10.00'),
            'currency': 'PLN',
            'min_booking_amount': None,
            'max_cashback_amount': Decimal('20.00'),
            'required_num_of_txns': 1,
            'policy_url': 'https://booksy.com/pl-pl/p/cashback?variant=b&content-only=1',
        },
        BooksyPayCashbackType.VARIANT_C: {
            'amount_type': 'fixed',
            'amount': Decimal('30.00'),
            'currency': 'PLN',
            'min_booking_amount': Decimal('50.00'),
            'required_num_of_txns': 2,
            'policy_url': 'https://booksy.com/pl-pl/p/cashback?variant=c&content-only=1',
        },
    },
}
