from decimal import Decimal

from django.test.utils import override_settings

from country_config import CURRENCY_CODES, CURRENCY_LOCALES, Country
from lib.tools import format_currency_wo_decimals, format_currency


def test_configs():
    answer = {
        'br': 'R$ 1.000.000',
        'ca': '$1,000,000',
        'cl': '$1.000.000',
        'es': '1.000.000 €',
        'gb': '£1,000,000',
        'ie': '€1,000,000',
        'pl': '1 000 000 zł',
        'us': '$1,000,000',
        'za': 'R1 000 000',
        'ar': '$ 1.000.000',
        'au': '$1,000,000',
        'co': '$ 1.000.000',
        'de': '1.000.000 €',
        'dk': '1.000.000 kr.',
        'fi': '1 000 000 €',
        'fr': '1\u202f000\u202f000 €',
        'hk': 'HK$1,000,000',
        'in': '₹10,00,000',
        'it': '1.000.000 €',
        'jp': '￥1,000,000',
        'mx': '$1,000,000',
        'my': 'RM1,000,000',
        'ng': '₦1,000,000',
        'nl': '€ 1.000.000',
        'ph': '₱1,000,000',
        'pt': '1 000 000 €',
        'ru': '1 000 000 ₽',
        'se': '1 000 000 kr',
        'sg': '$1,000,000',
        've': 'Bs.1.000.000',
    }

    country_list = Country.choices()

    for country, _unused in country_list:
        with (
            override_settings(API_COUNTRY=country),
            override_settings(CURRENCY_CODE=CURRENCY_CODES[country]),
            override_settings(CURRENCY_LOCALE=CURRENCY_LOCALES[country]),
        ):
            ret = format_currency_wo_decimals(Decimal('1000000'))
            assert ret == answer[country], f'Wrong format for {country!r}'


def test_format_currency_with_decimal_quantization():
    value = 0.9876
    assert format_currency(value, decimal_quantization=False) == '$0.9876'
