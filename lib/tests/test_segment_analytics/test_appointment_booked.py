import pytest

from model_bakery import baker
from mock import (
    patch,
    MagicMock,
)

from lib.segment_analytics.api import AnalyticsApi
from lib.segment_analytics import get_segment_api
from lib.segment_analytics.utils import get_booking_count
from lib.test_utils import create_subbooking
from webapps.booking.models import (
    Appointment,
    BookingSources,
)
from webapps.business.models import Business


@pytest.mark.django_db
@patch.object(AnalyticsApi, '_specific_sum_of_appointments', MagicMock())
@patch.object(AnalyticsApi, '_aha_habit_moment', MagicMock())
def test_appointment_booked():
    business1 = baker.make(Business)
    business2 = baker.make(Business)
    segment_api = get_segment_api(business1)

    bs_google = baker.make(BookingSources, name='GoogleFeeds')
    bs_android = baker.make(BookingSources, name='Android')

    booking_b1_google = create_subbooking(
        source=bs_google,
        business=business1,
        booking_type=Appointment.TYPE.CUSTOMER,
    )[0]
    booking_b1_android = create_subbooking(
        source=bs_android,
        business=business1,
        booking_type=Appointment.TYPE.CUSTOMER,
    )[0]
    booking_b2_google = create_subbooking(
        source=bs_google,
        business=business2,
        booking_type=Appointment.TYPE.CUSTOMER,
    )[0]
    booking_b2_android = create_subbooking(
        source=bs_android,
        business=business2,
        booking_type=Appointment.TYPE.CUSTOMER,
    )[0]

    segment_api.appointment_booked(booking_b1_google)
    segment_api.appointment_booked(booking_b1_android)
    segment_api.appointment_booked(booking_b2_google)
    segment_api.appointment_booked(booking_b2_android)

    gbc_business1 = get_booking_count(business1)

    assert gbc_business1.cb == 2
    assert gbc_business1.all == 2
    assert gbc_business1.cb_google == 1
