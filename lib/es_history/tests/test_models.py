import pytest
from freezegun import freeze_time
from model_bakery import baker

from lib.es_history.diff import FieldChange
from lib.es_history.tasks import index_history_records_task
from lib.es_history.tests.conftest import DummyDocument, DummyModel


@pytest.mark.django_db
def test_auto_add_off(test_index):
    DummyModel.history_auto_add = False
    instance = baker.make(DummyModel)

    assert not hasattr(instance, '_history_state')

    instance.foo = 'oof'
    instance.bar = 42

    instance.save()
    test_index.refresh()
    hits = DummyDocument.search().execute().hits

    assert not hits


@pytest.mark.django_db
@pytest.mark.parametrize('use_celery', (True, False))
def test_auto_add_on(test_index, clear_queue, test_data, use_celery):
    DummyModel.history_auto_add = True
    instance = baker.make(DummyModel)
    before = instance._history_state  # pylint: disable=protected-access

    assert before == {'foo': instance.foo, 'bar': instance.bar}

    instance.foo = 'oof'
    instance.bar = 42

    with freeze_time(test_data['time']):
        instance.save(history_user=test_data['user'], history_use_celery=use_celery)

        if use_celery:
            index_history_records_task(DummyModel)

    test_index.refresh()
    (document,) = DummyDocument.search().execute().hits

    assert document.pk == instance.pk
    assert document.updated == test_data['time']
    assert document.user == test_data['user_data']
    assert document.changes == {
        'foo': FieldChange(before=before['foo'], after='oof'),
        'bar': FieldChange(before=before['bar'], after=42),
    }
    assert set(document.changed_fields) == {'foo', 'bar'}


def test_extract_state_full():
    instance = baker.prepare(DummyModel)

    state = instance.extract_state()

    assert state == {
        'foo': instance.foo,
        'bar': instance.bar,
    }


def test_extract_state_partial():
    instance = baker.prepare(DummyModel)

    state = instance.extract_state({'foo'})

    assert state == {
        'foo': instance.foo,
    }


def test_bulk_extract_state_full(test_data):
    instances = test_data['instances']

    states = DummyModel.bulk_extract_state(instances)

    assert len(instances) == len(states)

    for instance in instances:
        assert states[instance.pk] == {
            'foo': instance.foo,
            'bar': instance.bar,
        }


def test_bulk_extract_state_partial(test_data):
    instances = test_data['instances']

    states = DummyModel.bulk_extract_state(instances, {'foo'})

    assert len(instances) == len(states)

    for instance in instances:
        assert states[instance.pk] == {
            'foo': instance.foo,
        }
