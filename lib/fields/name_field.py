from django.core.validators import ProhibitNullCharactersValidator
from rest_framework import serializers

from lib.serializers import NameRegexField


def _validate_apostrophe_count(
    value: str,
    error_message: dict,
    allow_blank: bool = True,
) -> str:
    """Fail if value equals to apostrophe or has more tan one in self."""
    if allow_blank and not value:  # allow blank but not None
        return value
    apostrophe = '\''
    if value == apostrophe or value.count(apostrophe) > 1:
        raise serializers.ValidationError(error_message)
    return value


class NameField(NameRegexField):
    default_validators = [
        ProhibitNullCharactersValidator(),
    ]

    def __init__(
        self,
        required=True,
        allow_blank=False,
        max_length=30,
        **kwargs,
    ):
        new_kwargs = {'required': required, 'allow_blank': allow_blank, 'max_length': max_length}
        merged_kwargs = {**kwargs, **new_kwargs}
        # NameRegexField doesn't accept *args
        # because CharField also accept only **kwargs
        super(<PERSON><PERSON><PERSON>, self).__init__(**merged_kwargs)

    def run_validators(self, value):
        super().run_validators(value)
        _validate_apostrophe_count(
            value,
            # custom validator with same invalid error message
            self.error_messages['invalid'],
            self.allow_blank,
        )
