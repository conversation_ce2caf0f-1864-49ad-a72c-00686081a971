# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: inapp.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0binapp.proto\"P\n\rUserBasicData\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x10\n\x08\x61pp_type\x18\x03 \x01(\t\"a\n\x1a\x43reateWarningCircleRequest\x12\'\n\x0fuser_basic_data\x18\x01 \x01(\x0b\x32\x0e.UserBasicData\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\"e\n\x1b\x43reateWarningCircleResponse\x12\'\n\x0f\x62\x61sic_user_data\x18\x01 \x01(\x0b\x32\x0e.UserBasicData\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"S\n\x1aRemoveWarningCircleRequest\x12\'\n\x0fuser_basic_data\x18\x01 \x01(\x0b\x32\x0e.UserBasicData\x12\x0c\n\x04name\x18\x02 \x01(\t\"e\n\x1bRemoveWarningCircleResponse\x12\'\n\x0f\x62\x61sic_user_data\x18\x01 \x01(\x0b\x32\x0e.UserBasicData\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x32\xbf\x01\n\x11InAppNotification\x12T\n\x15\x63reate_warning_circle\x12\x1b.CreateWarningCircleRequest\x1a\x1c.CreateWarningCircleResponse\"\x00\x12T\n\x15remove_warning_circle\x12\x1b.RemoveWarningCircleRequest\x1a\x1c.RemoveWarningCircleResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'inapp_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_USERBASICDATA']._serialized_start=15
  _globals['_USERBASICDATA']._serialized_end=95
  _globals['_CREATEWARNINGCIRCLEREQUEST']._serialized_start=97
  _globals['_CREATEWARNINGCIRCLEREQUEST']._serialized_end=194
  _globals['_CREATEWARNINGCIRCLERESPONSE']._serialized_start=196
  _globals['_CREATEWARNINGCIRCLERESPONSE']._serialized_end=297
  _globals['_REMOVEWARNINGCIRCLEREQUEST']._serialized_start=299
  _globals['_REMOVEWARNINGCIRCLEREQUEST']._serialized_end=382
  _globals['_REMOVEWARNINGCIRCLERESPONSE']._serialized_start=384
  _globals['_REMOVEWARNINGCIRCLERESPONSE']._serialized_end=485
  _globals['_INAPPNOTIFICATION']._serialized_start=488
  _globals['_INAPPNOTIFICATION']._serialized_end=679
# @@protoc_insertion_point(module_scope)
