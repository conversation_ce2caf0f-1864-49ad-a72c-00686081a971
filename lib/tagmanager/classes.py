from lib.tagmanager.utils import (
    get_appsflyer_url_params_from_device_type,
    get_firebase_url_params_from_device_type,
)
from webapps.segment.utils import get_device_type_name_from_api_key


class BaseAuth:
    def __init__(self):
        self._device_type = None
        self._url_params = None

        self.url_param_getter = None
        self.booking_source_id = None

    @property
    def url_params(self):
        if self._url_params is None:
            # pylint: disable=not-callable
            self._url_params = self.url_param_getter(self.device_type)
            # pylint: enable=not-callable
        return self._url_params

    @property
    def device_type(self):
        from webapps.booking.models import BookingSources

        if self._device_type is None and self.booking_source_id:
            booking_source = BookingSources.objects.filter(id=self.booking_source_id).first()
            self._device_type = get_device_type_name_from_api_key(booking_source.api_key)
        return self._device_type


class AppsFlyerAuth(BaseAuth):
    def __init__(self, appsflyer):
        super().__init__()
        self.url_param_getter = get_appsflyer_url_params_from_device_type

        self.appsflyer = appsflyer
        if self.appsflyer:
            self.booking_source_id = self.appsflyer.booking_source_id

    @property
    def body_params(self):
        if self.appsflyer:
            return {
                'advertisingId': self.appsflyer.advertising_id,
                'appsFlyerId': self.appsflyer.appsflyer_device_id,
                'afUserId': self.appsflyer.appsflyer_user_id,
            }
        return {}


class FirebaseAuth(BaseAuth):
    def __init__(self, booking_source_id=None, client_id=None, app_instance_id=None):
        super().__init__()
        self.url_param_getter = get_firebase_url_params_from_device_type

        self.booking_source_id = booking_source_id
        self.client_id = client_id
        self.app_instance_id = app_instance_id

    @property
    def body_params(self):
        return {self.id_name: self.id_value}

    @property
    def id_name(self):
        if self.client_id:
            return 'client_id'
        if self.app_instance_id:
            return 'app_instance_id'

    @property
    def id_value(self):
        return self.client_id or self.app_instance_id
