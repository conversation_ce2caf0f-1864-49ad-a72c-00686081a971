from django.conf import settings

from country_config import Country
from lib.geocoding.built_in import BuiltInGeoCoder


def internal_test_built_in_decoder():
    test_city_internal = (
        (52.229, 21.0122, 'pl', 'Warsaw'),  # Warsaw
        (51.1079, 17.0385, 'pl', 'Wrocław'),  # Wrocław
        (50.0647, 19.9450, 'pl', 'Krakow'),  # Krakow
        (50.1022, 18.5463, 'pl', 'Rybnik'),  # Rybnik
        # US
        (40.730610, -73.935242, 'us', 'Greenpoint'),  # New-York
        (37.773972, -122.431297, 'us', 'San Francisco, CA'),  # San Francisco
        (37.279518, -121.867905, 'us', 'San Jose, CA'),  # San Jose
        (41.881832, -87.623177, 'us', 'Chicago, IL'),  # Chicago
        (25.761681, -80.191788, 'us', 'Miami, FL'),  # Miami
        (48.769768, -122.485886, 'us', 'Bellingham, WA'),  # Bellingham
        (47.9252568, -97.0328547, 'us', 'East Grand Forks, MN'),  # Grand Forks
    )

    internal_test_countries = {Country.US, Country.PL}
    if settings.API_COUNTRY not in internal_test_countries:
        return
    test_cities = [x for x in test_city_internal if x[2] == settings.API_COUNTRY]
    geo_decoder = BuiltInGeoCoder()
    for city_info in test_cities:
        city_name = city_info[-1]
        latitude, longitude = city_info[0], city_info[1]
        geo_info = geo_decoder.reverse_geocode(latitude, longitude)
        assert city_name == geo_info['city'], f'Expected: f{city_name} ; Got: {geo_info["city"]}'
