import logging
from collections import OrderedDict

import requests
from django.conf import settings
from django.utils.translation import gettext as _
from slugify import slugify

from country_config.enums import Country
from lib.elasticsearch.consts import ESDocType
from lib.geocoding.base import BaseGeocoder, GeocodingError
from lib.tools import firstof
from lib.geocoding.utils import format_address


_logger = logging.getLogger('booksy.google-geocode')

_street_hints_log = logging.getLogger('booksy.street_hints')


class ReverseGeocoderHelper:
    ROOFTOP = 'ROOFTOP'
    RANGE_INTERPOLATED = 'RANGE_INTERPOLATED'
    GEOMETRIC_CENTER = 'GEOMETRIC_CENTER'
    APPROXIMATE = 'APPROXIMATE'

    @classmethod
    def find_component_by_type(
        cls, component_type, result, field='long_name', blacklisted_type=None
    ):
        for component in result.get('address_components', []):
            types = component.get('types', [])
            if component_type in types and not blacklisted_type in types:
                return component[field]
        return ''

    @classmethod
    def find_component(cls, component_type, results, field='long_name', blacklisted_type=None):
        for result in results:
            ret = cls.find_component_by_type(
                component_type, result, field, blacklisted_type=blacklisted_type
            )
            if ret:
                return ret

        return ''

    @staticmethod
    def find_zip_code(results):
        if results and 'geometry' in results[0]:
            location_type = results[0]['geometry'].get('location_type')
            # Exact match
            if location_type == ReverseGeocoderHelper.ROOFTOP:
                address_components = results[0].get('address_components', [])
                # Find entry with zip code
                for c in address_components:
                    component = c.get('types', [])
                    if 'postal_code' in component:
                        zip_code_info = c
                        break
                else:
                    zip_code_info = {}
                zip_code = zip_code_info.get('long_name')
                if zip_code:
                    return zip_code

        filtered = list(
            filter(
                lambda x: (
                    'postal_code' in x.get('types') and 'postal_code_prefix' not in x.get('types')
                ),
                results,
            )
        )

        if filtered:
            return filtered[0]['address_components'][0]['long_name']
        return ''

    @classmethod
    def get_data(cls, results):
        street_number = cls.find_component('street_number', results)
        route = cls.find_component('route', results)
        country_code = cls.find_component(
            'country',
            results,
            field='short_name',
        )

        ret = {
            'address': format_address(route, street_number, country_code=country_code),
            'city': cls.find_component('locality', results),
            'zip': cls.find_zip_code(results),
            'country': cls.find_component(
                'country',
                results,
                field='long_name',
            ),
            'country_code': country_code,
        }
        return ret


class Geocoder(BaseGeocoder):
    codename = 'google'
    GEOCODE_URL = 'https://maps.googleapis.com/maps/api/geocode/json'
    REVERSE_GEOCODE_URL = 'https://maps.googleapis.com/maps/api/geocode/json'

    # pylint: disable=too-many-arguments
    @classmethod
    def geocode(cls, address=None, address2=None, zipcode=None, city=None, country=None):
        """
        Pylint duplicate-code (R0801)
        It's impossible to disable duplicate code check
        on portions of code. Pylint with the current configuration
        ignores 'min-similarity-lines=10'.
        Open issue on pylint's github:
        https://github.com/PyCQA/pylint/issues/214
        """
        geocoder_settings = settings.GEOCODER.get('google', {})

        if not country:
            country = settings.COUNTRY_CONFIG.name

        components = [address, address2, city, zipcode, country]

        address_fmt = ', '.join(
            [x.decode('utf-8') if not isinstance(x, str) else x for x in components if x]
        )

        if not address_fmt:
            raise GeocodingError('geocode: at least one address entry is required')

        params = {
            'address': address_fmt,
            'region': geocoder_settings.get('region', 'us'),
            'language': geocoder_settings.get('language', 'en'),
        }
        if settings.GOOGLE_PLACES_API_KEY:
            params['key'] = settings.GOOGLE_PLACES_API_KEY
        response = requests.get(
            cls.GEOCODE_URL,
            params=params,
            timeout=5.0,
        )
        _logger.info("geocode: %r", response.content)

        response_json = response.json()

        if response_json.get('status') != 'OK':
            raise GeocodingError(f'status is wrong: {response_json.get("status")}')

        if len(response_json.get('results', [])) == 0:
            raise GeocodingError(
                _(
                    'We have not found the address you have entered. '
                    'Make sure the city and zip code are correct.'
                )
            )

        # Get some result, we will try to improve it
        result = response_json['results'][0]

        if zipcode is not None:
            # Attempt to find the result that contains this zipcode
            # Example: Terespolska 4, 03-813 returns 2 results:
            # first one is zipcode 00-001, second is 03-813 and
            # so we want the second one obviously
            for r in response_json['results']:
                if (
                    cls._filter_address_type(
                        r,
                        postal_type='postal_code',
                    ).get('long_name')
                    == zipcode
                ):
                    result = r

        address = [
            cls._filter_address_type(
                result,
                postal_type='route',
            ).get('long_name'),
            cls._filter_address_type(
                result,
                postal_type='street_number',
            ).get('long_name'),
        ]

        if 'location' in result['geometry']:
            # In case precise location is found
            tmp_zipcode = cls._filter_address_type(
                result,
                postal_type='postal_code',
            ).get('long_name', zipcode)
            tmp_city = cls._filter_address_type(
                result,
                postal_type='locality',
            ).get('long_name', city)
            tmp_country = cls._filter_address_type(
                result,
                postal_type='country',
            ).get('long_name', country)

            return cls.format_geocode_response(
                latitude=result['geometry']['location']['lat'],
                longitude=result['geometry']['location']['lng'],
                zipcode=tmp_zipcode,
                address=' '.join([x for x in address if x]),
                city=tmp_city,
                country=tmp_country,
            )
        if 'bounds' in result['geometry']:
            # In case exact location isn't found but only an
            # area
            north_east = result['geometry']['bounds']['northeast']
            south_west = result['geometry']['bounds']['southwest']
            z_code = cls._filter_address_type(
                result,
                postal_type='postal_code',
            ).get('long_name', zipcode)
            tmp_country = cls._filter_address_type(
                result,
                postal_type='country',
            ).get('long_name', country)
            tmp_city = cls._filter_address_type(result, postal_type='locality').get(
                'long_name', city
            )
            tmp_address = ' '.join([x for x in address if x])

            return cls.format_geocode_response(
                latitude=0.5 * (north_east['lat'] + south_west['lat']),
                longitude=0.5 * (north_east['lng'] + south_west['lng']),
                zipcode=z_code,
                address=tmp_address,
                city=tmp_city,
                country=tmp_country,
            )

        # Don't know any other types
        raise GeocodingError(f'unknown response format, {response_json}')

    @classmethod
    def _filter_address_type(cls, response, postal_type='postal_code'):
        """
        Filter address_type component from Google geocoding response.
        """
        try:
            return list(
                filter(
                    lambda x: postal_type in x.get('types', []),
                    response.get('address_components', []),
                )
            )[0]
        except IndexError:
            return {}

    @classmethod
    def reverse_geocode(cls, latitude=None, longitude=None, **__):
        geocoder_settings = settings.GEOCODER.get('google', {})

        if (latitude is None) or (longitude is None):
            raise GeocodingError('reverse_geocode: lat/lng is required')

        params = {
            'latlng': f'{latitude}, {longitude}',
            'language': geocoder_settings.get('language', 'en'),
        }
        if settings.GOOGLE_PLACES_API_KEY:
            params['key'] = settings.GOOGLE_PLACES_API_KEY
        response = requests.get(
            cls.REVERSE_GEOCODE_URL,
            params=params,
            timeout=5.0,
        )
        _logger.info("reverse: %r", response.content)
        return ReverseGeocoderHelper.get_data(response.json().get('results', []))


class AutoComplete:

    @classmethod
    def get_data(cls, query):
        query = query.replace('-', '')  # search for zipcode without dash
        params = {
            'input': query,
            'language': settings.GEOCODER['google']['language'],
            'components': settings.GEOCODER['google']['components'],
            'types': 'geocode',
        }
        if settings.GOOGLE_PLACES_API_KEY:
            params['key'] = settings.GOOGLE_PLACES_API_KEY
        response = requests.get(
            'https://maps.googleapis.com/maps/api/place/autocomplete/json',
            params=params,
            timeout=5.0,
        )
        hints = []
        try:
            data = response.json()
        except ValueError:
            _street_hints_log.exception(
                'response error: status=%r text=%r', response.status_code, response.text
            )
        else:
            # exception: google returns "Polska" and
            # api returns empty string #40911
            for prediction in data['predictions']:
                names_list = prediction['description'].split(', ')[:-1]
                if names_list:
                    hints.append(', '.join(names_list))
        # make hints unique but keep ordering
        hints = list(OrderedDict(list(zip(hints, hints))).keys())
        return hints


class ResolveStreetMixin:
    STREET = 'street'
    STREET_NUMBER = 'street_number'

    GOOGLE_TYPES_MATCH = [
        ('route', STREET),  # virtual region type
        ('point_of_interest', STREET),  # virtual region type
        (STREET_NUMBER, STREET),  # virtual street_number
        ('postal_code', 'zip'),
        ('sublocality_level_1', 'neighborhood'),
        ('sublocality_level_1', 'borough'),
        ('locality', 'city'),
        ('locality', 'village'),
        ('administrative_area_level_2', 'region'),
        ('administrative_area_level_2', 'county'),
        ('administrative_area_level_1', 'state'),
        ('country', 'country'),
    ]
    COLONIAL_COUNTRIES = [
        # US non-state territories
        'PR',  # @US - Puerto Rico
        'VI',  # @US - United States Virgin Islands
        'GU',  # @US - Guam
        'MP',  # @US - Commonwealth of the Northern Mariana Islands
        # GB non-standard islands
        'IM',  # Isle of Man
        'BM',  # Bermudas
        'GG',  # Guernsey Tax Heaven
        'JE',  # Jersey Money Launder
    ]

    @staticmethod
    def resolve_location_google_api(query):
        """
        Resolve location based on google_maps_api
        Return empty list under result key if nothing were found
        Response of google_maps_api:
            https://developers.google.com/maps/documentation/geocoding/start
        :param query: str,
        :return: dict. with keys:
            1) status
            2) results, list of dicts
                Each have keys:
                    - adress_components,
                    - formatted_address,
                    - geometry,
                    - place_id,
                    - types,
                    - ...
        """
        params = {
            'address': query,
            'region': settings.GEOCODER['google']['region'],
            'language': settings.GEOCODER['google']['language'],
            # 'components': settings.GEOCODER['google']['components'],
        }
        if settings.GOOGLE_PLACES_API_KEY:
            params['key'] = settings.GOOGLE_PLACES_API_KEY
        google_response = requests.get(
            'https://maps.googleapis.com/maps/api/geocode/json',
            params=params,
            timeout=5.0,
        )
        try:
            data = google_response.json()
        except ValueError:
            _street_hints_log.exception(
                'response error: status=%r text=%r',
                google_response.status_code,
                google_response.text,
            )
            data = {'results': []}
        if 'error_message' in data:
            _street_hints_log.error(
                'response error: status=%r text=%r',
                google_response.status_code,
                google_response.text,
            )
        return data

    @classmethod
    def get_google_and_blacklisted_types(cls, best_match):
        """
        Return two element tuple.
                1) First tuple matched google region types with Booksy types
                2) Blacklisted region types
        :param best_match: dict. from self.resolve_location_google_api response
        :return: two element tuple.
        """
        google_types_matched = cls.GOOGLE_TYPES_MATCH
        black_listed_types = ['postal_code_prefix']
        if (
            firstof(
                component['short_name']
                for component in best_match['address_components']
                if 'country' in component['types']
            )
            in cls.COLONIAL_COUNTRIES
        ):
            # we treat colonial countries as states within their overlords
            google_types_matched = [
                ('route', 'street'),  # virtual region type
                ('point_of_interest', 'street'),  # virtual region type
                ('postal_code', 'zip'),
                ('sublocality_level_1', 'neighborhood'),
                ('sublocality_level_1', 'borough'),
                ('locality', 'city'),
                ('locality', 'village'),
                ('administrative_area_level_1', 'region'),
                ('administrative_area_level_1', 'county'),
                ('country', 'state'),
            ]
        if any(
            component_type in best_match['types']
            for component_type in [
                'locality',
                'neighborhood',
                'sublocality_level_1',
            ]
        ):
            # #37820 - in Poland Google returns city's main postal code
            # for it's neighborhoods, this is wrong, so we must ignore it
            # in other countries postal code is not present in such cases
            black_listed_types.append('postal_code')

        return google_types_matched, black_listed_types

    @staticmethod
    def get_region_types_and_matched(best_match, google_types, blacklisted):
        """
        Search most appropriate Booksy region type for response from google_api

        :param best_match: dict. from self.resolve_location_google_api response
        :param google_types: mapping Google region types versus Booksy types
        :param blacklisted: blacklisted region types
        :return:two element tuple:
                    1)dict. key is booksy type, and value return from google_api
                    2)string if region type was found None otherwise
        """
        region_names_per_type = {}
        matched_type = None
        street_number = None
        for google_type, booksy_type in google_types:  # order matters
            for component in best_match['address_components']:
                if any(_type in blacklisted for _type in component['types']):
                    # google started sending zipcode prefixes,
                    # which messes things up a lot
                    continue
                if google_type in component['types']:
                    if google_type == ResolveStreetMixin.STREET_NUMBER:
                        # remember street number to add it to
                        # street
                        street_number = component['long_name']
                    else:
                        region_names_per_type[booksy_type] = component['long_name']
                    if matched_type is None:
                        matched_type = booksy_type

        street = region_names_per_type.get(ResolveStreetMixin.STREET)
        # HACK to add street number
        # for streets without it
        if (
            street_number
            and street
            and
            # check that address do not contain street_number
            not street[-1].isdigit()
        ):
            # merge street with street number
            region_names_per_type[ResolveStreetMixin.STREET] = (
                f'{region_names_per_type[ResolveStreetMixin.STREET]}, {street_number}'
            )

        return region_names_per_type, matched_type

    @staticmethod
    def check_formatted_address(best_match, blacklisted):
        """
        Format value under formatted_address of
        best_match depending if this region type is in blacklisted or zipcode

        :param best_match: dict. from self.resolve_location_google_api response
        :param blacklisted: list of blacklisted region types
        :return: string
        """
        address = ', '.join(best_match['formatted_address'].split(', ')[:-1])
        # #37820 - PL formatted address repairer
        # we must remove incorrect zipcode added by Google to PL neighborhoods
        if settings.API_COUNTRY == Country.PL and 'postal_code' in blacklisted:
            bad_zip = firstof(
                component['long_name']
                for component in best_match['address_components']
                if 'postal_code' in component['types']
            )
            if bad_zip:
                address = address.replace(f', {bad_zip} ', ', ')
                address = address.replace(f'{bad_zip} ', '')
        return address


class ResolveGeocoder(ResolveStreetMixin):
    MAX_MATCH_BY_NAME_DISTANCE = 30.0

    # pylint: disable=too-many-branches
    def get_data(self, query):
        from webapps.structure import searchables

        _debug = True

        if _debug:
            self._debug("QUERY: %r", query)

        data = self.resolve_location_google_api(query)

        if not data['results']:
            self.finish(
                {
                    'match': None,
                    'location': None,
                }
            )
            return

        best_match = data['results'][0]

        if _debug:
            google_types = {
                g_type: component['long_name']
                for component in best_match['address_components']
                for g_type in component['types']
            }
            self._debug(
                "GOOGLE RESPONSE:\ncoords: %r,%r\n%r\n",
                best_match['geometry']['location']['lat'],
                best_match['geometry']['location']['lng'],
                '\n'.join(f'{key}: {val}' for key, val in google_types.items()),
            )
        google_types_matched, blacklisted_types = self.get_google_and_blacklisted_types(best_match)

        region_names_per_type, matched_type = self.get_region_types_and_matched(
            best_match, google_types_matched, blacklisted_types
        )

        address = self.check_formatted_address(best_match, blacklisted_types)
        # prepare response object
        response = {
            'match': {
                'type': matched_type,
                'latitude': best_match['geometry']['location']['lat'],
                'longitude': best_match['geometry']['location']['lng'],
                'formatted_address': address,
            },
            'location': None,  # filled later
        }

        # zip codes can be matched exactly, because they are unique
        if 'zip' in region_names_per_type:
            regions = (
                searchables.RegionSearchable(
                    ESDocType.REGION,
                )
                .params(size=1)
                .execute(
                    {
                        'type': ['zip'],
                        'text': region_names_per_type['zip'],
                    }
                )
            )

            if regions:
                response['location'] = regions[0]
                if _debug:
                    self._debug("LOCATION: %r (%r)", regions[0]['full_name'], regions[0]['type'])
                return response

        # look up nearest matching regions
        regions = (
            searchables.RegionInfoSearchable(
                ESDocType.REGION,
            )
            .params(size=100)
            .execute(
                {
                    'type': list(set(region_names_per_type.keys()) - {'street', 'zip'}),
                    'latitude': best_match['geometry']['location']['lat'],
                    'longitude': best_match['geometry']['location']['lng'],
                }
            )
        )

        if _debug:
            self._debug(
                "REGIONS FROM ELASTICSEARCH:\n%r\n",
                '\n'.join(
                    [
                        f'{i} {reg.type}: {reg.full_name} ({reg.meta.sort[0]})'
                        for i, reg in enumerate(regions)
                    ]
                ),
            )

        # pylint: disable=invalid-name
        # match by name starting with lowest region type
        TYPES_ORDERING = ['neigborhood', 'borough', 'city', 'village', 'county', 'region', 'state']
        location = None
        for region_level in TYPES_ORDERING:
            if region_level not in region_names_per_type:
                continue
            # match by name
            slug = slugify(region_names_per_type[region_level])
            for region in regions:
                distance = region.meta.sort[0]
                if distance > self.MAX_MATCH_BY_NAME_DISTANCE:
                    continue
                if slug in (region.slug, slugify(region.full_name)):
                    location = region
                    break
            # propagate break
            if location is not None:
                break

        # fallback to nearest region
        if location is None and regions:
            location = regions[0]

        response['location'] = location
        if _debug:
            self._debug(
                "LOCATION: %r (%r)", (location or {}).get('full_name'), (location or {}).get('type')
            )

        # TODO return format data
        return response

    @staticmethod
    def _debug(message, *args, **kwargs):
        _street_hints_log.debug(message, *args, **kwargs)


class BusinessResolveGeocoder(ResolveStreetMixin):
    MAX_MATCH_BY_NAME_DISTANCE = 30.0

    def get_data(self, query):
        from webapps.structure import searchables

        data = self.resolve_location_google_api(query)
        response = {
            'street': None,
            'city': None,
            'zip': None,
        }

        if not data['results']:
            self.finish(response)
            return

        best_match = data['results'][0]
        google_types_matched, blacklisted_types = self.get_google_and_blacklisted_types(best_match)
        # TODO possible optimization
        # on ordered dict get_region_types_and_matched
        region_names_per_type, _matched_type = self.get_region_types_and_matched(
            best_match,
            google_types_matched,
            blacklisted_types,
        )

        accepted_regions = list(response.keys())
        # fill all possible info
        for region_type, region_name in list(region_names_per_type.items()):
            if region_type in accepted_regions:
                response[region_type] = region_name

        search_types = {}
        # check empty keys
        for key, value in list(response.items()):
            if value is None:
                search_types[key] = False

        if search_types and settings.API_COUNTRY != Country.IE:
            # Ireland doesn't have any regions in the db
            # so do not search it but return what was found
            # from google-api
            regions = (
                searchables.RegionInfoSearchable(
                    ESDocType.REGION,
                )
                .params(size=100)
                .execute(
                    {
                        'type': list(search_types),
                        'latitude': best_match['geometry']['location']['lat'],
                        'longitude': best_match['geometry']['location']['lng'],
                    }
                )
            )

            for region in regions:
                # iterate through 3 elements
                # if all of them True
                # break search by parents regions
                if all(search_types.values()):
                    break
                if region['type'] in accepted_regions:
                    search_types[region['type']] = True
                    response[region['type']] = region['full_name']
        return response
