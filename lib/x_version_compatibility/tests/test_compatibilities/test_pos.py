from unittest import TestCase
from unittest.mock import MagicMock

from parameterized import parameterized

from lib.x_version_compatibility import FilterOutStripeCompatibility
from lib.x_version_compatibility.typing import RequestType
from webapps.booking.models import BookingSources
from webapps.consts import ANDROID, IPHONE, WEB


class TestFilterOutStripeCompatibility(TestCase):
    @parameterized.expand(
        [
            # False <== default value
            (None, None, None, False),
            # False <== default value
            (BookingSources.CUSTOMER_APP, WEB, '1.2.3', False),
            # The same version
            (BookingSources.CUSTOMER_APP, ANDROID, '2.6.1_338', False),
            # Higher version - build
            (BookingSources.CUSTOMER_APP, ANDROID, '2.6.1_339', False),
            # Higher version  - patch
            (BookingSources.CUSTOMER_APP, ANDROID, '2.6.2', False),
            # Lower version - build
            (BookingSources.CUSTOMER_APP, ANDROID, '2.6.1_337', True),
            # Lower version - patch
            (BookingSources.CUSTOMER_APP, ANDROID, '2.6.0', True),
            # The same version
            (BookingSources.CUSTOMER_APP, IPHONE, '2.7.1', False),
            # Higher version - build
            (BookingSources.CUSTOMER_APP, IPHONE, '2.7.1 (3339)', False),
            # Higher version  - patch
            (BookingSources.CUSTOMER_APP, IPHONE, '2.7.2', False),
            # Lower version - patch + build
            (BookingSources.CUSTOMER_APP, IPHONE, '2.7.0 (3337)', True),
            # Lower version - patch
            (BookingSources.CUSTOMER_APP, IPHONE, '2.7.0', True),
        ]
    )
    def test_is_compatible(self, app_type, source_name, x_version, expected_result):
        booking_source = MagicMock()
        booking_source.configure_mock(app_type=app_type, name=source_name)
        request = MagicMock(
            spec=RequestType, headers={'X-Version': x_version}, booking_source=booking_source
        )

        self.assertEqual(FilterOutStripeCompatibility(request), expected_result)
