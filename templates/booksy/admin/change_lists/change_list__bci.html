{% extends "admin/change_list.html" %}
{% load i18n static admin_list admin_urls suit_list %}
{% load search_form_query_fields %}

{% block extrahead %}
  {{ block.super }}
  {{ media.js }}
  {% if action_form %}{% if actions_on_top or actions_on_bottom %}
    <script type="text/javascript">
      (function ($) {
        $(document).ready(function ($) {
          $("tr input.action-select").actions();
          $('select[name="action"]').on("change", function (ev) {
              if (ev.target.value == 'invite_bci') {
                  $('#invite_params').show();
              } else {
                  $('#invite_params').hide();
              }
          });
        });
      })(django.jQuery);
    </script>
  {% endif %}{% endif %}
{% endblock %}

{% block actions_on_bottom_block %}
{% admin_actions %}
<div id="invite_params" style="display:none; margin-top: 10px">
  <label>Report email (optional): </label>
  <input type="email" name="report_email">
</div>
{% endblock %}
