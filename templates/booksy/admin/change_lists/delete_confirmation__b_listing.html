{% extends "admin/base_site.html" %}
{% load admin_urls %}
{% load trans from i18n %}
{% load blocktrans from i18n %}

{% block breadcrumbs %}
  <ul class="breadcrumb">
    <li><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
      <span class="divider">&raquo;</span></li>
    <li>
      <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{% firstof opts.app_config.verbose_name app_label|capfirst|escape %}</a>
      <span class="divider">&raquo;</span></li>
    <li>
      <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst|escape }}</a>
      <span class="divider">&raquo;</span></li>
    <li><a href="
        {% url opts|admin_urlname:'changelist' %}{{ object.pk }}">{{ object|truncatewords:"18" }}</a>
      <span class="divider">&raquo;</span></li>
    <li class="active">{% trans 'Delete' %}</li>
  </ul>
{% endblock %}

{% block content %}
  {% if perms_lacking or protected %}
    {% if perms_lacking %}
      <div class="alert alert-error">
        {% blocktrans with escaped_object=object %}Deleting the {{ object_name }} '{{ escaped_object }}' would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:{% endblocktrans %}
        <ul>
          {% for obj in perms_lacking %}
            <li>{{ obj }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
    {% if protected %}
      <div class="alert alert-error">
        {% blocktrans with escaped_object=object %}Deleting the {{ object_name }} '{{ escaped_object }}' would require deleting the following protected related objects:{% endblocktrans %}
        <ul>
          {% for obj in protected %}
            <li>{{ obj }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
  {% else %}

    {% block delete_form %}
      <form action="" method="post">{% csrf_token %}
        <div class="alert alert-block alert-error">
          <h4 class="alert-heading">{% trans 'Delete' %} {% trans opts.verbose_name %}?</h4>
          <p>
            {% blocktrans with escaped_object=object %}Are you sure you want to delete the {{ object_name }} "{{ escaped_object }}"?{% endblocktrans %}</p>
          <br>

          <div>
            {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1" />{% endif %}
            {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}" />{% endif %}
            <input type="hidden" name="post" value="yes"/>
            <input type="submit" value="{% trans "Yes, I'm sure" %}" class="btn btn-danger"/>
          </div>
        </div>
      </form>
    {% endblock %}
  {% endif %}
{% endblock %}
