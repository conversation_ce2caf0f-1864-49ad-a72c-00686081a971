{% extends "mail_base.html" %}

{% block extratitle %}{{ _('Password Change Request') }}{% endblock extratitle %}
{% block body_content %}
    <p style="font: bold 16px/30px Arial, Helvetica, sans-serif;">
        {{ _('Hi, %(name)s!')|format(name=user.first_name) }}
    </p>
    <p>
        {{ _('We’ve received a request from you to change your password on Booksy.') }}
    </p>
    <p>
        {{ _('Click here to reset your Booksy password.') }}<br/><br/>
        <a style="padding: 0 30px; background: #FFC309; color: #4B3A04; font: bold 16px/30px Arial, Helvetica, sans-serif; display: inline-block; cursor: pointer; text-decoration: none; box-shadow: 0 1px 3px #999;" mc:disable-tracking
           href="{{ user.get_password_reset_url(BOOKING_SOURCE) }}">
            &nbsp;&nbsp;{{ _('Reset Password') }}&nbsp;&nbsp;
        </a>
        <br/><br/>
        {{ _('The link will expire in 72 hours.') }}
    </p>
    <p>
        {{ _('Links not working? Paste this into your browser:') }}<br/>
        <b>{{ user.get_password_reset_url(BOOKING_SOURCE) }}</b>
    </p>
{% endblock body_content %}
