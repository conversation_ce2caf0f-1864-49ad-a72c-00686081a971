syntax = "proto3";

package booksy_auth.interface;

service BooksyAuth {
  rpc session_exists(SessionExistsRequest) returns (SessionExistsResponse) {}
}

message SessionExistsRequest {
  string session_key = 1;
  string country_code = 2;
  string domain = 3;
}

message SessionExistsResponse {
  bool session_exists = 1;
  uint32 country_user_id = 2;
  string user_id = 3;
  string expired = 4;
  string origin = 6;
  string superuser_email = 7;
}
