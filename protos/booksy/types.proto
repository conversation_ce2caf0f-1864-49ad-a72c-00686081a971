syntax = "proto3";

package booksy.types;

import "google/protobuf/descriptor.proto";


extend google.protobuf.MessageOptions {
  optional string history_model = 50001;
  optional int64 required_number = 50002;
}

message ServicePrice_deprecated {
  string value = 1;
  string price_type = 2;
  string discount = 3;
}

message ServicePrice {
  // minor unit price
  int32 value = 1;
  string price_type = 2;
  // minor unit price
  int32 discount = 3;
}

/*
   DispatchContext and HistoryContext are fields to be used in History Messages.
   They must be used in every Message Type used for History Project
   with the number declared as `required_number`.
  
   Every History Message must declare `history_model` option as well.
  
     message SomeHistoryMessage {
       option (booksy.types.history_model) = 'my_model';
       ...
       DispatchContext dispatch_context = 2000;
       HistoryContext history_context = 2001;
     }
 */

message DispatchContext {
  option (required_number) = 2000;

  // operator
  string operator_type = 1;
  uint32 user_id = 2;
  string user_email = 3;
  string superuser_email = 4;
  string partner_uuid = 5;
  string partner_name = 6;

  // request metadata
  string forwarded_ip = 7;
  string fingerprint = 8;
  string uagent = 9;
  string x_version = 10;
  string referer = 11;

  //
  string endpoint = 12;
  string booking_source = 13;

  string task_id = 14;
  string task_name = 15;

  // other json
  string extra_data = 16;
}


message HistoryContext {
  option (required_number) = 2001;

  string country_code = 1;
  uint32 business_id = 2;
  int64 model_id = 3;
  int64 event_time = 4;
  string event_type = 5;
}
