var jsdomify = require('jsdomify').default;
var config = require('./config').default;


// // mock dom api
jsdomify.create();

var adyen = require('adyen-cse-web');

var options = {}
var card = config.card
card.generationtime = new Date().toISOString();

var instance = adyen.createEncryption(config.key, options);
var data = instance.encrypt(config.card);

console.log('encrypted_data = "' + data + '"')


