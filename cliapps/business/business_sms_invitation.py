#!/usr/bin/env python
import argparse

import django
from service.exceptions import ServiceError

django.setup()  # noqa

from lib.invite import invite_customer
from webapps.business.models import Business


def invite_customers_for_business(business_id):
    business = Business.objects.get(id=business_id)
    customers = business.business_customer_infos.all()
    count = customers.count()
    for i, bci in enumerate(customers.iterator(), 1):
        phone = bci.cell_phone

        if not phone and bci.user:
            phone = bci.user.cell_phone
        try:
            ret = bci.invite(sms_only=True)
            print(ret)
        except ServiceError:
            print(f'sending failed biz={business} tel={phone}')
        print(phone, i, '/', count)
    print("DONE")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--business_id', dest='business_id', required=True)
    args = parser.parse_args()
    invite_customers_for_business(args.business_id)
