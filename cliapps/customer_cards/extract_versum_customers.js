/*
Tool speeding up data extraction from versum.com
Paste below script to developer console and copy extracted data
*/

var result = [];
$('.customer-row').each(
    function() {
        result.push([
            $(this).find('.row-title').text().replace('\n', ' ').trim(),
            $(this).find('td:nth-child(2) div.icon_box').attr('data-original-title').replace('wyślij email:', '').replace('nie podano', '').trim(),
            $(this).find('.versum-phone-button').text().replace('\n', ' ').trim(),
            $(this).find('td:nth-child(3) span').text().replace('\n', ' ').trim(),
        ].join(','))
    }
);
console.log(result.join('\n'));
