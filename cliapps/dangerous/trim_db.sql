BEGIN;

DROP TABLE IF EXISTS google_region_import;
DROP TABLE IF EXISTS region_import;
DROP TABLE IF EXISTS booking_booking_report;
DROP TABLE IF EXISTS missing_pl;

TRUNCATE business_business CASCADE;
TRUNCATE booking_appointment CASCADE;
TRUNCATE booking_subbooking CASCADE;
TRUNCATE booking_repeatingbooking CASCADE;

TRUNCATE registrationcode_registrationcode CASCADE;
TRUNCATE business_appsflyer;
TRUNCATE segment_segmentcounter;
TRUNCATE segment_achievedevent;
TRUNCATE user_unsubscribedemail;

TRUNCATE donation_donationrewardcode;
DELETE FROM donation_donation WHERE pos_id !=1;

TRUNCATE
    purchase_subscription,
    purchase_subscriptiondiscount,
    purchase_subscriptionhistory,
    purchase_subscriptiontransaction,
    purchase_invoice CASCADE;

TRUNCATE pos_transaction CASCADE;
TRUNCATE register_register CASCADE;
TRUNCATE pos_poschangelog;
TRUNCATE pos_bankaccount CASCADE;
TRUNCATE voucher_voucheradditionalinfo CASCADE;

DELETE FROM pos_taxrate  WHERE pos_id !=1;
DELETE FROM pos_paymenttype  WHERE pos_id !=1;
DELETE FROM pos_tip  WHERE pos_id !=1;
DELETE FROM pos_commissiondefaults WHERE pos_id != 1;
DELETE FROM pos_productcategory WHERE pos_id != 1;
DELETE FROM voucher_voucher WHERE pos_id != 1;
DELETE FROM voucher_vouchertemplate WHERE pos_id != 1;
DELETE FROM pos_pos_pos_plans WHERE pos_id != 1;
DELETE FROM pos_pos WHERE pos_id != 1;
TRUNCATE auth_user CASCADE;
TRUNCATE user_user CASCADE;
TRUNCATE subdomain_subdomain;

-- rm sessions
TRUNCATE user_usersessioncache;
TRUNCATE django_session CASCADE;

-- rm notifications
TRUNCATE notification_reciever CASCADE;
TRUNCATE notification_notificationschedule;
TRUNCATE notification_notificationhistory;
TRUNCATE notification_notificationsmscodes;

-- rm transactions and payment methods
TRUNCATE pos_paymentmethod;
TRUNCATE pos_bankaccount CASCADE;
TRUNCATE dwolla_customer CASCADE;
TRUNCATE adyen_cardholder CASCADE;
TRUNCATE adyen_notification;
TRUNCATE adyen_adyenrequestlog;

-- other
TRUNCATE experiment_experimentevent;
TRUNCATE experiment_v3_experiment CASCADE; 
TRUNCATE experiment_v3_experimentslot CASCADE;  
TRUNCATE marketing_archivedblast;
TRUNCATE feedback_feedback;
TRUNCATE booking_bookingchange;
TRUNCATE business_delayedbookmarkevent;
TRUNCATE user_emailtoken;
TRUNCATE urlinator_urlinator;
TRUNCATE elasticsearch_businessavailability;
TRUNCATE market_pay_marketpaynotification;
TRUNCATE market_pay_marketplaceaccounting;
TRUNCATE admin_extra_superuserloginentry;

-- used to login in admin
TRUNCATE socialaccount_socialapp CASCADE;
TRUNCATE socialaccount_socialaccount CASCADE;
DELETE FROM django_migrations 
WHERE app='allauth_google' AND name='0001_initial';
COMMIT;
