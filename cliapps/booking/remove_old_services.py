import argparse
import django
from tqdm import tqdm

django.setup()  # noqa

# pylint: disable=wrong-import-position

from webapps.business.models import Service, ServiceVariant


# pylint: enable=wrong-import-position
def main():
    parser = argparse.ArgumentParser(
        description="""
                Script for removing old services for business. Services that are not active
                and have only non-active service variants without booking transactions and vouchers
                connected will be removed. Service variants are removed with services.
            """
    )
    parser.add_argument('--dry-run', action='store_true')
    parser.add_argument(
        '--business_id',
        type=int,
        required=True,
        help='id of the business that should be have old services removed',
    )
    args = parser.parse_args()
    clear_old_business_ids(business_id=args.business_id, dry_run=args.dry_run)


def clear_old_business_ids(business_id: int, dry_run: bool = False):
    inactive_services = Service.objects.filter(business_id=business_id, active=False)

    services_to_remove_cnt, variants_to_remove_cnt = 0, 0
    services_removed_cnt, variants_removed_cnt = 0, 0

    for service in tqdm(inactive_services):
        can_be_safely_deleted = True

        for variant in service.service_variants.all():
            if variant.active or not variant.can_be_safely_deleted():
                can_be_safely_deleted = False
            else:
                variants_to_remove_cnt += 1
                if not dry_run:
                    variants_removed_cnt += int(remove_variant(variant))
        if can_be_safely_deleted:
            services_to_remove_cnt += 1
            if not dry_run:
                services_removed_cnt += int(remove_service(service))

    print(
        f"{services_to_remove_cnt} services and {variants_to_remove_cnt}"
        f" service variants should be removed"
    )
    if not dry_run:
        print(
            f"{services_removed_cnt} services and {variants_removed_cnt}"
            f" service variants were removed"
        )


def remove_service(service: Service) -> bool:
    service.safe_delete()
    return bool(service.deleted)


def remove_variant(variant: ServiceVariant) -> bool:
    variant.safe_delete()
    return bool(variant.deleted)


if __name__ == '__main__':
    main()
