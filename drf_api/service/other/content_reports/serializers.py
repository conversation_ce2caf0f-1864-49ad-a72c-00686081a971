from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from drf_api.base_serializers import CustomValidationErrorSerializer
from service.other.enums import (
    InappropriateContentReason,
    InappropriateContentReasonDSA,
    InappropriateObject,
)


class ReportInappropriateContentSerializer(CustomValidationErrorSerializer):
    id = serializers.IntegerField(help_text='Primary key of object')
    object_type = serializers.ChoiceField(choices=InappropriateObject.choices())
    reason = serializers.ChoiceField(
        choices=InappropriateContentReason.choices(),
        help_text='Why user find content inappropriate',
    )


class ReportInappropriateContentDSASerializer(CustomValidationErrorSerializer):
    reported_object_id = serializers.IntegerField(help_text='Primary key of object')
    object_type = serializers.ChoiceField(choices=InappropriateObject.choices())
    reason = serializers.ChoiceField(
        choices=InappropriateContentReasonDSA.choices(),
        help_text='Why user find content inappropriate',
    )
    additional_info = serializers.CharField(max_length=512)
    is_trusted_flagger = serializers.BooleanField(required=False)
    first_name = serializers.CharField(required=False, allow_blank=True)
    second_name = serializers.CharField(required=False, allow_blank=True)
    entity_name = serializers.CharField(required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)

    def validate(self, attrs):
        super().validate(attrs)
        children_sexual = trusted_flagger = True
        required_fields = {
            (not children_sexual, not trusted_flagger): ('first_name', 'second_name', 'email'),
            (not children_sexual, trusted_flagger): ('entity_name', 'email'),
            (children_sexual, trusted_flagger): ('entity_name',),
            (children_sexual, not trusted_flagger): (),
        }[
            attrs['reason'] == InappropriateContentReasonDSA.CHILDREN_SEXUAL,
            attrs.pop('is_trusted_flagger', False),
        ]

        for field in required_fields:
            if not attrs.get(field):
                raise serializers.ValidationError({field: _('This field is required')})

        return attrs
