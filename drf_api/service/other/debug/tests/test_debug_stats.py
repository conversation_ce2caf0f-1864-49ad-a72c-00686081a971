from django.shortcuts import reverse

from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase

from webapps.booking.models import BookingSources


class TestDebugStats(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.url = reverse('debug-stats')
        cls.source = baker.make(BookingSources, name='PerformanceTest')
        cls.headers = {'HTTP_X_API_KEY': cls.source.api_key}

    def test_debug_stats_kill_switches_not_shown(self):
        response = self.client.get(self.url, **self.headers)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertNotIn(b'kill_switches', response.content)

    def test_debug_stats_kill_switches_shown(self):
        data = {"kill_switches": 1}
        response = self.client.get(self.url, data, **self.headers)
        self.assertIn(b'kill_switches', response.content)

    def test_debug_stats_kill_switches_replica_data_shown(self):
        data = {"kill_switches": 1, "use_replica": 1}
        response = self.client.get(self.url, data, **self.headers)
        self.assertIn(b'kill_switches', response.content)
