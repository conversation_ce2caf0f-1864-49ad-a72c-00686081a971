from rest_framework import serializers

from lib.tools import tznow, get_object_or_404
from service.exceptions import ServiceError
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.marketing.unsubscribe import UnsubscribedService
from webapps.marketing.utils import unsubscribe_token_decode
from webapps.segment.tasks import analytics_contact_preferences_updated_task
from webapps.user.models import EmailToken, UnsubscribedEmail, User


class UnsubscribeEmailSerializer(serializers.Serializer):
    token = serializers.CharField(max_length=45, write_only=True)

    def validate_token(self, token):
        email_token = EmailToken.objects.filter(
            token=token,
            expiry_date__gt=tznow(),
        ).first()
        if email_token is None:
            raise serializers.ValidationError('Token is invalid')
        self.context['email_token'] = email_token
        return token

    def save(self, **kwargs):
        email_token = self.context['email_token']
        UnsubscribedEmail.objects.get_or_create(email=email_token.email)
        user = User.objects.filter(email=email_token.email).values('id').first()
        if user:
            analytics_contact_preferences_updated_task.delay(
                user_id=user.get('id'),
                context={
                    'session_user_id': user.get('id'),
                    'source_id': kwargs['booking_source'].id,
                },
            )


class UnsubscribeBlastsSerializer(serializers.Serializer):
    token = serializers.CharField(write_only=True)

    def validate_token(self, token):
        try:
            bci_id = unsubscribe_token_decode(token)
            self.context['bci_id'] = bci_id
        except ValueError:
            raise ServiceError(errors=['Invalid token'])  # pylint: disable=raise-missing-from
        return token

    def save(self, **kwargs):
        bci_id = self.context['bci_id']
        bci = get_object_or_404(BusinessCustomerInfo, id=bci_id)
        UnsubscribedService.unsubscribe_bci_and_duplicates(bci)
