from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.response import Response

from drf_api.base_views import BaseBooksyNoSessionApiView
from drf_api.service.utils.serializers import BooksyPasswordSerializer
from service.mixins.validation import validate_serializer


class CheckBooksyPasswordApiView(BaseBooksyNoSessionApiView):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    API_KEY_REQUIRED = False

    @staticmethod
    def get_serializer(*args, **kwargs):
        return BooksyPasswordSerializer(*args, **kwargs)

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        validate_serializer(serializer)
        return Response(status=status.HTTP_200_OK, data=serializer.data)
