import logging
from typing import Optional

from bo_obs.datadog import set_apm_tag_in_current_span
from django.conf import settings
from django.contrib.sessions.backends.base import UpdateError
from django.utils.functional import cached_property
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.status import HTTP_403_FORBIDDEN

from drf_api.service.business.validators.access import get_business_view_validator
from lib.dispatch_context import dispatch_context
from lib.feature_flag.feature.customer import BooksyAuthTempExtraMetadataFlag
from lib.tools import get_meta_data_from_handler
from service.mixins.throttling import get_django_user_ip
from service.tools import InsertUserProfile, ServiceError
from webapps.booking.adapters import is_internal_booking_source
from webapps.booking.models import BookingSources
from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Resource, Business
from webapps.consts import WEB, FRONTDESK_SOURCES, <PERSON><PERSON><PERSON><PERSON>_CUSTOMER
from webapps.kill_switch.models import <PERSON><PERSON><PERSON>
from webapps.session.ports import SessionMediator
from webapps.user.models import UserProfile

user_log = logging.getLogger('booksy.user_log')

sessions_logger = logging.getLogger('booksy.sessions')

request_log = logging.getLogger('request')

logger_account = logging.getLogger('booksy.account')


class BooksyViewSessionMixin:
    """
    Class to mix with DRF View subclasses. Should be inherited before View
    subclasses.
    It inject session with session_key from X-ACCESS-TOKEN to DRF Request
    object. After executing handler method if conditions are met, session is
    saved/bumped.
    """

    def _is_new_login_turn_on(self):
        return KillSwitch.exist_and_alive(KillSwitch.System.BOOKSY_AUTH)

    def initial(self, request, *args, **kwargs):
        # pylint: disable=protected-access
        access_token = request._request.META.get('HTTP_X_ACCESS_TOKEN')
        session = None
        if access_token is not None:
            _session = SessionMediator(
                session_key=access_token,
                new_login_turned_on=self._is_new_login_turn_on(),
            ).get_session()
            if _session.keys():  # Session is filled when created by user
                session = _session
            else:
                sessions_logger.debug(
                    'Invalid Token: %s, used by %s.',
                    access_token,
                    get_django_user_ip(request),
                )
            if BooksyAuthTempExtraMetadataFlag():
                set_apm_tag_in_current_span('debug_request_url', request.build_absolute_uri())
        request.session = session
        super().initial(request, *args, **kwargs)

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if getattr(self.request, 'session', None) is not None and self.session_qualified_to_bump():
            self.request.session[
                'session_expire_date'
            ] = self.request.session.get_expiry_date().strftime(
                settings.ES_DATETIME_FORMAT,
            )
            try:
                self.request.session.save()
            except UpdateError:
                request_log.warning("Session wrapper raised an exception", exc_info=True)
        return response

    def session_flushed(self):
        return not self.request.session.exists(
            self.request.session._session_key  # pylint: disable=protected-access
        )

    def session_qualified_to_bump(self):
        # TODO booksy_auth move SESSION_BUMP_DISABLED feature to booksy_auth
        return (
            not self.request.method in getattr(self, 'SESSION_BUMP_DISABLED_FOR', [])
            and (
                self.request.session.modified
                or settings.SESSION_SAVE_EVERY_REQUEST
                or self.request.session.bump_session()
            )
            and not self.session_flushed()
        )


class BookingSourceMixin:
    """
    Mixin to be used in drf views. It validate BookingSource rules during initialization of view .
    It provide property of api_key and booking source in view instance. To use it call:
        self.api_key
        self.booking_source
    """

    API_KEY_REQUIRED = True
    SOURCE_NAME = None
    CUSTOMER_APP_SUBSTRING = '/customer_api/'
    BUSINESS_APP_SUBSTRINGS = ['/business_api/', '/printer_api/']
    INTERNAL_APP_SUBSTRING = '/internal/'

    @cached_property
    def api_key(self):
        return self.request.META.get('HTTP_X_API_KEY')

    @cached_property
    def booking_source(self) -> BookingSources | None:
        source = None
        if self.api_key is not None:
            source = BookingSources.get_cached(api_key=self.api_key, name=self.SOURCE_NAME)
        self.request.booking_source = source
        return source

    @cached_property
    def internal_booking_source(self):
        return is_internal_booking_source(self.booking_source)

    def validate_booking_source(self):
        if self.API_KEY_REQUIRED and self.api_key is None:
            self._raise_booking_source_error()
        if self.api_key is not None and self.booking_source is None:
            # api_key was not valid, no booking_source found
            self._raise_booking_source_error()
        if (
            self.booking_source is not None
            and self.booking_source.app_type is not BookingSources.UNIVERSAL_APP
        ):
            expected_app_type = None
            if self.CUSTOMER_APP_SUBSTRING in self.request.path:
                expected_app_type = BookingSources.CUSTOMER_APP
            elif any(s in self.request.path for s in self.BUSINESS_APP_SUBSTRINGS):
                expected_app_type = BookingSources.BUSINESS_APP
            elif self.INTERNAL_APP_SUBSTRING in self.request.path:
                expected_app_type = BookingSources.INTERNAL_APP
            if expected_app_type is not None and self.booking_source.app_type != expected_app_type:
                self._raise_booking_source_error()

    @staticmethod
    def _raise_booking_source_error():
        raise ServiceError(
            HTTP_403_FORBIDDEN,
            errors=[
                {
                    'code': 'invalid',
                    'field': 'api_key',
                    'description': _('Invalid api key.'),
                },
            ],
        )

    def initial(self, request, *args, **kwargs):
        """Validation for BookingSource work only in drf views"""
        self.validate_booking_source()
        dispatch_context.booking_source = self.booking_source
        super().initial(request, *args, **kwargs)

    @property
    def is_customer_web(self):
        return (
            self.booking_source.app_type == BookingSources.CUSTOMER_APP
            and self.booking_source.name == WEB
        )

    @property
    def is_customer_mobile(self):
        return self.booking_source.name in MOBILE_CUSTOMER

    @property
    def is_frontdesk(self):
        return self.booking_source.name in FRONTDESK_SOURCES


class ViewPropertyMixin:
    @property
    def user_agent(self):
        return self.request.META.get('HTTP_USER_AGENT')

    @property
    def fingerprint(self):
        return self.request.META.get('HTTP_X_FINGERPRINT')

    @property
    def user_pseudo_id(self):
        return self.request.META.get('HTTP_X_USER_PSEUDO_ID')

    @property
    def access_token(self):
        return self.request.META.get('HTTP_X_ACCESS_TOKEN')

    @property
    def language(self):
        return getattr(self.request._request, 'LANGUAGE_CODE')  # pylint: disable=protected-access

    @property
    def user(self):
        """
        In tornado api user was accessed from view instance.
        Added access for user from request in view instance.
        """
        return self.request.user


class InsertUserProfileMixin(InsertUserProfile):
    """DRF view mixin which insert proper profile of user into user instance in view."""

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        if request.user.is_authenticated:
            self.inject_user_profile()


class ProfileTypePathMixin:
    DEFAULT_PROFILE_TYPE = UserProfile.Type.CUSTOMER
    FORCE_PROFILE_TYPE = None

    def initial(self, request, *args, **kwargs):
        self.profile_type = (
            self.FORCE_PROFILE_TYPE
            or self._get_profile_type_from_path(request)
            or self.DEFAULT_PROFILE_TYPE
        )
        super().initial(request, *args, **kwargs)

    @staticmethod
    def _get_profile_type_from_path(request):  # pylint: disable=no-else-return
        profile_type = None
        if '/customer_api/' in request.path:
            profile_type = UserProfile.Type.CUSTOMER
        elif '/business_api/' in request.path:
            profile_type = UserProfile.Type.BUSINESS
        return profile_type


class QuerySerializerMixin:
    query_serializer_class: serializers.Serializer = None

    def get_query_serializer(self, *args, **kwargs):
        if self.query_serializer_class is None:
            # pylint: disable=broad-exception-raised
            raise RuntimeError('Query Serializer not defined')
        kwargs.setdefault('context', self.get_serializer_context())  # noqa
        return self.query_serializer_class(*args, **kwargs)  # noqa pylint: disable=not-callable


class ResponseSerializerMixin:
    """Allows to use different serializer for output serializing"""

    response_serializer_class: Optional[serializers.Serializer] = None

    def get_response_serializer_class(self):
        """
        Look for serializer class in `self.response_serializer_class`, which
        should be a serializer class,
        i.e.:

        class MyViewSet(ResponseSerializerMixin, ViewSet):
            serializer_class = MyDefaultSerializer  # optional
            response_serializer_class = MyDefaultResponseSerializer

        If `self.response_serializer_class` is not defined, it falls back to
        `self.get_serializer_class`.
        """
        if self.response_serializer_class is not None:
            return self.response_serializer_class
        return self.get_serializer_class()  # noqa

    def get_response_serializer(self, *args, **kwargs):
        """
        Return the serializer instance that should be used for serializing output.
        """
        serializer_class = self.get_response_serializer_class()
        kwargs.setdefault('context', self.get_serializer_context())  # noqa
        return serializer_class(*args, **kwargs)


class BusinessViewValidatorMixin:
    business_lookup_kwargs: str = 'business_pk'
    required_minimum_access_level: StaffAccessLevels = Resource.STAFF_ACCESS_LEVEL_OWNER

    def get_fetcher(
        self,
        business_id: int | None = None,
        required_minimum_access_level: StaffAccessLevels | None = None,
        **kwargs,
    ):
        business_id = business_id or self.kwargs[self.business_lookup_kwargs]
        required_minimum_access_level = (
            required_minimum_access_level or self.required_minimum_access_level
        )

        validator = get_business_view_validator(
            business=business_id,
            request=self.request,
            required_minimum_access_level=required_minimum_access_level,
            user=self.request.user,
            **kwargs,
        )
        validator.validate()
        return validator.fetcher

    def get_business(
        self,
        business_id: int | None = None,
        required_minimum_access_level: StaffAccessLevels | None = None,
        **kwargs,
    ) -> Business:
        return self.get_fetcher(business_id, required_minimum_access_level, **kwargs).business


class ExtendedLogsMixin:

    def _log_base(self, phrase: str, extra_phrase: str = '', extra_kwargs: dict | None = None):
        data = self.request.data
        login_data = (
            f'LoginData: '
            f'EMAIL: {data.get("email")}, '
            f'FINGERPRINT: {self.fingerprint}, '
            f'PROFILE_TYPE: {self.profile_type}'
        )
        if cell_phone := data.get('cell_phone'):
            login_data += f', PHONE: {cell_phone}'
        extra = {
            'request_url': self.request.get_full_path(),
            'country_code': settings.API_COUNTRY,
            'metadata': get_meta_data_from_handler(self),
            'login_data': login_data,
        }
        if extra_kwargs:
            extra.update(extra_kwargs)
        logger_account.warning(msg=f'[LOGIN - {phrase}] {extra_phrase}', extra=extra)

    def log_sign_in_success(self, user_id: int, **kwargs):
        self._log_base(
            'LOGIN SUCCESS',
            f'User {user_id} logged in',
            extra_kwargs={'user_id': user_id} | kwargs,
        )

    def log_sign_in_attempt(self):
        self._log_base('ATTEMPT')

    def log_sign_in_attempt_bad_login_data(self):
        self._log_base('BAD LOGIN DATA')

    def log_no_account(self):
        self._log_base('NO ACCOUNT')

    def log_user_inactive(self):
        self._log_base('USER INACTIVE')

    def log_user_connect_with_attempt(self, user_id: int, connect_with: str):
        self._log_base('CONNECT ATTEMPT', f'User {user_id} attempt to connect with {connect_with}')

    def log_user_connect_with_success(self, user_id: int, connect_with: str):
        self._log_base('CONNECT SUCCESS', f'User {user_id} connected with {connect_with}')


class MultiSerializerViewSetMixin(ResponseSerializerMixin):
    def get_serializer_class(self):
        """
        Look for serializer class in `self.serializer_action_classes`, which
        should be a dict mapping action name (key) to serializer class (value),
        i.e.:

        class MyViewSet(MultiSerializerViewSetMixin, ViewSet):
            serializer_class = MyDefaultSerializer  # optional
            serializer_action_classes = {
               'list': ListSerializer,
               'churn': ChurnSerializer,
            }
        """

        try:
            return self.serializer_action_classes[self.action]
        except (KeyError, AttributeError):
            return super().get_serializer_class()

    def get_response_serializer_class(self):
        """
        Look for serializer class in `self.response_serializer_action_classes`, which
        should be a dict mapping action name (key) to serializer class (value),
        i.e.:

        class MyViewSet(MultiSerializerViewSetMixin, ViewSet):
            serializer_class = MyDefaultSerializer  # optional
            response_serializer_action_classes = {
               'list': ListResponseSerializer,
               'churn': ChurnResponseSerializer,
            }

        If `self.response_serializer_action_classes` is not defined, it falls back to
        `self.get_serializer_class`.
        """

        try:
            return self.response_serializer_action_classes[self.action]
        except (KeyError, AttributeError):
            return self.get_serializer_class()
